name: teemo_coca
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.5 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_screenutil: ^5.9.0
  event_bus: ^2.0.0
  get: ^4.7.2
  connectivity_plus: ^5.0.2
  shared_preferences: ^2.1.2
  device_info_plus: ^10.0.0
  package_info_plus: ^8.3.0
  uuid: ^3.0.5
  crypto: ^3.0.1
  # geolocator: ^12.0.0
  # geolocator: ^11.1.0
  # geolocator: ^9.0.0
  url_launcher: ^6.2.0
  dio: ^5.5.0+1
  sqflite: ^2.3.0
  sqflite_common_ffi_web: ^0.4.0
  intl: ^0.20.2
  image_picker: ^1.0.7 
  cupertino_icons: ^1.0.8
  path_provider: ^2.1.0
  flutter_keyboard_visibility: ^6.0.0
  flutter_native_splash: ^2.3.2
  google_maps_flutter: ^2.10.0
  mop: 2.48.9
  # mop: ^2.47.7
  carousel_slider: ^5.1.1
  json_annotation: ^4.9.0
  flutter_staggered_animations: ^1.1.1
  local_auth: ^2.3.0
  fl_chart: ^0.66.0
  lottie: ^3.1.3
  home_widget: ^0.8.0
  jpush_flutter: 3.3.3

  # # 添加快速操作支持
  # quick_actions: ^1.0.7
  # # 添加应用快捷方式支持
  # app_shortcuts: ^0.1.0
  
  # flutter_statusbarcolor_ns: ^0.5.0
  # finclip_flutter_plugin:
  #   path: ../../MyPlugin/finclip_flutter_plugin 

  # flutter_camera_stitching:
  #   path: /Users/<USER>/Documents/Project/Flutter/MyPlugin/flutter_camera_stitching


dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.14.3
  flutter_native_splash: ^2.3.2
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec


#app图标配置 修改配置执行命令 flutter pub run flutter_launcher_icons
flutter_launcher_icons:
  android: "icon_sfa"
  image_path: "assets/icon.png"
  ios: true
  remove_alpha_ios: true
  image_path_ios: "assets/icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  
#闪屏页配置 修改配置执行命令 flutter pub run flutter_native_splash:create
flutter_native_splash:
  color: "#5DBEF2"
  image: assets/splash.png
  android: true
  ios: true
  android_12:
    image: assets/splash.png
    icon_background_color: "#5DBEF2"


# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
    uses-material-design: true
    assets:
        - assets/images/red/
        - assets/images/ebest/
        - assets/minp/
        - assets/lottie/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
