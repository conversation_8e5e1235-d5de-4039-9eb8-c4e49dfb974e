import 'dart:async';
import 'dart:collection';
import 'dart:async';
import 'package:dio/dio.dart';
import 'package:get/get.dart'; 
import 'sfa_event_bus/event_bus_s.dart';
import 'sfa_event_bus/sfa_event_bus.dart';

class HttpHelper {
  static final limiterInterceptor = DioLimiterInterceptor(maxConcurrent: 3);
  HttpHelper(){
    _addLimiterInterceptor();
  }

  static final dio = Dio(
    /// 忽略证书
    BaseOptions(
      /// 连接服务器超时时间.
      connectTimeout: const Duration(seconds: 1000 * 150),
      /// 两次数据流数据接收的最长间隔时间，注意不是请求的最长接收时间。
      receiveTimeout: const Duration(seconds: 1000 * 150),
      sendTimeout: const Duration(seconds: 1000 * 150),
      /// [Dio] 会自动编码请求体。
      contentType: Headers.jsonContentType,
      /// 忽略证书
      validateStatus: (status) => true
    ),
  ); 
  void _addLimiterInterceptor() {
    dio.interceptors.add(limiterInterceptor);
  }

  /// get请求方法
  static Future get<T>(apiPath, Options? options,
      {Function(int, int)? onReceiveProgress}) async {
    // print("get:$apiPath");
    try {
      var response = await dio.getUri<T>(Uri.parse(apiPath),
          options: options, onReceiveProgress: onReceiveProgress);
      if (response.statusCode == 200) { 
          return response.data;
      } else {
        throw Exception("http post request failed ex:$response $apiPath");
      } 
    } on DioException catch (ex) {
      if(ex.response!=null){
          if(ex.response!.statusCode==401){
            eventBus.fire(EventBus401());
          }
      }
      String exMessage = getConnectionExpMsg(ex.type);
      throw Exception("$exMessage -- ${ex.toString()}");
    } on Exception catch (ex) {
      throw Exception("$ex -- ${ex.toString()}");
    }
  }

  ///post请求
  static Future post<T>(String apiPath, {Object? data, Options? options}) async {
    // dio.interceptors.add(LogInterceptor(responseBody: true));
   
    try {
      var response = await dio.postUri(
        Uri.parse(apiPath),
        data: data,
        options: options,
      ); 
      if ([200,201,400].contains(response.statusCode)) { 
          return response.data;
      } else { 
        //  print(response);
        throw Exception(response);
      }
    } on DioException catch (ex) { 
      if(ex.response!=null){
          if(ex.response!.statusCode==401){
            eventBus.fire(EventBus401());
          }
      }
      String exMessage = getConnectionExpMsg(ex.type);
      throw Exception(exMessage);
    } on Exception catch (ex) { 
      throw Exception("$ex");
    }
  }

  static String getConnectionExpMsg(DioExceptionType type) {
    String exMessage = "";
    switch (type) {
      case DioExceptionType.connectionTimeout:
        exMessage = "HTTP_connectionTimeout".tr;
      case DioExceptionType.sendTimeout:
        exMessage = 'HTTP_sendTimeout'.tr;
      case DioExceptionType.receiveTimeout:
        exMessage = 'HTTP_receiveTimeout'.tr;
      case DioExceptionType.badCertificate:
        exMessage = 'HTTP_badCertificate'.tr;
      case DioExceptionType.badResponse:
        exMessage = 'HTTP_badResponse'.tr;
      case DioExceptionType.cancel:
        exMessage = 'HTTP_cancel'.tr;
      case DioExceptionType.connectionError:
        exMessage = 'HTTP_connectionError'.tr;
      case DioExceptionType.unknown:
        exMessage = 'HTTP_unknown'.tr;
    }
    return exMessage;
  }
}


/// Dio限流拦截器
class DioLimiterInterceptor extends Interceptor {
  final int maxConcurrent;
  final Duration timeout;
  final Queue<_PendingRequest> _pendingRequests = Queue();
  int _currentConcurrent = 0;
  bool _isShutdown = false;

  DioLimiterInterceptor({
    this.maxConcurrent = 5,
    this.timeout = const Duration(seconds: 30),
  });

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    if (_isShutdown) {
      handler.reject(DioException(
        requestOptions: options,
        error: 'DioLimiter已关闭',
      ));
      return;
    }

    if (_currentConcurrent >= maxConcurrent) {
      // 达到最大并发数，加入等待队列
      final completer = Completer<void>();
      _pendingRequests.add(_PendingRequest(options, handler, completer));
      
      // 设置超时
      Timer(timeout, () {
        if (!completer.isCompleted) {
          completer.completeError('请求超时');
          _pendingRequests.removeWhere((item) => item.completer == completer);
        }
      });
      
      await completer.future;
    } else {
      // 直接执行请求
      _executeRequest(options, handler);
    }
  }

  void _executeRequest(RequestOptions options, RequestInterceptorHandler handler) {
    _currentConcurrent++;
    try {
      handler.next(options); // 直接调用，不要 then/await/return
    } finally {
      _currentConcurrent--;
      _processNextRequest();
    }
  }

  void _processNextRequest() {
    if (_pendingRequests.isNotEmpty) {
      final item = _pendingRequests.removeFirst();
      if (!item.completer.isCompleted) {
        item.completer.complete();
        _executeRequest(item.options, item.handler);
      }
    }
  }

  /// 获取状态
  Map<String, dynamic> get status => {
    'currentConcurrent': _currentConcurrent,
    'maxConcurrent': maxConcurrent,
    'pendingRequests': _pendingRequests.length,
    'isShutdown': _isShutdown,
  };

  /// 关闭限流器
  void shutdown() {
    _isShutdown = true;
    while (_pendingRequests.isNotEmpty) {
      final item = _pendingRequests.removeFirst();
      if (!item.completer.isCompleted) {
        item.completer.completeError('DioLimiter已关闭');
      }
    }
  }
}

class _PendingRequest {
  final RequestOptions options;
  final RequestInterceptorHandler handler;
  final Completer<void> completer;

  _PendingRequest(this.options, this.handler, this.completer);
}