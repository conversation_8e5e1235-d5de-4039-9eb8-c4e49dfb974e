
class EventBus401 {
  EventBus401();
}


class EventBusSyncProgress {
  int progress;
  String msg;
  EventBusSyncProgress(this.progress,this.msg);
}
//结束拜访事件
class EventBusEndCall{
  String callId; 
  EventBusEndCall(this.callId);
} 

//结束拜访事件
class EventBusSyncFile{
  EventBusSyncFile();
}

//开始拜访事件
class EventBusStartCall {
  EventBusStartCall();
}


//同步完成
class EventBusSyncComplete{
  String? callId; 
  EventBusSyncComplete(this.callId);
}

class EventBusUoload{ 
  EventBusUoload();
}
 