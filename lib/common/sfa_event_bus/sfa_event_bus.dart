import 'dart:async';

import 'package:event_bus/event_bus.dart';
import 'package:get/get.dart'; 
import '../../GlobalController.dart';
import 'event_bus_s.dart';

EventBus eventBus = EventBus();
GlobalController globalController = Get.find<GlobalController>();

class EventBusFunc { 
  static StreamSubscription<EventBusSyncFile>? _event_syncfile;
  static StreamSubscription<EventBus401>? _event_401;

  static http401(Function(EventBus401) func) {
    _event_401 = eventBus.on<EventBus401>().listen((event) async {
      await func(event);
    });
  }

  static http401dispose() {
    if (_event_401 != null) {
      _event_401!.cancel();
    }
  }

  static syncFileEvent(Function(EventBusSyncFile) func) {
    _event_syncfile = eventBus.on<EventBusSyncFile>().listen((event) async {
      await func(event);
    });
  }
   

  static syncFiledispose() {
    if (_event_syncfile != null) {
      _event_syncfile!.cancel();
    }
  }
}
