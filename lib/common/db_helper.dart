import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:synchronized/synchronized.dart';

///  数据库操作工具类
class dbHelper {
  static final Lock _lock = Lock();
  static DatabaseFactory databaseFactoryWebLocal = createDatabaseFactoryFfiWeb(
      options: SqfliteFfiWebOptions(
          sharedWorkerUri: Uri.parse('sqflite_sw.js')));
  static late Database db;
  static Future<Database> _getDbConnect() async {
    if (kIsWeb) {
      databaseFactory = databaseFactoryWebLocal;
    }
    var db = await databaseFactory.openDatabase('eBestDBnew.db');
    return db;
  }

  ///返回集合数据
  static Future<List<Map<String, Object?>>> executeRead(String querys,
      [List? params]) async {
    return _lock.synchronized(() async {
      var db = await _getDbConnect();
      try {
        List<Map<String, Object?>> list = await db.rawQuery(querys, params);
        return list;
      } on Exception {
        rethrow;
      } finally {
        if (db.isOpen) {
          await db.close();
        }
      }
    });
  }

  ///返回集合中的第一条数据
  static Future<Map<String, Object?>?> executeReadFirst(String querys,
      [List? params]) async {
    return _lock.synchronized(() async {
      var db = await _getDbConnect();
      try {
        List<Map<String, Object?>> list = await db.rawQuery(querys, params);
        if (list.isNotEmpty) {
          return list[0];
        } else {
          return null;
        }
      } on Exception {
        rethrow;
      } finally {
        if (db.isOpen) {
          await db.close();
        }
      }
    });
  }

  //执行单条sql
  static Future execute(String query, [List? params]) async {
    return _lock.synchronized(() async {
      var db = await _getDbConnect();
      try {
        await db.execute(query, params ?? []);
      } on Exception {
        rethrow;
      } finally {
        if (db.isOpen) {
          await db.close();
        }
      }
    });
  }

  //执行多条sql
  static Future executes(List<Map<String, dynamic>> querys) async {
    if (querys.isEmpty) throw Exception('querys is null');
    return _lock.synchronized(() async {
      var db = await _getDbConnect();
      try {
        await db.transaction((txn) async {
          var batch = txn.batch();
          for (var query in querys) {
            await txn.execute(query['query'], query['params'] ?? []);
          }
          await batch.commit(continueOnError: true).then((value) {
            return value.length == querys.length;
          });
        });
      } on Exception catch (e) {
        print('executes  Exception');
        print(e);
        print('executes  Exception');
        rethrow;
      } finally {
        if (db.isOpen) {
          await db.close();
        }
      }
    });
  }
 

  //导出数据库数据 json格式
  static Future exportDataToJson(String databaseName) async {
    // 打开数据库连接
    final db = await _getDbConnect();

    // 查询数据库中的所有表名
    final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name != 'sqlite_sequence';");

    // 用于保存所有表的数据的JSON对象
    Map<String, List<Map<String, dynamic>>> allData = {};

    // 导出每个表的数据
    for (final table in tables) {
      final tableName = table['name'] as String;
      final tableData = await db.query(tableName);
      allData[tableName] = tableData;
    }

    // 将所有表的数据转换为JSON格式
    final jsonData = json.encode(allData);
    print(jsonData);

    // 关闭数据库连接
    await db.close();
  }
}
