import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'dart:math' as math;
import '../them/base_them.dart';
import 'package:lottie/lottie.dart';
 
class SfaDialog {
  /// 显示Loading - 可口可乐瓶填充动画
  static void showLoading({String? message}) {
    Get.dialog(
      PopScope(
        canPop: false,
        child: Center(
          child: Material(
            type: MaterialType.transparency,
            child: Container(
              width: 200.w,
              padding: EdgeInsets.all(10.w),
              decoration: BoxDecoration(
                // color: ThemeManager.currentTheme.cardBackground.withOpacity(0.4),
                color: Colors.black.withOpacity(0.5),
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Lottie 可口可乐动画
                  SizedBox(
                    height: 120.h,
                    width: 120.w,
                    child: Lottie.asset(
                      'assets/lottie/coca.json',
                      fit: BoxFit.contain,
                      animate: true,
                      repeat: true,
                    ),
                  ),
                  
                  if (message != null) ...[
                    SizedBox(height: 5.h),
                    Text(
                        message,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16.sp,
                        ),
                      )
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
      barrierDismissible: true
    );
  }

  static void showLoading1({String? message}) {
    Get.dialog(
      PopScope(
        canPop: false,
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: const [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                if (message != null) ...[
                  const SizedBox(height: 16),
                  Text(message, style: const TextStyle(fontSize: 16)),
                ],
              ],
            ),
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  /// 关闭Loading
  static void dismissLoading() {
    if (Get.isDialogOpen ?? false) Get.back();
  }

  /// 显示Alert弹窗
  static Future<void> alert({
    required String title,
    required String content,
    String confirmText = '确定',
    VoidCallback? onConfirm,
  }) async {
    await Get.dialog(
      AlertDialog(
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
        content: Text(content),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              if (onConfirm != null) onConfirm();
            },
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  /// 显示Confirm弹窗
  static Future<void> confirm({
    required String title,
    required String content,
    String confirmText = '确定',
    String cancelText = '取消',
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) async {
    await Get.dialog(
      AlertDialog(
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
        content: Text(content),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              if (onCancel != null) onCancel();
            },
            child: Text(cancelText),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
            onPressed: () {
              Get.back();
              if (onConfirm != null) onConfirm();
            },
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  /// 显示Toast
  static void toast( String message, {Duration duration = const Duration(seconds: 2)}) {
    if (Get.isSnackbarOpen ?? false) Get.back();
    Get.rawSnackbar(
      message: message,
      duration: duration,
      backgroundColor: Colors.black87,
      borderRadius: 12,
      margin: const EdgeInsets.symmetric(horizontal: 40, vertical: 20),
      snackPosition: SnackPosition.TOP,
      snackStyle: SnackStyle.FLOATING,
      animationDuration: const Duration(milliseconds: 300),
      isDismissible: true,
      forwardAnimationCurve: Curves.easeOutBack,
      reverseAnimationCurve: Curves.easeInBack,
      messageText: Text(
        message,
        style: const TextStyle(color: Colors.white, fontSize: 16),
        textAlign: TextAlign.center,
      ),
    );
  }

  static void bottomSheet(String title, String message, {VoidCallback? onConfirm, VoidCallback? onColse, bool barrierDismissible = false}) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(title ,style: const TextStyle(fontSize: 16,color: Colors.black),),
                const Spacer(),
                GestureDetector(
                  onTap: () {
                    Get.back();
                    if (onColse != null) onColse();
                  },
                  child: const Icon(Icons.close, color: Colors.black),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Text(
              message,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: () {
                  Get.back();
                  if (onConfirm != null) onConfirm();
                },
                child: const Text(
                  'RESYNC',
                  style: TextStyle(fontSize: 18, color: Colors.white, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withOpacity(0.3),
      isDismissible: barrierDismissible,
      enableDrag: barrierDismissible,
    );
  }

  static void bottomSheetConfirm(String title, String message, {VoidCallback? onConfirm, VoidCallback? onCancel, bool barrierDismissible = false, String confirmText = '确认', String cancelText = '取消'}) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(title, style: const TextStyle(fontSize: 16, color: Colors.black)),
                const Spacer(),
              ],
            ),
            const SizedBox(height: 20),
            Text(
              message,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 32),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Colors.grey),
                    ),
                    onPressed: () {
                      Get.back();
                      if (onCancel != null) onCancel();
                    },
                    child: Text(cancelText, style: const TextStyle(fontSize: 16, color: Colors.black)),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Get.back();
                      if (onConfirm != null) onConfirm();
                    },
                    child: Text(confirmText, style: const TextStyle(fontSize: 16, color: Colors.white)),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withOpacity(0.3),
      isDismissible: barrierDismissible,
      enableDrag: barrierDismissible,
    );
  }
}
