import 'package:get/get_connect/http/src/utils/utils.dart';
import 'package:mop/api.dart';
import 'package:mop/mop.dart';
import 'package:teemo_coca/common/db_helper.dart';
import 'package:teemo_coca/sfaglobal.dart';

class FinClpManager {
  static const String apiServer = "https://www.finclip.com";

static const String sdkKeyA =
      "gGQNj7MtTZg6yxJdaOnxpQBH18g2LVRoE1Dt3bq7y47XMUZS9co7NSiJgdEUxGIu";
  static const String sdkSecretA = "2f97e0bb6dd4e91d";

static const String sdkKeyB =
      "gGQNj7MtTZg6yxJdaOnxpY+IZ2Mc/4tQEOogwWfvUnqgA4x+JWh7hhNS5aO52BFs";
  static const String sdkSecretB = "e59a7cfb5def7225";
  
  static String? currentAppletId;
  static Future<void> init() async {

FinStoreConfig storeConfigA =
        FinStoreConfig(sdkKeyA, sdkSecretA, apiServer);

FinStoreConfig storeConfigB =
        FinStoreConfig(sdkKeyB, sdkSecretB, apiServer);
        
    List<FinStoreConfig> storeConfigs = [storeConfigA,storeConfigB];
    Config config = Config(storeConfigs);
    UIConfig uiconfig = UIConfig();
    uiconfig.isAlwaysShowBackInDefaultNavigationBar = false;
    uiconfig.isClearNavigationBarNavButtonBackground = false;
    uiconfig.isHideFeedbackAndComplaints = true;
    uiconfig.isHideBackHome = true;
    uiconfig.isHideForwardMenu = true;
    CapsuleConfig capsuleConfig = CapsuleConfig();
    // capsuleConfig.capsuleBgLightColor = 0x33ff00ee;
    capsuleConfig.capsuleCornerRadius = 16;
    // capsuleConfig.capsuleRightMargin = 25;
    uiconfig.capsuleConfig = capsuleConfig;
    uiconfig.appletText = "applet";
    // uiconfig.loadingLayoutCls = "com.finogeeks.mop_example.CustomLoadingPage";
    uiconfig.autoAdaptDarkMode = true;
    uiconfig.isHideAddToDesktopMenu = true;

    config.appletAutoAuthorize = true;
    config.disableRequestPermissions = false;
    final res = await Mop.instance.initSDK(config, uiConfig: uiconfig);
    Mop.instance.registerAppletHandler(MyAppletHandler());
    Mop.instance.registerExtensionApi('getUserProfile', getUserProfile);

    /// 只读 执行单条sql
    /// 参数demo
    /// {
    ///   "query":"select * from user where id > ?",
    ///   "params":[1]
    /// }
    /// 返回一个集合
    /// {
    ///    "success":true,
    ///    "data":[...]
    ///    "message":null
    /// }
    Mop.instance.registerExtensionApi('SFADbExecuteRead', (params) async {
      print("SFADbExecuteRead--------:$params");
      try {
        var data = await dbHelper.executeRead(params['query'], params['params']);
        return {"success": true, "data": data, "message": null};
      } catch (e) {
        return {"success": false, "data": null, "message": e.toString()};
      }
    });

    /// 只读 获取第一条数据
    /// 参数demo
    /// {
    ///   "query":"select * from user where id > ?",
    ///   "params":[1]
    /// }
    /// 返回一个对象
    /// {
    ///    "data":{}
    ///    "message":null
    ///    "success":true
    /// }
    Mop.instance.registerExtensionApi('SFADbExecuteReadFirst', (params) async {
      print("SFADbExecuteReadFirst:$params");
      try {
        var data =
            await dbHelper.executeReadFirst(params['query'], params['params']);
        return {"success": true, "data": data, "message": null};
      } catch (e) {
        return {"success": false, "data": null, "message": e.toString()};
      }
    });

    /// 单条sql： 写入 ，更新，删除
    /// 参数demo
    /// {
    ///   "query":"update user set name='zhangsan' where id = ?",
    ///   "params":[1]
    /// }
    /// 返回一个对象
    /// {
    ///    "success":true,
    ///    "data":null
    ///    "message":"错误信息"
    /// }
    Mop.instance.registerExtensionApi('SFADbExecute', (params) async {
      try {
        await dbHelper.execute(params['query'], params['params']);
        return {"success": true, "data": null, "message": null};
      } catch (e) {
        return {"success": false, "data": null, "message": e.toString()};
      }
    });

    /// 批量： 写入 ，更新，删除
    /// 参数demo
    /// [{
    ///   "query":"update user set name='zhangsan' where id = ?",
    ///   "params":[1]
    /// },
    ///   "query":"delete from user where id = ?",
    ///   "params":[1]
    /// },
    /// ]
    /// 返回一个对象
    ///{
    ///"success": true,
    ///"data": null,
    ///"message":null
    ///};
    Mop.instance.registerExtensionApi('SFADbExecutes', (params) async {
      print("SFADbExecutes:$params");
      try {
        await dbHelper.executes(params);
        return {"success": true, "data": null, "message": null};
      } catch (e) {
        return {"success": false, "data": null, "message": e.toString()};
      }
    });
    // if (!mounted) return;
  }

  static Future<Map<String, dynamic>> getUserProfile(dynamic params) async {
    Map<String, dynamic> result = {
      "userInfo": {
        "nickName": SfaGlobal.loginUserInfo?.userName ?? "error:not find nickName",
        "avatarUrl": "https://www.ebestmobile.com/wp-content/uploads/2024/09/eBest-logo.png",
        "gender": 1,
        "country": "China",
        "province": "Shanghai",
        "city": "Shanghai",
        "token": SfaGlobal.loginUserInfo?.token ?? "",
      }
    };

    return Future.value(result);
  }

  static openApplet(String appletId) async {
    if (currentAppletId != null) {
      await Mop.instance.closeApplet(currentAppletId ?? '', true);
      currentAppletId = null;
    }
    currentAppletId = appletId;
    await Mop.instance.openApplet(appletId);
  }

  static startApplet(String appletId) async {
    if (currentAppletId != null) {
      await Mop.instance.closeApplet(currentAppletId ?? '', true);
      currentAppletId = null;
    }
    currentAppletId = appletId;
    Mop.instance.startApplet(
        RemoteAppletRequest(appletId: appletId, apiServer: apiServer));
  }

  //结束小程序 小程序会从内存中清除
  static finishRunningApplet() async {
    if (currentAppletId != null) {
      await Mop.instance.finishRunningApplet(currentAppletId ?? '', true);
      currentAppletId = null;
    }
  }

  //关闭小程序 小程序会在内存中存在
  static closeApplet() async {
    if (currentAppletId != null) {
      await Mop.instance.closeApplet(currentAppletId ?? '', true);
      currentAppletId = null;
    }
  }
}

class MyAppletHandler extends AppletHandler {
  @override
  Future<void> appletDidOpen(String appId) async {
    print("appletDidOpen appId:$appId");
  }

  @override
  bool customCapsuleMoreButtonClick(String appId) {
    print("customCapsuleMoreButtonClick---");
    // toAppMessageChannel.invokeMethod("showCustomMoreView", {"appId": appId});
    return false;
  }

  @override
  void forwardApplet(Map<String, dynamic> appletInfo) {
    print("forwardApplet ---");
  }

  @override
  Future<List<CustomMenu>> getCustomMenus(String appId) {
    CustomMenu menu1 = CustomMenu(
        'menu100',
        'https://img1.baidu.com/it/u=2878938773,1765835171&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',
        '百度图标',
        'common');
    menu1.darkImage =
        'https://img95.699pic.com/xsj/14/46/mh.jpg%21/fw/700/watermark/url/L3hzai93YXRlcl9kZXRhaWwyLnBuZw/align/southeast';

    CustomMenu menu2 =
        CustomMenu('menu101', 'minipro_list_collect', '工程图标', 'common');
    menu2.darkImage = 'minipro_list_service';

    List<CustomMenu> customMenus = [
      menu1,
      menu2,
      CustomMenu(
          'ShareDingDing',
          'https://encrypted-tbn0.gstatic.cn/images?q=tbn:ANd9GcSpvugSNLs9R7iopz_noeotAelvgzYj-74iCg&usqp=CAU',
          '谷歌图标',
          'common'),
    ];
    return Future.value(customMenus);
  }

  @override
  Future<void> getMobileNumber(
    Function(dynamic params) param0,
  ) async {
    param0.call({"phone": "HubSFA12345678901"});
    return Future.value(null);
  }

  @override
  Future<Map<String, dynamic>> getUserInfo() {
    throw UnimplementedError();
  }

  @override
  Future<void> onCustomMenuClick(
      String appId, String path, String menuId, String appInfo) {
    throw UnimplementedError();
  }
}
