//同步状态
class SyncState{
//数据同步状态
  static SyncStatus _currentSyncStatus=SyncStatus.END;
  static setSyncState(SyncStatus status){
    _currentSyncStatus = status;
  }
  //获取数据同步状态
  static get getSyncStatus =>_currentSyncStatus;

  //文件同步状态
  static FileSyncStatus _currentFileSyncStatus=FileSyncStatus.END;
  static setFileSyncState(FileSyncStatus status){
    _currentFileSyncStatus = status;
  }
  //获取文件同步状态
  static get getFileSyncStatus =>_currentFileSyncStatus;


}

enum SyncStatus {
  //同步中
  RUNNING,
  //同步结束
  END
}

enum FileSyncStatus {
  //同步中
  RUNNING,
  //同步结束
  END
}