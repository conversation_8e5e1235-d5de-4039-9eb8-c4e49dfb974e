// import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../common/http_helper.dart';
import 'common.dart';
import 'package:path_provider/path_provider.dart' as path_provider;

class FileHelper  {
  static late Directory dirDocument;
  static late Directory dirImage;
  static late Directory dirRoot; 
  static late String applicationDocumentsDirectory;
  static init() async {
    if (!kIsWeb) {
      dirDocument = await createDir(Common.fileDirectory.document);
      dirImage = await createDir(Common.fileDirectory.image);
      dirRoot = await createDir(Common.fileDirectory.root); 
      applicationDocumentsDirectory = (await path_provider.getApplicationDocumentsDirectory()).path;
    }
  }

  static String? getFileExit(String type) {
    Map<String, String> MIME_MapTable = {
      "video/3gpp": ".3gp",
      "application/vnd.android.package-archive": ".apk",
      "video/x-ms-asf": ".asf",
      "video/x-msvideo": ".avi",
      "application/octet-stream": ".exe",
      "image/bmp": ".bmp",
      "text/plain": ".xml",
      "application/msword": ".doc",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
          ".docx",
      "image/gif": ".gif",
      "application/x-gtar": ".gtar",
      "application/x-gzip": ".gz",
      "text/html": ".html",
      "application/java-archive": ".jar",
      "image/jpeg": ".jpg",
      "application/x-javascript": ".js",
      "audio/x-mpegurl": ".m3u",
      "audio/mp4a-latm": ".m4p",
      "video/vnd.mpegurl": ".m4u",
      "video/x-m4v": ".m4v",
      "video/quicktime": ".mov",
      "audio/x-mpeg": ".mp3",
      "video/mp4": ".mpg4",
      "application/vnd.mpohun.certificate": ".mpc",
      "video/mpeg": ".mpg",
      "audio/mpeg": ".mpga",
      "application/vnd.ms-outlook": ".msg",
      "audio/ogg": ".ogg",
      "application/pdf": ".pdf",
      "image/png": ".png",
      "application/vnd.ms-powerpoint": ".ppt",
      "application/x-rar-compressed": ".rar",
      "audio/x-pn-realaudio": ".rmvb",
      "application/rtf": ".rtf",
      "application/x-tar": ".tar",
      "application/x-compressed": ".tgz",
      "audio/x-wav": ".wav",
      "audio/x-ms-wma": ".wma",
      "audio/x-ms-wmv": ".wmv",
      "application/vnd.ms-works": ".wps",
      "application/x-compress": ".z",
      "application/zip": ".zip",
      "application/vnd.ms-excel tapplication/x-excel": ".xls",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
          ".xlsx",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation":
          ".pptx",
      "application/vnd.openxmlformats-officedocument.presentationml.slideshow":
          ".ppsx"
    };

    return MIME_MapTable[type];
  }

  static String? getFileMimeType(String fileName) {
    String subName = "." + fileName.split('.').last;
    var MIME_MapTable = {
      //{后缀名，MIME类型}
      ".3gp": "video/3gpp",
      ".3gpp": "video/3gpp",
      ".apk": "application/vnd.android.package-archive",
      ".asf": "video/x-ms-asf",
      ".avi": "video/x-msvideo",
      ".bin": "application/octet-stream",
      ".bmp": "image/bmp",
      ".c": "text/plain",
      ".class": "application/octet-stream",
      ".conf": "text/plain",
      ".cpp": "text/plain",
      ".doc": "application/msword",
      ".docx":
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ".exe": "application/octet-stream",
      ".gif": "image/gif",
      ".gtar": "application/x-gtar",
      ".gz": "application/x-gzip",
      ".h": "text/plain",
      ".htm": "text/html",
      ".html": "text/html",
      ".jar": "application/java-archive",
      ".java": "text/plain",
      ".jpeg": "image/jpeg",
      ".jpg": "image/jpeg",
      ".js": "application/x-javascript",
      ".log": "text/plain",
      ".m3u": "audio/x-mpegurl",
      ".m4a": "audio/mp4a-latm",
      ".m4b": "audio/mp4a-latm",
      ".m4p": "audio/mp4a-latm",
      ".m4u": "video/vnd.mpegurl",
      ".m4v": "video/x-m4v",
      ".mov": "video/quicktime",
      ".mp2": "audio/x-mpeg",
      ".mp3": "audio/x-mpeg",
      ".mp4": "video/mp4",
      ".mpc": "application/vnd.mpohun.certificate",
      ".mpe": "video/mpeg",
      ".mpeg": "video/mpeg",
      ".mpg": "video/mpeg",
      ".mpg4": "video/mp4",
      ".mpga": "audio/mpeg",
      ".msg": "application/vnd.ms-outlook",
      ".ogg": "audio/ogg",
      ".pdf": "application/pdf",
      ".png": "image/png",
      ".pps": "application/vnd.ms-powerpoint",
      ".ppt": "application/vnd.ms-powerpoint",
      ".prop": "text/plain",
      ".rar": "application/x-rar-compressed",
      ".rc": "text/plain",
      ".rmvb": "audio/x-pn-realaudio",
      ".rtf": "application/rtf",
      ".sh": "text/plain",
      ".tar": "application/x-tar",
      ".tgz": "application/x-compressed",
      ".txt": "text/plain",
      ".wav": "audio/x-wav",
      ".wma": "audio/x-ms-wma",
      ".wmv": "audio/x-ms-wmv",
      ".wps": "application/vnd.ms-works",
      //".xml",    "text/xml",
      ".xml": "text/plain",
      ".z": "application/x-compress",
      ".zip": "application/zip",
      ".xls": "application/vnd.ms-excel application/x-excel",
      ".xlsx":
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ".pptx":
          "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      ".ppsx":
          "application/vnd.openxmlformats-officedocument.presentationml.slideshow",
      "": "*/*"
    };
    return MIME_MapTable[subName];
  }

  //创建文件夹
  static Future<Directory> createDir(String dirName) async {
    try {
      Directory documentsDirectory =
          await path_provider.getApplicationDocumentsDirectory();
      String path =
          '${documentsDirectory.path}${Platform.pathSeparator}$dirName';
      var dir = Directory(path);
      var exist = dir.existsSync();
      if (!exist) {
        return await dir.create(recursive: true);
      }
      return dir;
    } catch (e) {
      rethrow;
    }
  }

  deleteDirectory(String path) {
    Directory directory = Directory(path);
    if (directory.existsSync()) {
      List<FileSystemEntity> files = directory.listSync();
      if (files.isNotEmpty) {
        for (var file in files) {
          file.deleteSync();
        }
      }
      directory.deleteSync();
    }
  }

  deleteFile(String path) {
    File file = File(path);
    if (file.existsSync()) {
      file.deleteSync();
    }
  }

  static Future<bool> fileExists(Directory? dirName, String filename) async {
    dirName ??= FileHelper.dirDocument;
    // Directory documentsDirectory =
    //     await path_provider.getApplicationDocumentsDirectory();
    String path = '${dirName.path}/$filename';
    print(path);
    File file = File(path);
    return file.existsSync();
  }

  //下载文件
  //uri 远程文件路径，filename 保存的文件名，savePath 保存路径 默认保存到document
  Future<File> downloadFile(String uri, String fileName,
      [String? savePath, Function(int, int)? onReceiveProgress]) async {
    savePath ??= Common.fileDirectory.document;
    var folder = await createDir(savePath.toString());
    // var oauth = await SfaGlobal.getOauth2();
    // var headers = {"Authorization": "Bearer ${oauth.accessToken}"};
    var headers = {"Authorization": "Bearer "};
    var dwfile = await HttpHelper.dio.get(
        uri, options:   Options(headers: headers, responseType: ResponseType.bytes),
        onReceiveProgress: onReceiveProgress);
    File file = File('${folder.path}/$fileName');
    return await file.writeAsBytes(dwfile.data);
  }

  //下载文件
  //uri 远程文件路径，filename 保存的文件名，savePath 保存路径 默认保存到document
  Future diodownloadFile(String uri, String fileName,
      [String? savePath]) async {
    savePath ??= Common.fileDirectory.document;
    var folder = await createDir(savePath.toString());
    // var oauth = await SfaGlobal.getOauth2();
    // var headers = {"Authorization": "Bearer ${oauth.accessToken}"};
     var headers = {"Authorization": "Bearer "};
      await HttpHelper.dio.download(uri, '${folder.path}/$fileName',
        options: Options(headers: headers));
  }
  
  static Future<List> chekcFile(List list, Directory? dirName,
      [Function? callback]) async {
    dirName ??= FileHelper.dirDocument;
    var path = dirName.path;
    for (var item in list) {
      File file = File("$path${Platform.pathSeparator}${item.fileName}");
      item.isDownload.value = file.existsSync() ? 2 : 0;
      item.filePath.value = file.existsSync()
          ? "$path${Platform.pathSeparator}${item.fileName}"
          : "";
      if (callback != null) {
        callback(item);
      }
    }
    return list;
  }

  static Future<File> getFileItem(String fileName, Directory dirName) async {
    var path = dirName.path;
    File file = File("$path${Platform.pathSeparator}${fileName}");
    return file;
  }
   
  Future clearFileByDir(Directory directory) async {
    if (!directory.existsSync()) {
      throw FileSystemException('Directory does not exist');
    }

    await Future.forEach(directory.listSync(), (FileSystemEntity entity) async {
      if (entity is Directory) {
        await clearFileByDir(entity);
        await entity.delete();
      } else if (entity is File) {
        await entity.delete();
      }
    });
  }
 
} 
