import 'dart:async';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:jpush_flutter/jpush_flutter.dart';
import 'package:jpush_flutter/jpush_interface.dart';

class JPushManager {
  static final JPushManager _instance = JPushManager._internal();
  factory JPushManager() => _instance;
  JPushManager._internal();

  final JPushFlutterInterface jpush = JPush.newJPush();
  bool _initialized = false;
  String? _lastAlias;

  Future<void> init({
    required String appKey,
    String channel = "developer-default",
    bool production = false,
    bool debug = true,
    NotificationSettingsIOS iosSettings = const NotificationSettingsIOS(sound: true, alert: true, badge: true),
    String? deviceAlias,
  }) async {
    if (_initialized) return;
    _initialized = true;
    try {
      if(!Platform.isAndroid && !Platform.isIOS){
        jpush.setCallBackHarmony((eventName, data) async {
          print("[JPushManager] eventName: $eventName");
          print("[JPushManager] data: $data");
        });
      }

      jpush.setAuth(enable: true);
      jpush.setup(
        appKey: appKey,
        channel: channel,
        production: production,
        debug: debug,
      );
      jpush.applyPushAuthority(iosSettings);

      jpush.addEventHandler(
        onConnected: (Map<String, dynamic> message) async {
          print("[JPushManager] onConnected: $message");
          if (deviceAlias != null) {
            await setAlias(deviceAlias);
          }
        },
      );

      jpush.getRegistrationID().then((rid) {
        print("[JPushManager] registration id: $rid");
      });
    } on PlatformException catch (e) {
      print("[JPushManager] PlatformException during init: $e");
    } catch (e) {
      print("[JPushManager] Error during init: $e");
    }
  }

  Future<void> setAlias(String alias) async {
    print("[JPushManager] setAlias $alias");
    _lastAlias = alias;
    try {
      await jpush.setAlias(alias);
      print("[JPushManager] 设置别名成功: $alias");
    } catch (e) {
      print("[JPushManager] 设置别名失败: $e");
    }
  }
 

  Future<String?> getRegistrationId() async {
    try {
      return await jpush.getRegistrationID();
    } catch (e) {
      print("[JPushManager] 获取 registrationId 失败: $e");
      return null;
    }
  }
  // 其他 JPush 相关方法可继续补充
} 