import 'dart:io';

class Common {
  static String get sfaVersion => "1.5.6";
  static LocalStorageKey get localStorageKey => LocalStorageKey();
  static AppConfig get appConf => AppConfig();
  static List get recordActionValues => [1, 2]; // 上传数据状态 新增:1,更新:2,
  static RecordAction get recordAction => RecordAction();
  static IsActive get isActive => IsActive();
  static XgklPhotoSource get xgklPhotoSource => XgklPhotoSource();
  //目录名称
  static FileDirectoryName get fileDirectoryName => FileDirectoryName();
  static FileDirectory get fileDirectory => FileDirectory();
  static FileFolders get fileFolders => FileFolders();

  ///目录分割符
  static String get pathSeparator => Platform.pathSeparator;
  
  //批量下载页大小
  static int get batchDownloadPageSize => 500;
 
   static LoginConfigTable get loginConfigTable => LoginConfigTable();

  static String get slaKey => 'QqwM98C0AOAAIMAACCAAhAMQAkOEhiC9';

  static SourceType get sourceType => SourceType();

  static List<Map<String, dynamic>> SfdcConnects =  
  [
    {
      "isSelected": false,
      "text": "Production",
      "value": "Production",
      "loginUrl": "https://login.salesforce.com/services/oauth2/token",
      "clientId":
          "3MVG9n_HvETGhr3CkILUF9OOdBOLZRWlzX6CXzyksoSFp9LPD7Fm1QqLmDnh8T5FNEaBz5iVkTdLX7uy9uDvS",
      "clientSecret":
          "D2BAC56C80CAD183192DF7E1D375AD169191762D0CCFAA32B8CADCF583D47F40",
    },
    {
      "isSelected": false,
      "text": "Sandbox",
      "value": "Sandbox",
      "loginUrl": "https://test.salesforce.com/services/oauth2/token",
      "clientId":
          "3MVG9n_HvETGhr3CkILUF9OOdBOLZRWlzX6CXzyksoSFp9LPD7Fm1QqLmDnh8T5FNEaBz5iVkTdLX7uy9uDvS",
      "clientSecret":
          "D2BAC56C80CAD183192DF7E1D375AD169191762D0CCFAA32B8CADCF583D47F40",
    }
  ];
 
 
}

class SourceType {
  String get sfdc => "SFDC";
  String get eBest => "eBest";
  String get local => "Local";
}


class LoginConfigTable {
  get mobileObjectConfigure => "ebMobile__MobileObjectConfigure__c";
  get iMentorObjectConfig => "ebMobile__iMentorObjectConfig__c";
  get eBestSFASetting => "ebMobile__eBestSFASetting__c";
  get user => "User";
  get configuration => "ebMobile__Configuration__c";
  get dynamicPageConfig => "ebMobile__DynamicPageConfig__c";
  get callConfiguration => "ebMobile__CallConfiguration__c";
  get invoiceCalendarConfig => "ebMobile__InvoiceCalendarConfig__c";
}

class FileDirectoryName {
  String get document => "document";
  String get image => "image";
}

class XgklPhotoSource {
  String get Camera => 'Camera'; //系统相机或系统相册
  String get Visit => 'Visit'; //本次Visit已存在的照片
}

//App Config
class AppConfig {
  get appVersion => "1.5.6";
  get splitChart => "▏";
  get aesPrivateKey => "ebestmobile_teemo"; //加密key
  get filePrefix => "teemo__temp__"; //照片默认前缀
}

class FileFolders {
  String get asset => "CustomerAsset"; 
}

class FileDirectory {
  String get root => "root";
  String get document =>
      "root${Platform.pathSeparator}${FileDirectoryName().document}";
  String get image =>
      "root${Platform.pathSeparator}${FileDirectoryName().image}";
}

class RecordAction {
  int get tempRec => -1; //临时数据 --- 不上传数据状态
  int get defaultRec => 0; //默认未修改
  int get insertRec => 1; //新增的数据
  int get updateRec => 2; //更新的数据
  int get deleteRec => 3;
  int get holdRec => 4; //hold数据
  int get updateHoldRec => 5;
}
 
class IsActive {
  ///有效
  String get active => "1";

  ///无效
  String get disable => "0";
}
  
//Sorgae key
class LocalStorageKey {
  /*
  系统配置信息
  */
  get appSettingInfo => "appSettingInfo";
  
  //token
  get token => "token";
  get sfdcInstanceUrl => "sfdcInstanceUrl";
  get sfdcApiVersion => "sfdcApiVersion";
  get sfdcDownloadGroupCount => "sfdcDownloadGroupCount";

  //下载开始时间
  get lastSyncTime => "lastSyncTime";
  //app版本
  get appVersion => "appVersion";
  //设备ID
  get deviceId => "deviceId";
  //用户信息
  get loginUserInfo => "loginUserInfo";

  // 语言设置
  get selectedLanguage => "selectedLanguage";
  
  // 市场设置
  get selectedMarket => "selectedMarket";
  
  // 用户凭证
  get userCredentials => "userCredentials";

  // 主题设置
  get selectedTheme => "selectedTheme";

  // 数据源设置
  get selectedDataSource => "selectedDataSource";

  // 角色权限
  get sfaRolePromise => "SFARolePromise";

  // 登录配置
  get keepTableRecordsConfig => "keepTableRecordsConfig";

  // 权限
  get permissions => "permissions";

}



 