import 'package:dio/dio.dart';

class SfaException implements Exception{
    String message;
    SfaErrorType errorType;
    dynamic other;
    SfaException(this.message,this.errorType,[this.other]);
}

enum SfaErrorType{
  //同步中
  syncProcessing,
   //登录
  login,
  //配置数据
  configData,
   //db
  dbSchema, 
  //数据下载
  dataDownload, 
  //同步成功
  updateSyncSuccess,
  //修改密码
  changePassword,
  //单表批量同步数据
  downloadTable, 
  //上传数据
  uploadData,
  //定位服务 
  locationServiceEnabled,
  //定位权限
  locationPermissionDenied,
  //定位异常
  locationException,
  //ExportDB
  exportDB,
  //初始化同步
  initialSync,
  getOnlineData,
  httpError
}