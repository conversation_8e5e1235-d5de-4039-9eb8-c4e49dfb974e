import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
// import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../extension/StringExtension.dart';
import 'dart:convert';
import '../sfaglobal.dart';
import 'common.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:uuid/uuid.dart';
import 'package:crypto/crypto.dart';
import 'file_helper.dart';
// import 'package:geolocator/geolocator.dart' as geolocation;
import 'package:url_launcher/url_launcher.dart' as urllauncher;

class Util {
  static SharedPreferences? _SharedPreferences;

  ///写入Storage
  static Future setStorage(String key, dynamic value,
      [bool tojson = true]) async {
    _SharedPreferences ??= await SharedPreferences.getInstance();
    _SharedPreferences?.setString(key, tojson ? jsonEncode(value) : value);
  }

  ///获取Storage
  static getStorage(String key, [bool jsondecode = true]) async {
    try {
      _SharedPreferences ??= await SharedPreferences.getInstance();
      String? s = _SharedPreferences?.getString(key);
      // print('getStorage---------1  key $key s $s');
      if (s != null) {
        if (jsondecode) {
          return json.decode(s);
        } else {
          return s;
        }
      } else {
        return null;
      }
    } catch (e) {
      // print(e);
      return e.toString();
    }
  }

  ///移除某个key的所有数据
  static removeStorage(String key) async {
    // var sp = await SharedPreferences.getInstance();
    _SharedPreferences ??= await SharedPreferences.getInstance();
    _SharedPreferences?.remove(key);
  }

  bool getNewPoCompleted(List arr) {
    List questionCompleted = ['Yes', 'Yes（対象外店舗）', '定番扱いなし店舗', '定番なし'];
    return arr.every((item) {
      return questionCompleted.contains('$item');
    });
  }

  ///清空所有本地数据
  ///慎用！！！
  static clearStorage() async {
    // var sp = await SharedPreferences.getInstance();
    _SharedPreferences ??= await SharedPreferences.getInstance();
    await _SharedPreferences?.clear();
  }

/*
  * Md5加密
  * */
  static String Md5(String data) {
    var content = const Utf8Encoder().convert(data);
    var digest = md5.convert(content);
    return digest.toString();
  }

  ///反序列
  static T deSerialization<T>(String json) {
    T nJson = jsonDecode(json);
    return nJson;
  }

  ///序列化
  static String serialization(Object obj) {
    String nJson = jsonEncode(obj);
    return nJson;
  }

  ///字符串转base64
  static String base64En(Object params, [bool tojson = true]) {
    return base64.encode(
        utf8.encode(tojson ? serialization(params) : params.toString()));
  }

  ///base64转字符串
  static String base64De(String params) {
    return utf8.decode(base64.decode(params));
  }

  ///获取设备信息
  static Future<Map<String, dynamic>> getDirverInfo() async {
    final deviceInfoPlugin = DeviceInfoPlugin();
    final deviceInfo = await deviceInfoPlugin.deviceInfo;
    final allInfo = deviceInfo.data;
    return allInfo;
  }

  static Future<String> getDeviceAlias() async {
  final deviceInfo = DeviceInfoPlugin();

  if (Platform.isAndroid) {
    final androidInfo = await deviceInfo.androidInfo;
    // 设备别名可以用 device、model、manufacturer 等字段
    return androidInfo.model ?? "Unknown Android Device";
  } else if (Platform.isIOS) {
    final iosInfo = await deviceInfo.iosInfo;
    // name 字段通常是用户给设备起的别名
    return iosInfo.name ?? "Unknown iOS Device";
  } else {
    return "Unknown Device";
  }
}

  static String getRecordId() {
    return SfaGlobal.dataSource == Common.sourceType.sfdc
        ? getRecordId1()
        : getRecordId2();
  }

  //sfdc生成记录格式必须为GUID开头
  static String getRecordId1() {
    return 'GUID${getUuid()}';
  }

  //eBest生成记录格式为16位数字
  static String getRecordId2({int size = -5}) {
    var routeNo = 1;
    var tick = DateTime.now().millisecondsSinceEpoch.toString().last(size);
    // const randomNumber = Math.floor(Math.random() * 1000000);
    var randomNumber = Random().nextInt(1000000).toString().last(size);
    return '$routeNo$tick$randomNumber';
  }

  //获取是否有定位权限
  Future<bool> getLocationPermissions() async {
    // Position? location = await getCurrentLocation();
    // if (location == null) {
    //   return false;
    // } else {
    //   return true;
    // }
    return false;
  }

  static Future<String> getPlatform() async {
    String platform = "xxxxx";
    if (kIsWeb) {
      var browserInfo = await DeviceInfoPlugin().webBrowserInfo;
      platform =
          "${browserInfo.appCodeName} ${browserInfo.appVersion} ${browserInfo.browserName.name}  ${browserInfo.platform}";
    } else {
      if (Platform.isAndroid) {
        var androidInfo = await DeviceInfoPlugin().androidInfo;
        var release = androidInfo.version.release;
        var sdkInt = androidInfo.version.sdkInt;
        var manufacturer = androidInfo.manufacturer;
        var model = androidInfo.model;
        platform = "Android $release (SDK $sdkInt), $manufacturer $model";
      } else if (Platform.isIOS) {
        var iosInfo = await DeviceInfoPlugin().iosInfo;
        var systemName = iosInfo.systemName;
        var version = iosInfo.systemVersion;
        var name = iosInfo.name;
        var model = iosInfo.model;
        platform = "$systemName $version, $name $model";
      }
    }
    return platform;
  }

  static String getUuid() {
    var uuid = const Uuid();
    var v1 = uuid.v1(); // -> '6c84fb90-12c4-11e1-840d-7b25c5ee775a'
    return v1;
    // // Generate a v4 (random) id
    // var v4 = uuid.v4(); // -> '110ec58a-a0f2-4ac4-8393-c866d813b8d1'

    // // Generate a v4 (crypto-random) id
    // var v4_crypto = uuid.v4(options: {'rng': UuidUtil.cryptoRNG});
    // // -> '110ec58a-a0f2-4ac4-8393-c866d813b8d1'

    // // Generate a v5 (namespace-name-sha1-based) id
    // var v5 = uuid.v5(Uuid.NAMESPACE_URL, 'www.google.com');
  }

  //是否是平板
  static Future<bool> isTable() async {
    var setting = await getStorage(Common.localStorageKey.appSettingInfo);
    if (setting != null) {
      String device = setting["device"];
      return device == "Pad";
    } else {
      //日本全部
      //是平板
      return true; //web默认pad
    }
  }

  //色值转换
  static Color hexColor(String source) {
    source = source.toUpperCase().replaceAll('#', '');
    if (source.length == 6) {
      source = 'FF$source';
    }
    return Color(int.parse(source, radix: 16));
  }

  static MaterialColor hexMaterialColor(String source) {
    Color color = hexColor(source);
    List<double> strengths = <double>[.05];
    Map<int, Color> smatch = <int, Color>{};
    final int r = color.red, g = color.green, b = color.blue;
    for (int i = 0; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    for (var strength in strengths) {
      final double ds = 0.5 - strength;
      smatch[(strength * 1000).round()] = Color.fromARGB(
          r + ((ds < 0 ? r : (255 - r)) * ds).round(),
          g + ((ds < 0 ? g : (255 - g)) * ds).round(),
          b + ((ds < 0 ? b : (255 - b)) * ds).round(),
          1);
    }
    return MaterialColor(color.value, smatch);
  }

  /// fileType 文件类型  照片 文档
  /// source 默认相机
  static Future openCameraFile(
      {SfaImageType fileType = SfaImageType.Image,
      // ImageSource source = ImageSource.gallery,
      ImageSource source = ImageSource.camera,
      RxBool? isUserCamera}) async {
    final picker = ImagePicker();
    if (isUserCamera != null) isUserCamera.value = true;
    final pickedFile = await picker.pickImage(source: source, imageQuality: 40);
    if (pickedFile != null) {
      late Directory dir;
      switch (fileType) {
        case SfaImageType.Image:
          dir = FileHelper.dirImage;
          break;
      }

      var newName = "${Common.appConf.filePrefix}${getUuid()}.jpg";
      var saveAsPath = "${dir.path}${Common.pathSeparator}$newName";
      await pickedFile.saveTo(saveAsPath);
      if (isUserCamera != null) isUserCamera.value = false;
      return {
        "name": newName,
        "nativeURL": saveAsPath,
      };
    } else {
      if (isUserCamera != null) isUserCamera.value = false;
    }
  }

  // getApplicationDocumentsDirectory() 路径相当于 '/data/user/0/xx.xx.xx/app_flutter'
  // getTemporaryDirectory()            路径相当于'/data/user/0/xx.xx.xx/cache'
  // getExternalStorageDirectory()      仅Android平台可用，路径相当于'/storage/emulated/0'外置存储根路径
  // getApplicationSupportDirectory()   仅Ios平台可用

  ///返回照片base64
  /// fileType 文件类型  照片 文档
  /// source 默认相机
  static Future openCameraData(
      {ImageSource source = ImageSource.camera}) async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: source, imageQuality: 40);
    if (pickedFile != null) {
      var avataFile = File(pickedFile.path);
      List<int> imageBytes = await avataFile.readAsBytes();
      String base64 = base64Encode(imageBytes);
      return "data:image/jpg;base64,$base64";
    }
  }

  double convertDegreesToRadians(double value) {
    return value * pi / 180;
  }

  double calculateLatLngDistance(
      double lat1, double lon1, double lat2, double lon2) {
    final double R = 6371 * 1000; // meters
    final double dLat = convertDegreesToRadians(lat2 - lat1);
    final double dLon = convertDegreesToRadians(lon2 - lon1);
    lat1 = convertDegreesToRadians(lat1);
    lat2 = convertDegreesToRadians(lat2);
    final double a = sin(dLat / 2) * sin(dLat / 2) +
        sin(dLon / 2) * sin(dLon / 2) * cos(lat1) * cos(lat2);
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    final double d = R * c;
    return d;
  }

  // 监听定位服务状态的变化
  // Stream<bool> locationServiceStream() {
    // return geolocation.Geolocator.getServiceStatusStream().map((status) {
    //   return status == geolocation.ServiceStatus.enabled;
    // }); 
  // }

  // 获取当前位置
  // static Future<geolocation.Position?> getCurrentLocation() async {
    // try {
    //   return await geolocation.Geolocator.getCurrentPosition(
    //       desiredAccuracy: geolocation.LocationAccuracy.high);
    // } catch (e) {
    //   print('Error getting current location: $e');
    // }
    // return null;
  // }

  // 检查定位服务和权限并请求权限
  Future<bool> checkLocationServiceAndPermission() async {
    // 检查定位服务是否已经开启
    // bool isLocationServiceEnabled =
    //     await geolocation.Geolocator.isLocationServiceEnabled();

    // // 检查定位权限并请求权限
    // geolocation.LocationPermission permission =
    //     await geolocation.Geolocator.checkPermission();
    // if (permission == geolocation.LocationPermission.denied ||
    //     permission == geolocation.LocationPermission.deniedForever) {
    //   permission = await geolocation.Geolocator.requestPermission();
    // }
    // if (permission == geolocation.LocationPermission.deniedForever) {
    //   // 如果权限被永久拒绝，则引导用户到应用设置页面
    //   await geolocation.Geolocator.openAppSettings();
    // }
    // return permission == geolocation.LocationPermission.whileInUse ||
    //     permission == geolocation.LocationPermission.always;
    return false;
  }

  static void pirntMapValueType(Map<String, Object?> json) {
    for (var j in json.keys.toList()) {
      Object? value = json[j];

      if (value != null) {
        if (value is String) {
          print("key $j is String  $value");
        } else if (value is int) {
          print("key $j is int  $value");
        } else if (value is double) {
          print("key $j is double  $value");
        }
      } else {
        print("key $j is null");
      }
    }
  }

  ///打开网站
  static void openWebURL(String url,
      {Map<String, String>? headers,
      urllauncher.LaunchMode mode =
          urllauncher.LaunchMode.externalApplication}) async {
    final Uri toLaunch = Uri.parse(url);
    if (headers != null) {
      urllauncher.launchUrl(toLaunch,
          mode: mode,
          webViewConfiguration:
              urllauncher.WebViewConfiguration(headers: headers));
    } else {
      urllauncher.launchUrl(
        toLaunch,
        mode: mode,
      );
    }
  }

  ///拉起打电话
  static void callPhone(String phoneNumber) async {
    final Uri toLaunch = Uri(scheme: 'tel', path: phoneNumber);
    urllauncher.launchUrl(toLaunch);
  }

  static String getGuid() {
    return Uuid().v4();
  }

 

  // static Future<String> getDeviceId() async {
  //   var deviceId = await getStorage(Common.localStorageKey.deviceId, false);
  //   deviceId ??= setDeviceId();
  //   return deviceId;
  // }

  static Future<String> getDeviceId() async {
    // final deviceInfo = DeviceInfoPlugin();
    String? deviceId;
    // if (Platform.isAndroid) {
    //   final androidInfo = await deviceInfo.androidInfo;
    //   deviceId = androidInfo.id; // 设备的 Android ID
    // } else if (Platform.isIOS) {
    //   final iosInfo = await deviceInfo.iosInfo;
    //   deviceId = iosInfo.identifierForVendor; // 设备的 identifierForVendor
    // }
    // if (deviceId == null) {
      deviceId = await getStorage(Common.localStorageKey.deviceId, false);
      if (deviceId == null) {
        deviceId = getUuid();
        setStorage(Common.localStorageKey.deviceId, deviceId, false);
      }
    // }
    return deviceId;
  }

    /// 获取应用的 Bundle ID (iOS) 或 Package Name (Android)
  static Future<String> getAppBundleId() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    return packageInfo.packageName;
  }
}

enum SfaImageType { Image }
