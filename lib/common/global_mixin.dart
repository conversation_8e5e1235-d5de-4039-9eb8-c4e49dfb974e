import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:get/get.dart';

enum FormatType {
  //货币
  currency,
  //数字
  decima,
  //日期+时间
  datetime,
  //日期
  date,
  //时间
  time,
  //货币符号
  symbol,
  //货币简称
  symbolName
}

mixin GlobalMixin{
  //国际化数据类型  货币 数字
  String format(FormatType ftype, dynamic val, String? region) {
    Locale? locale = Get.locale;
    if (region != null) {
      locale = Locale("en", region);
      Intl.defaultLocale = region;
    }
    String formatValue = "";
    if (locale != null) {
      switch (ftype) {
        //货币
        case FormatType.currency:
          var currency = NumberFormat.simpleCurrency(decimalDigits: 2);
          if (val is String) {
            formatValue = currency.format(double.parse(val));
          } else {
            formatValue = currency.format(val);
          }
          break;
        //数字
        case FormatType.decima:
          var decimal = NumberFormat.decimalPattern();
          if (val is String) {
            formatValue = decimal.format(double.parse(val));
          } else {
            formatValue = decimal.format(val);
          }
          break;
        //日期+时间
        case FormatType.datetime:
          if (val is String) {
            formatValue =
                DateFormat.yMd().add_Hms().format(DateTime.parse(val));
          } else {
            formatValue = DateFormat.yMd().add_Hms().format(val as DateTime);
          }
          break;
        //日期
        case FormatType.date:
          if (val is String) {
            formatValue = DateFormat.yMd().format(DateTime.parse(val));
          } else {
            formatValue = DateFormat.yMd().format(val as DateTime);
          }
          break;
        //时间
        case FormatType.time:
          if (val is String) {
            formatValue = DateFormat.Hms().format(DateTime.parse(val));
          } else {
            formatValue = DateFormat.Hms().format(val as DateTime);
          }
          break;
        //货币符号
        case FormatType.symbol:
          formatValue = $ncc(region);
          break;
        //货币符号
        case FormatType.symbolName:
          formatValue = $ncn(region);
          break;
      }
    }
    return formatValue;
  }

  //货币格式化 带货币简称: $,￥,R$ ...
  String $ncf(String val, String? region) {
    return format(FormatType.currency, val, region);
  }

  //百分比格式化
  String $npf(val, region) {
    return "$val%";
  }

  //数字格式化
  String $nf(String val, String? region, [int digits = 2]) {
    return format(FormatType.decima, val, region);
  }

  //日期
  String $ds(String val, String? region) {
    return format(FormatType.date, val, region);
  }

  //日期时间
  String $dl(val, region) {
    return format(FormatType.datetime, val, region);
  }

  //日期时间
  String $dt(val, region) {
    return format(FormatType.time, val, region);
  }

  //货币符号 "$", "US$", or "€".
  String $ncc(region) {
    var format = NumberFormat.simpleCurrency(locale: region);
    return format.currencySymbol;
  }

  //货币简称 "BRL", "CNY", or "USD"
  String $ncn(region) {
    var format = NumberFormat.simpleCurrency(locale: region);
    if (format.currencyName != null) {
      return format.currencyName.toString();
    } else {
      return "NAN";
    }
  }
}
