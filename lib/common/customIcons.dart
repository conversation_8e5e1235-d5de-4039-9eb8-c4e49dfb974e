import 'package:flutter/material.dart';

class CustomIcons {
  static const IconData generalsurvey =
      IconData(0xe613, fontFamily: 'IconFont');
  static const IconData home = IconData(0xe614, fontFamily: 'IconFont');
  static const IconData rmrequset = IconData(0xe615, fontFamily: 'IconFont');
  static const IconData exportDB = IconData(0xe616, fontFamily: 'IconFont');
  static const IconData sync = IconData(0xe617, fontFamily: 'IconFont');
  static const IconData password = IconData(0xe618, fontFamily: 'IconFont');
  static const IconData photo = IconData(0xe619, fontFamily: 'IconFont');
  static const IconData message = IconData(0xe620, fontFamily: 'IconFont');
  static const IconData clearphoto = IconData(0xe621, fontFamily: 'IconFont');
  static const IconData setting = IconData(0xe622, fontFamily: 'IconFont');
  static const IconData messagebook = IconData(0xe61a, fontFamily: 'IconFont');
  static const IconData initsync = IconData(0xe61b, fontFamily: 'IconFont');
  static const IconData nextmont = IconData(0xe61c, fontFamily: 'IconFont');
  static const IconData adminProfile = IconData(0xe61d, fontFamily: 'IconFont');
  static const IconData synclog = IconData(0xe61e, fontFamily: 'IconFont');
  static const IconData poposmstatus = IconData(0xe61f, fontFamily: 'IconFont');
  // store icon
  static const IconData attribute = IconData(0xe630, fontFamily: 'IconFont');
  static const IconData general = IconData(0xe631, fontFamily: 'IconFont');
  static const IconData traxphoto = IconData(0xe632, fontFamily: 'IconFont');
  static const IconData po = IconData(0xe633, fontFamily: 'IconFont');
  static const IconData dashboard = IconData(0xe634, fontFamily: 'IconFont');
  static const IconData nextstep = IconData(0xe635, fontFamily: 'IconFont');
  static const IconData notes = IconData(0xe636, fontFamily: 'IconFont');
  static const IconData adhoc = IconData(0xe637, fontFamily: 'IconFont');
  static const IconData time = IconData(0xe638, fontFamily: 'IconFont');
  static const IconData elibrary = IconData(0xe639, fontFamily: 'IconFont');
  static const IconData grallery = IconData(0xe63a, fontFamily: 'IconFont');
  static const IconData profile = IconData(0xe63b, fontFamily: 'IconFont');
  static const IconData chq = IconData(0xe63c, fontFamily: 'IconFont');
  static const IconData rmphoto = IconData(0xe63f, fontFamily: 'IconFontNew');
  static const IconData rmaddphoto = IconData(0xe63e, fontFamily: 'IconFontNew');
  // 添加更多图标...

  // 使用一个Map来获取图标数据
  static const Map<String, IconData> iconMap = {
    // 添加更多图标...
    'generalsurvey': generalsurvey,
    'home': home,
    'rmrequset': rmrequset,
    'exportDB': exportDB,
    'sync': sync,
    'password': password,
    'photo': photo,
    'message': message,
    'clearphoto': clearphoto,
    'setting': setting,
    'messagebook': messagebook,
    'initsync': initsync,
    'nextmont': nextmont,
    'adminProfile': adminProfile,
    'synclog': synclog,
    'poposmstatus': poposmstatus,
    'attribute': attribute,
    'general': general,
    'traxphoto': traxphoto,
    'po': po,
    'dashboard': dashboard,
    'nextstep': nextstep,
    'notes': notes,
    'adhoc': adhoc,
    'time': time,
    'elibrary': elibrary,
    'grallery': grallery,
    'profile': profile,
    'chq': chq,
    'rmphoto': rmphoto,
    'rmaddphoto': rmaddphoto,
  };
}
