import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import '../../common/sfa_exception.dart';

//全局异常的捕捉
class AppCatchError {
  run(Widget app) {
    ///Flutter 框架异常
    FlutterError.onError = (FlutterErrorDetails details) async {
      ///线上环境
      if (kReleaseMode) {
        //可以将错误日志上传到云
        // Zone.current.handleUncaughtError(details.exception, details.stack!);
      } else {
        //开发期间 print
        FlutterError.dumpErrorToConsole(details);
      }
    };
    runZonedGuarded(() {
      runApp(app);
    }, (error, stack) => catchError(error, stack));
  }

  ///对搜集的 异常进行处理  上报等
  catchError(Object error, StackTrace stack) async {
     print("SfaException ${ error.toString()} StackTrace ${stack}");
    if (error is SfaException) {
      SfaException ex = error;
      switch (ex.errorType) {
        case SfaErrorType.login: 
        case SfaErrorType.httpError:
        case SfaErrorType.initialSync:
        case SfaErrorType.exportDB:
        case SfaErrorType.configData:
        case SfaErrorType.dbSchema:
        case SfaErrorType.dataDownload:
        case SfaErrorType.updateSyncSuccess:
        case SfaErrorType.changePassword:
        case SfaErrorType.downloadTable:
        case SfaErrorType.uploadData:
        case SfaErrorType.locationException:
        case SfaErrorType.getOnlineData:
        case SfaErrorType.syncProcessing:
        case SfaErrorType.locationPermissionDenied:
        case SfaErrorType.locationServiceEnabled:
          
          break;  
      }
      
    } else if (error is DioException) {
     
    } else {
      
    }
  }
}
