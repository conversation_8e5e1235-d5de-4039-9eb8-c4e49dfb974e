import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

///国际化
class Sfai18n {
  static String _format(I18nType ftype, dynamic val, [String? region]) {
    Locale? locale = Get.locale;
    if (region != null) {
      locale = Locale("zh", region);
      Intl.defaultLocale = region;
    }
    String formatValue = "";
    if (locale != null) {
      switch (ftype) {
        //货币
        case I18nType.currency:
          var currency = NumberFormat.simpleCurrency(decimalDigits: 2);
          if (val is String) {
            formatValue = currency.format(double.parse(val));
          } else {
            formatValue = currency.format(val);
          }
          break;
        //数字
        case I18nType.decima:
          var decimal = NumberFormat.decimalPattern();
          if (val is String) {
            formatValue = decimal.format(double.parse(val));
          } else {
            formatValue = decimal.format(val);
          }
          break;
        //日期+时间
        case I18nType.datetime:
          if (val is String) {
            formatValue =
                DateFormat.yMd().add_Hms().format(DateTime.parse(val));
          } else {
            formatValue = DateFormat.yMd().add_Hms().format(val as DateTime);
          }
          break;
        //日期
        case I18nType.date:
          if (val is String) {
            formatValue = DateFormat.yMd().format(DateTime.parse(val));
          } else {
            formatValue = DateFormat.yMd().format(val as DateTime);
          }
          break;
        //时间
        case I18nType.time:
          if (val is String) {
            formatValue = DateFormat.Hms().format(DateTime.parse(val));
          } else {
            formatValue = DateFormat.Hms().format(val as DateTime);
          }
          break;
        //货币符号
        case I18nType.symbol:
          formatValue = $ncc(region);
          break;
        //货币符号
        case I18nType.symbolName:
          formatValue = $ncn(region);
          break;
      }
    }
    return formatValue;
  }

  //货币符号
  static String symbol() {
    return _format(I18nType.symbol, "");
  }

  //货币格式化 带货币简称: $,￥,R$ ...
  static String $ncf(String val, [String? region]) {
    return _format(I18nType.currency, val, region);
  }

  //百分比格式化
  static String $npf(val, [String? region]) {
    return "$val%";
  }
  
  //数字格式化
  static String $nf(String val, [String? region, int digits = 2]) {
    return _format(I18nType.decima, val, region);
  }

  //日期
  static String $ds(String val, [String? region]) {
    return _format(I18nType.date, val, region);
  }

  //日期时间
  static String $dl(val, [String? region]) {
    return _format(I18nType.datetime, val, region);
  }

  //日期时间
  static String $dt(val, [String? region]) {
    return _format(I18nType.time, val, region);
  }

  //货币符号 "$", "US$", or "€".
  static String $ncc([String? region]) {
    var format = NumberFormat.simpleCurrency(locale: region);
    return format.currencySymbol;
  }

  //货币简称 "BRL", "CNY", or "USD"
  static String $ncn([String? region]) {
    var format = NumberFormat.simpleCurrency(locale: region);
    if (format.currencyName != null) {
      return format.currencyName.toString();
    } else {
      return "NAN";
    }
  }
}



//国际化格式化类型
enum I18nType {
  ///货币
  currency,
  ///数字
  decima,
  ///日期+时间
  datetime,
  ///日期
  date,
  ///时间
  time,
  ///货币符号
  symbol,
  ///货币简称
  symbolName
}