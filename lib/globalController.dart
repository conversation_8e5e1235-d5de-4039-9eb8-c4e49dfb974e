// global_controller.dart
import 'dart:async'; 
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'common/sfa_event_bus/sfa_event_bus.dart'; 

import 'package:jpush_flutter/jpush_flutter.dart';
import 'package:jpush_flutter/jpush_interface.dart';

import 'common/util.dart';
import 'common/jpush_manager.dart';

class GlobalEvent {
  // 定义事件的属性和方法
}

// ... 全局状态数据和方法,例如message未读信息数量 elibray未下载照片数量等
class GlobalController extends GetxController {
  // 移除原有 JPush 相关成员
  RxInt unUpload = 0.obs;
   
  @override
  void onInit() {
    super.onInit();
    initPlatformState();
    print('GlobalController onInit');
  }
  
  GlobalController() {
    eventBus.on<GlobalEvent>().listen((event) {
      // 在这里执行全局方法
      getSyncStatus();
    });
  }
  StreamController<void> eventStreamController =
      StreamController<void>.broadcast();

  Future<void> initPlatformState() async {
    try {
      // 这里可以自定义 deviceAlias 的生成方式
      // String deviceAlias = "Code_1234567890"; // 或 await Util.getDeviceId();
      await JPushManager().init(
        appKey: "bfbf7371ee66eee8e5b04977",
        channel: "developer-default",
        production: false,
        debug: true
      );
    } catch (e) {
      print("[GlobalController] JPush 初始化失败: $e");
    }
  }
  //获取同步状态
  getSyncStatus() async {
    
  }

  clearEventBus() { 
     
  }
}
