import 'package:flutter/material.dart';
import '../them/base_them.dart';

class SfaAppBar extends StatelessWidget implements PreferredSizeWidget {
  final double height;
  final bool showAppBar;
  final Widget? title;
  final List<Widget>? actions;
  final Widget? leading;

  const SfaAppBar({
    Key? key,
    this.height = 35,
    this.showAppBar = true,
    this.title,
    this.actions,
    this.leading,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(height),
      child: Stack(
        children: [
          Container(
            width: double.infinity,
            height: height,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage("${ThemeManager.currentTheme.assetsPath}nav.png"),
                fit: BoxFit.cover,
              ),
            ),
          ),
          if (showAppBar)
            AppBar(
              elevation: 0,
              title: title,
              actions: actions,
              leading: leading,
              toolbarHeight: height,
            ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height);
}