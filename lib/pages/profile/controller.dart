import 'package:get/get.dart';
import 'package:flutter/material.dart';

/// 个人中心控制器
class ProfileController extends GetxController {
  // 用户信息
  final user = <String, dynamic>{
    'name': '张经理',
    'position': '高级销售经理 | 华东大区',
    'id': '10086',
    'avatar': 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
    'customerCount': 128,
    'opportunityCount': 42,
    'rank': 1,
  }.obs;

  // 本月业绩完成度
  final achievement = {
    'progress': 0.82,
    'completed': '¥82万',
    'target': '¥100万',
    'daysLeft': 12,
  }.obs;

  // 功能模块
  final modules = [
    {'icon': Icons.show_chart, 'color': Colors.pink, 'label': '我的业绩'},
    {'icon': Icons.emoji_events, 'color': Colors.blue, 'label': '销售排行'},
    {'icon': Icons.assignment, 'color': Colors.green, 'label': '待办事项'},
    {'icon': Icons.receipt_long, 'color': Colors.purple, 'label': '佣金明细'},
  ];

  // 最近跟进记录
  final recentFollowUps = [
    {
      'customer': '上海万科集团',
      'time': '今天 10:30',
      'desc': '客户拜访：与张总监沟通智能办公设备解决方案。',
      'type': '客户拜访',
    },
    {
      'customer': '北京朗科科技',
      'time': '昨天 14:30',
      'desc': '电话沟通：确认IT基础设施更新项目进展，安排下周现场演示。',
      'type': '电话沟通',
    },
    {
      'customer': '广州佳能打印',
      'time': '3天前',
      'desc': '邮件沟通：发送最终合同版本，客户已确认，等待签署。',
      'type': '邮件沟通',
    },
  ];

  // 我的团队
  final team = [
    {
      'name': '李销售',
      'avatar': 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
      'role': '销售顾问',
      'progress': 0.86,
      'completed': '¥85.2万',
      'target': '¥100万',
    },
    {
      'name': '王销售',
      'avatar': 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
      'role': '销售顾问',
      'progress': 0.72,
      'completed': '¥72万',
      'target': '¥100万',
    },
  ];
} 