import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../common/screen_adapter.dart';
import '../../them/base_them.dart';
import '../../router/routers_names.dart';
import 'index.dart';

class HomePage extends GetView<HomeController> {
  const HomePage({super.key});

  // 构建轮播图
  Widget _buildCarousel() {
    return SizedBox(
      height: ScreenAdapter.height(350),
      child: Stack(
        children: [
          // 轮播图页面
          PageView.builder(
            controller: controller.carouselController,
            onPageChanged: (index) {
              controller.currentCarouselIndex.value = index;
            },
            itemCount: controller.carouselImages.length,
            itemBuilder: (context, index) {
              return Container(
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: NetworkImage(controller.carouselImages[index]),
                    fit: BoxFit.cover,
                  ),
                ),
              );
            },
          ),
          // 指示器
          Positioned(
            bottom: 20,
            left: 0,
            right: 0,
            child: Obx(() => Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                controller.carouselImages.length,
                (index) => Container(
                  width: 8,
                  height: 8,
                  margin: const EdgeInsets.only(right: 5),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: controller.currentCarouselIndex.value == index
                        ? Colors.red
                        : Colors.white,
                  ),
                ),
              ),
            )),
          ),
        ],
      ),
    );
  }

  // 构建切换按钮
  Widget _buildToggleButton(String text, bool isActive) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 6),
      decoration: BoxDecoration(
        color: isActive ? ThemeManager.currentTheme.primaryColor : Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isActive ? ThemeManager.currentTheme.primaryColor: Colors.grey[300]!,
          width: 1.5,
        ),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: isActive ? Colors.white : Colors.grey[600],
          fontWeight: FontWeight.w500,
          fontSize: 13,
        ),
      ),
    );
  }

  // 构建角色按钮
  Widget _buildRoleButton(String text, bool isActive) {
    return Container(
      margin: const EdgeInsets.only(right: 24),
      child: Column(
        children: [
          Text(
            text.tr,
            style: TextStyle(
              color: isActive ? ThemeManager.currentTheme.primaryColor : Colors.grey[500],
              fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 6),
          Container(
            width: 24,
            height: 2,
            decoration: BoxDecoration(
              color: isActive ? ThemeManager.currentTheme.primaryColor : Colors.transparent,
              borderRadius: BorderRadius.circular(1),
            ),
          ),
        ],
      ),
    );
  }

  // 构建统计数据区域
  Widget _buildStatistics() {
    return Container(
      padding: const EdgeInsets.fromLTRB(15, 20, 15, 15),
      child: GridView.builder(
        shrinkWrap: true, // 自适应高度
        physics: const NeverScrollableScrollPhysics(), // 禁止滚动
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2, // 每行2个
          mainAxisSpacing: 15, // 垂直间距
          crossAxisSpacing: 15, // 水平间距
          childAspectRatio: 0.9, // 宽高比
        ),
        itemCount: 14, // 统计项数量
        itemBuilder: (context, index) {
          // 根据索引返回不同的统计项
          switch (index % 3) {
            case 0:
              return _buildStatisticItem(
                "home.call_completion_rate".tr,
                0.85,
                0.92,
              );
            case 1:
              return _buildStatisticItem(
                "home.strike_rate".tr,
                0.75,
                0.80,
              );
            default:
              return _buildStatisticItem(
                "home.target".tr,
                0.65,
                0.70,
              );
          }
        },
      ),
    );
  }

// 构建统计数据项
  Widget _buildStatisticItem(String title, double value, double target) {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 标题文本优化
          Flexible(
            child: Text(
              title.tr,
              style: TextStyle(
                fontSize: 12,  // 减小字号
                color: Colors.grey[700],
                fontWeight: FontWeight.w500,
                height: 1.2,  // 添加行高控制
              ),
              overflow: TextOverflow.ellipsis,  // 文本溢出显示省略号
              maxLines: 2,  // 最多显示2行
            ),
          ),
          const SizedBox(height: 12),  // 减小间距
          // 中间进度指示器
          Center(
            child: Stack(
              alignment: Alignment.center,
              children: [
                SizedBox(
                  width: 80,
                  height: 80,
                  child: CircularProgressIndicator(
                    value: value,
                    backgroundColor: Colors.grey[100],
                    valueColor: AlwaysStoppedAnimation<Color>(ThemeManager.currentTheme.primaryColor),
                    strokeWidth: 8,
                  ),
                ),
                Text(
                  "${(value * 100).toInt()}%",
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.w600,
                    color: ThemeManager.currentTheme.primaryColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),  // 减小间距
          // 底部目标值
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Target",
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 12,  // 减小字号
                ),
              ),
              Text(
                "${(target * 100).toInt()}%",
                style: const TextStyle(
                  fontSize: 12,  // 减小字号
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  // 主视图
  Widget _buildView() {
    return Container(
      color: Colors.white,
      child: CustomScrollView(
        slivers: [
          // 轮播图
          SliverToBoxAdapter(
            child: _buildCarousel(),
          ), 
          // MTD/DTD 吸顶区域
          SliverPersistentHeader(
            pinned: true, // 设置吸顶
            delegate: _StickyTabBarDelegate(
              child: Container(
                color: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                child: Obx(() => Row(
                  children: [
                    _buildToggleButton("MTD", controller.isMTD.value),
                    const SizedBox(width: 10),
                    _buildToggleButton("DTD", !controller.isMTD.value),
                  ],
                )),
              ),
            ),
          ),
          // 角色选择栏和统计数据
          SliverToBoxAdapter(
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Obx(() => Row(
                      children: controller.roles
                          .map((role) => _buildRoleButton(
                                role.name,
                                role.code == controller.selectedRole.value,
                              ))
                          .toList(),
                    )),
                  ),
                ),
                _buildStatistics(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(
      init: HomeController(),
      id: "home",
      builder: (_) {
        return Scaffold(
          body: SafeArea(
            child: _buildView(),
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: (){
              
            },
            backgroundColor: ThemeManager.currentTheme.primaryColor,
            child: const Icon(Icons.route),
          ),
        );
      },
    );
  }
}
  // 吸顶代理类
  class _StickyTabBarDelegate extends SliverPersistentHeaderDelegate {
    final Widget child;

    _StickyTabBarDelegate({
      required this.child,
    });

    @override
    Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
      return child;
    }

    @override
    double get maxExtent => 50; // 最大高度

    @override
    double get minExtent => 50; // 最小高度

    @override
    bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
      return true;
    }
  }

