import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'dart:async';

import '../../common/customIcons.dart'; 

class HomeController extends GetxController {
  // 轮播图当前索引
  final currentCarouselIndex = 0.obs;
  // 轮播图控制器
  late PageController carouselController;
  // 轮播图数据
  final List<String> carouselImages = [
    'https://images.unsplash.com/photo-1554866585-cd94860890b7?w=800', // 可口可乐瓶特写
    'https://images.unsplash.com/photo-1622483767028-3f66f32aef97?w=800', // 冰镇可乐
    'https://images.unsplash.com/photo-1567103472667-6898f3a79cf2?w=800', // 经典可乐罐
  ];
  // 定时器
  Timer? _timer;

  // 功能按钮配置
  final List<FunctionButton> functionButtons = [
    FunctionButton(
      title: 'home.new_customers',
      icon: CustomIcons.adhoc,
      onTap: () => Get.toNamed('/customers/new'),
    ),
    FunctionButton(
      title: 'home.library',
      icon: CustomIcons.adminProfile,
      onTap: () => Get.toNamed('/library'),
    ),
    FunctionButton(
      title: 'home.task_center',
      icon: CustomIcons.clearphoto,
      onTap: () => Get.toNamed('/tasks'),
    ),
    FunctionButton(
      title: 'home.more_content',
      icon: CustomIcons.general,
      onTap: () => Get.toNamed('/more'),
    ),
  ];

  // 角色列表
  final List<RoleItem> roles = [
    RoleItem(code: 'SALES', name: 'home.role.sales'),
    RoleItem(code: 'SFE', name: 'home.role.sfe'),
    RoleItem(code: 'RED', name: 'home.role.red'),
    RoleItem(code: 'AO', name: 'home.role.ao'),
    RoleItem(code: 'CMA', name: 'home.role.cma'),
    RoleItem(code: 'CI', name: 'home.role.ci'),
  ];

  // 当前选中的角色
  final selectedRole = 'SALES'.obs;
  
  // MTD/DTD 切换状态
  final isMTD = true.obs;

  @override
  void onInit() {
    super.onInit();
    carouselController = PageController();
    _startAutoPlay();
  }

  // 开始自动播放
  void _startAutoPlay() {
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (currentCarouselIndex.value < carouselImages.length - 1) {
        currentCarouselIndex.value++;
      } else {
        currentCarouselIndex.value = 0;
      }
      carouselController.animateToPage(
        currentCarouselIndex.value,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    });
  }

  @override
  void onClose() {
    _timer?.cancel();
    carouselController.dispose();
    super.onClose();
  }

  void onOrderTap() {
    // TODO: 实现订单点击事件
  }

  void onNewCustomersTap() {
    // TODO: 实现新客户点击事件
  }

  void onLibraryTap() {
    // TODO: 实现资料库点击事件
  }

  void onTaskCenterTap() {
    // TODO: 实现任务中心点击事件
  }

  void onMoreContentTap() {
    // TODO: 实现更多内容点击事件
  }
}


class RoleItem {
  final String code;
  final String name;

  RoleItem({
    required this.code, 
    required this.name,
  });
}

class FunctionButton {
  final String title;
  final IconData icon;
  final VoidCallback onTap;

  FunctionButton({
    required this.title,
    required this.icon,
    required this.onTap,
  });
}