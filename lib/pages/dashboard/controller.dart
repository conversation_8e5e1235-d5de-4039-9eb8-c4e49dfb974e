import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:math';

import '../../common/fin_clp_manager.dart';
import '../app_home_widget/home_widget1/index.dart';

/// Dashboard 控制器
/// 管理工作台页面的数据和状态
class DashboardController extends GetxController {
  // 控制状态
  late PageController carouselController;
  final currentCarouselIndex = 0.obs;
  final isMTD = true.obs; // 默认显示MTD(Month To Date)数据
  final isWeeklyView = true.obs; // 默认显示周视图

  // 用户信息
  final userInfo = UserInfo(
    name: '张经理',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
  ).obs;

  // 销售目标数据
  final salesTarget = SalesTarget(
    targetAmount: 1000000,
    completedAmount: 680000,
    daysRemaining: 12,
    completionRate: 0.68,
  ).obs;

  // 今日任务列表
  final todayTasks = <Task>[].obs;

  // 快捷操作列表
  final quickActions = <QuickAction>[].obs;

  // 拜访效率数据
  final visitEfficiency = VisitEfficiency(
    labels: ['周一', '周二', '周三', '周四', '周五'],
    plannedVisits: [4, 5, 3, 6, 4],
    actualVisits: [3, 5, 2, 4, 3],
  ).obs;

  // 拜访成效数据
  final visitEffectiveness = VisitEffectiveness(
    dealClosed: 35,
    inProgress: 50,
    notInterested: 15,
  ).obs;

  // 最近活动列表
  final recentActivities = <Activity>[].obs;

  @override
  void onInit() {
    super.onInit();
    carouselController = PageController();
    _generateTestData();
    _startAutoPlay();
  }

  @override
  void onClose() {
    carouselController.dispose();
    super.onClose();
  }

  /// 生成测试数据
  void _generateTestData() {
    // 生成20条测试数据
    
    // 1. 生成今日任务数据
    todayTasks.value = _generateTasks();
    
    // 2. 生成快捷操作数据
    quickActions.value = [
       QuickAction(
        title: 'sfa.data_dashboard',
        icon: Icons.dashboard_outlined,
        color: const Color(0xFFFF9500),
        onTap: () {
          FinClpManager.openApplet("fc2615029311976901");
        }
      ),
      QuickAction(
        title: 'sfa.new_order',
        icon: Icons.shopping_cart_outlined,
        color: const Color(0xFFFE2C55),
        onTap: (){
          
        },
      ),
      QuickAction(
        title: 'sfa.customer_management',
        icon: Icons.people_outline,
        color: const Color(0xFF1876F2),
        onTap: () => Get.toNamed('/customers'),
      ),
      QuickAction(
        title: 'sfa.visit_planning',
        icon: Icons.map_outlined,
        color: const Color(0xFF07C160),
        onTap: () => Get.toNamed('/visits'),
      ),
     
    ];
    
    // 3. 生成最近活动数据
    recentActivities.value = _generateActivities();
  }

  /// 生成任务测试数据
  List<Task> _generateTasks() {
    final random = Random();
    final taskTypes = ['sfa.visit_customer', 'sfa.collect_payment', 'sfa.submit_report'];
    final customerNames = [
      '星巴克咖啡厅', '全家便利店', '喜茶', '麦当劳', '肯德基',
      '瑞幸咖啡', '奈雪的茶', '85度C', '沃尔玛', '全家福超市'
    ];
    
    final List<Task> tasks = [];
    for (int i = 0; i < 5; i++) {
      final type = taskTypes[random.nextInt(taskTypes.length)];
      final customer = customerNames[random.nextInt(customerNames.length)];
      final completed = random.nextBool();
      final deadline = DateTime.now().add(Duration(hours: random.nextInt(8)));
      
      tasks.add(Task(
        id: 'TASK-${100 + i}',
        type: type,
        customer: customer,
        completed: completed,
        deadline: deadline,
      ));
    }
    return tasks;
  }

  /// 生成活动测试数据
  List<Activity> _generateActivities() {
    final random = Random();
    final activityTypes = ['visit', 'order', 'payment', 'return'];
    final customerNames = [
      '星巴克咖啡厅', '全家便利店', '喜茶', '麦当劳', '肯德基',
      '瑞幸咖啡', '奈雪的茶', '85度C', '沃尔玛', '全家福超市'
    ];
    final amounts = [1200.0, 2500.0, 5000.0, 8000.0, 10000.0, 15000.0, 20000.0];
    
    final List<Activity> activities = [];
    for (int i = 0; i < 15; i++) {
      final type = activityTypes[random.nextInt(activityTypes.length)];
      final customer = customerNames[random.nextInt(customerNames.length)];
      final amount = type == 'visit' ? null : amounts[random.nextInt(amounts.length)];
      final time = DateTime.now().subtract(Duration(hours: random.nextInt(72)));
      
      activities.add(Activity(
        id: 'ACT-${1000 + i}',
        type: type,
        customer: customer,
        amount: amount,
        time: time,
      ));
    }
    return activities;
  }

  /// 开始轮播图自动播放
  void _startAutoPlay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (carouselController.hasClients) {
        int nextPage = currentCarouselIndex.value + 1;
        if (nextPage >= bannerList.length) {
          nextPage = 0;
        }
        
        carouselController.animateToPage(
          nextPage,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        _startAutoPlay();
      }
    });
  }

  /// 切换 MTD/DTD 视图
  void toggleMTDView(bool value) {
    isMTD.value = value;
  }

  /// 切换周/日视图
  void toggleWeeklyView(bool value) {
    isWeeklyView.value = value;
  }

  /// 根据当前时间返回问候语 key
  String getGreetingKey() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'sfa.good_morning';
    } else if (hour < 18) {
      return 'sfa.good_afternoon';
    } else {
      return 'sfa.good_evening';
    }
  }

  // 处理原生端刷新请求
Future<void> updateWidgetData() async {
   
    // 生成随机数据并刷新 widget
    final random = DateTime.now().millisecondsSinceEpoch;
    final data = WidgetDataModel(
      userName: '张经理',
      updateTime: DateTime.now(),
      todayVisitPlanned: 5 + random % 6,
      todayVisitCompleted: random % 6,
      visitCompletionRate: (random % 100) / 100.0,
      salesTarget: 1000000,
      salesCompleted: (random % 1000000).toDouble(),
      salesCompletionRate: (random % 100) / 100.0,
      daysRemaining: 12,
      todayOrders: random % 20,
      weeklyNewCustomers: random % 10,
      pendingTasks: random % 8,
    );
    await SfaWidgetService.updateWidgetData(data);
}

}

/// Banner数据
final List<BannerItem> bannerList = [
  BannerItem(
    imageUrl: 'https://images.unsplash.com/photo-1554866585-cd94860890b7?w=800',
    title: '可口可乐夏季促销',
    link: '/promotion/summer',
  ),
  BannerItem(
    imageUrl: 'https://images.unsplash.com/photo-1622483767028-3f66f32aef97?w=800',
    title: '新品上市：零度可乐',
    link: '/product/zero',
  ),
  BannerItem(
    imageUrl: 'https://images.unsplash.com/photo-1567103472667-6898f3a79cf2?w=800',
    title: '经典回顾：可口可乐125周年',
    link: '/brand/anniversary',
  ),
];

/// 用户信息模型
class UserInfo {
  final String name;
  final String avatar;

  UserInfo({
    required this.name,
    required this.avatar,
  });
}

/// 销售目标模型
class SalesTarget {
  final double targetAmount;
  final double completedAmount;
  final int daysRemaining;
  final double completionRate;

  SalesTarget({
    required this.targetAmount,
    required this.completedAmount,
    required this.daysRemaining,
    required this.completionRate,
  });
}

/// 任务模型
class Task {
  final String id;
  final String type;
  final String customer;
  final bool completed;
  final DateTime deadline;

  Task({
    required this.id,
    required this.type,
    required this.customer,
    required this.completed,
    required this.deadline,
  });
}

/// 快捷操作模型
class QuickAction {
  final String title;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  QuickAction({
    required this.title,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}

/// 拜访效率模型
class VisitEfficiency {
  final List<String> labels;
  final List<int> plannedVisits;
  final List<int> actualVisits;

  VisitEfficiency({
    required this.labels,
    required this.plannedVisits,
    required this.actualVisits,
  });
}

/// 拜访成效模型
class VisitEffectiveness {
  final int dealClosed;
  final int inProgress;
  final int notInterested;

  VisitEffectiveness({
    required this.dealClosed,
    required this.inProgress,
    required this.notInterested,
  });
}

/// 活动模型
class Activity {
  final String id;
  final String type;
  final String customer;
  final double? amount;
  final DateTime time;

  Activity({
    required this.id,
    required this.type,
    required this.customer,
    this.amount,
    required this.time,
  });
}

/// Banner模型
class BannerItem {
  final String imageUrl;
  final String title;
  final String link;

  BannerItem({
    required this.imageUrl,
    required this.title,
    required this.link,
  });
} 