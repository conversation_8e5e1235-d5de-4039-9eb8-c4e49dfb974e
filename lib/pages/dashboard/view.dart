import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../them/base_them.dart';
import '../../i10n/i10n.dart';
import 'controller.dart';

/// Dashboard 页面
class DashboardPage extends GetView<DashboardController> {
  const DashboardPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return 
        GetBuilder<DashboardController>(
      init: DashboardController(),
      id: "dashboard",
      builder: (_) {
        return 
     Scaffold(
      backgroundColor: const Color(0xFFF6F6F6),
      body: SafeArea(
        child: Column(
          children: [
            _buildAppBar(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildWelcomeSection(),
                    _buildSalesTargetCard(),
                    _buildTodayTasksCard(),
                    _buildQuickActionsGrid(),
                    _buildSalesPerformanceSection(),
                    _buildRecentActivitiesCard(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          controller.updateWidgetData();
        },
        child:const Icon(Icons.refresh),
      ),
      );
      }
    );
  }

  /// 构建顶部导航栏
  Widget _buildAppBar() {
    return Container(
      height: 50.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 2,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            I10n.of('sfa.dashboard'),
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: ThemeManager.currentTheme.primaryColor,
            ),
          ),
          Stack(
            children: [
              IconButton(
                icon: Icon(
                  Icons.notifications_outlined,
                  size: 24.sp,
                  color: Colors.grey[600],
                ),
                onPressed: () {},
              ),
              Positioned(
                top: 8.h,
                right: 8.w,
                child: Container(
                  width: 18.w,
                  height: 18.h,
                  decoration: BoxDecoration(
                    color: ThemeManager.currentTheme.primaryColor,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      '3',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
             
            ],
          ),
        ],
      ),
    );
  }

  /// 构建欢迎区域
  Widget _buildWelcomeSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(28.r),
            child: Image.network(
              controller.userInfo.value.avatar,
              width: 56.w,
              height: 56.h,
              fit: BoxFit.cover,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Obx(() => Text(
                  '${controller.userInfo.value.name}，${I10n.of(controller.getGreetingKey())}!',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                  ),
                )),
                SizedBox(height: 4.h),
                Text(
                  I10n.of('sfa.good_day_orders'),
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建销售目标卡片
  Widget _buildSalesTargetCard() {
    final formatCurrency = NumberFormat.currency(
      locale: 'zh_CN',
      symbol: '¥',
      decimalDigits: 0,
    );
    
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        elevation: 2,
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    I10n.of('sfa.monthly_sales_target'),
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Obx(() => Text(
                    '${I10n.of('sfa.days_remaining')} ${controller.salesTarget.value.daysRemaining}',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                  )),
                ],
              ),
              SizedBox(height: 16.h),
              Row(
                children: [
                  _buildCircularProgress(),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              I10n.of('sfa.completed_amount'),
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                            Obx(() => Text(
                              formatCurrency.format(controller.salesTarget.value.completedAmount),
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                                color: ThemeManager.currentTheme.primaryColor,
                              ),
                            )),
                          ],
                        ),
                        SizedBox(height: 8.h),
                        Obx(() => ClipRRect(
                          borderRadius: BorderRadius.circular(3.r),
                          child: LinearProgressIndicator(
                            value: controller.salesTarget.value.completionRate,
                            backgroundColor: Colors.grey[200],
                            valueColor: AlwaysStoppedAnimation<Color>(
                              ThemeManager.currentTheme.primaryColor,
                            ),
                            minHeight: 6.h,
                          ),
                        )),
                        SizedBox(height: 8.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              I10n.of('sfa.target_amount'),
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                            Obx(() => Text(
                              formatCurrency.format(controller.salesTarget.value.targetAmount),
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            )),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建圆形进度指示器
  Widget _buildCircularProgress() {
    return SizedBox(
      width: 100.w,
      height: 100.h,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            width: 80.w,
            height: 80.h,
            child: Obx(() => CircularProgressIndicator(
              value: controller.salesTarget.value.completionRate,
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(
                ThemeManager.currentTheme.primaryColor,
              ),
              strokeWidth: 8.w,
            )),
          ),
          Obx(() => Text(
            '${(controller.salesTarget.value.completionRate * 100).toInt()}%',
            style: TextStyle(
              fontSize: 22.sp,
              fontWeight: FontWeight.w600,
              color: ThemeManager.currentTheme.primaryColor,
            ),
          )),
        ],
      ),
    );
  }

  /// 构建今日任务卡片
  Widget _buildTodayTasksCard() {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        elevation: 2,
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    I10n.of('sfa.today_tasks'),
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  GestureDetector(
                    onTap: () {},
                    child: Text(
                      I10n.of('sfa.view_all'),
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: ThemeManager.currentTheme.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              Obx(() => Column(
                children: controller.todayTasks
                    .take(3) // 只显示前3个任务
                    .map((task) => _buildTaskItem(task))
                    .toList(),
              )),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建任务项
  Widget _buildTaskItem(Task task) {
    final IconData taskIcon;
    
    switch (task.type) {
      case 'sfa.visit_customer':
        taskIcon = Icons.people_outline;
        break;
      case 'sfa.collect_payment':
        taskIcon = Icons.payment_outlined;
        break;
      case 'sfa.submit_report':
        taskIcon = Icons.description_outlined;
        break;
      default:
        taskIcon = Icons.check_circle_outline;
    }
    
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.h,
            decoration: BoxDecoration(
              color: task.completed 
                  ? Colors.green.withOpacity(0.1)
                  : Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              taskIcon,
              color: task.completed ? Colors.green : Colors.blue,
              size: 20.sp,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  I10n.of(task.type),
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  task.customer,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: task.completed
                  ? Colors.green.withOpacity(0.1)
                  : ThemeManager.currentTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              task.completed 
                  ? I10n.of('visited')
                  : DateFormat('HH:mm').format(task.deadline),
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
                color: task.completed
                    ? Colors.green
                    : ThemeManager.currentTheme.primaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建快捷操作网格
  Widget _buildQuickActionsGrid() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            I10n.of('sfa.quick_actions'),
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 12.h),
          Obx(() => GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 12.w,
              mainAxisSpacing: 12.h,
              childAspectRatio: 0.8,
            ),
            itemCount: controller.quickActions.length,
            itemBuilder: (context, index) {
              final action = controller.quickActions[index];
              return _buildActionItem(action);
            },
          )),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  /// 构建操作项
  Widget _buildActionItem(QuickAction action) {
    return GestureDetector(
      onTap: action.onTap,
      child: Column(
        children: [
          Container(
            width: 56.w,
            height: 56.h,
            decoration: BoxDecoration(
              color: action.color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Icon(
              action.icon,
              color: action.color,
              size: 28.sp,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            I10n.of(action.title),
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  } 

  /// 构建销售业绩区域
  Widget _buildSalesPerformanceSection() {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            I10n.of('sfa.sales_performance'),
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16.h),
          IntrinsicHeight(
            child: Row(
              children: [
                Expanded(child: _buildVisitEfficiencyCard()),
                SizedBox(width: 12.w),
                Expanded(child: _buildVisitEffectivenessCard()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建拜访效率卡片
  Widget _buildVisitEfficiencyCard() {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      elevation: 2,
      child: Container(
        height: double.infinity,
        padding: EdgeInsets.all(12.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              I10n.of('sfa.visit_efficiency'),
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            SizedBox(
              height: 180.h,
              child: LayoutBuilder(
                builder: (context, constraints) {
                  // Calculate appropriate bar width based on available width
                  final availableWidth = constraints.maxWidth;
                  final barWidth = (availableWidth / (controller.visitEfficiency.value.labels.length * 2.5)).clamp(8.0, 12.0);
                  
                  return Obx(() => BarChart(
                    BarChartData(
                      alignment: BarChartAlignment.spaceAround,
                      barTouchData: BarTouchData(enabled: false),
                      titlesData: FlTitlesData(
                        show: true,
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            getTitlesWidget: (value, meta) {
                              if (value < 0 || value >= controller.visitEfficiency.value.labels.length) {
                                return const SizedBox.shrink();
                              }
                              return Padding(
                                padding: EdgeInsets.only(top: 8.h),
                                child: Text(
                                  controller.visitEfficiency.value.labels[value.toInt()],
                                  style: TextStyle(
                                    fontSize: 10.sp,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              );
                            },
                            reservedSize: 30,
                          ),
                        ),
                        leftTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            getTitlesWidget: (value, meta) {
                              if (value == 0) return const SizedBox.shrink();
                              return Padding(
                                padding: EdgeInsets.only(right: 8.w),
                                child: Text(
                                  value.toInt().toString(),
                                  style: TextStyle(
                                    fontSize: 10.sp,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              );
                            },
                            reservedSize: 20,
                          ),
                        ),
                        rightTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                        topTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                      ),
                      gridData: FlGridData(
                        show: true,
                        horizontalInterval: 2,
                        getDrawingHorizontalLine: (value) {
                          return FlLine(
                            color: Colors.grey[200],
                            strokeWidth: 1,
                          );
                        },
                      ),
                      borderData: FlBorderData(show: false),
                      barGroups: List.generate(
                        controller.visitEfficiency.value.labels.length,
                        (index) => BarChartGroupData(
                          x: index,
                          barRods: [
                            BarChartRodData(
                              toY: controller.visitEfficiency.value.plannedVisits[index].toDouble(),
                              color: Colors.blue.withOpacity(0.7),
                              width: barWidth,
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(4.r),
                                topRight: Radius.circular(4.r),
                              ),
                            ),
                            BarChartRodData(
                              toY: controller.visitEfficiency.value.actualVisits[index].toDouble(),
                              color: ThemeManager.currentTheme.primaryColor.withOpacity(0.7),
                              width: barWidth,
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(4.r),
                                topRight: Radius.circular(4.r),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ));
                },
              ),
            ),
            SizedBox(height: 8.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildLegendItem(
                  I10n.of('sfa.planned_visits'),
                  Colors.blue.withOpacity(0.7),
                ),
                SizedBox(width: 16.w),
                _buildLegendItem(
                  I10n.of('sfa.actual_visits'),
                  ThemeManager.currentTheme.primaryColor.withOpacity(0.7),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建拜访成效卡片
  Widget _buildVisitEffectivenessCard() {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      elevation: 2,
      child: Container(
        height: double.infinity,
        padding: EdgeInsets.all(12.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              I10n.of('sfa.visit_effectiveness'),
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            SizedBox(
              height: 180.h,
              child: LayoutBuilder(
                builder: (context, constraints) {
                  // Calculate appropriate radius based on available width
                  final availableWidth = constraints.maxWidth;
                  final centerRadius = availableWidth * 0.25; // 15% of available width
                  final sectionRadius = availableWidth * 0.30; // 18% of available width
                  
                  return Obx(() => PieChart(
                    PieChartData(
                      sectionsSpace: 2,
                      centerSpaceRadius: centerRadius,
                      sections: [
                        PieChartSectionData(
                          value: controller.visitEffectiveness.value.dealClosed.toDouble(),
                          title: '${controller.visitEffectiveness.value.dealClosed}%',
                          color: Colors.green.withOpacity(0.7),
                          radius: sectionRadius,
                          titleStyle: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        PieChartSectionData(
                          value: controller.visitEffectiveness.value.inProgress.toDouble(),
                          title: '${controller.visitEffectiveness.value.inProgress}%',
                          color: Colors.blue.withOpacity(0.7),
                          radius: sectionRadius,
                          titleStyle: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        PieChartSectionData(
                          value: controller.visitEffectiveness.value.notInterested.toDouble(),
                          title: '${controller.visitEffectiveness.value.notInterested}%',
                          color: Colors.grey.withOpacity(0.7),
                          radius: sectionRadius,
                          titleStyle: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ));
                },
              ),
            ),
            SizedBox(height: 8.h),
            Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildLegendItem(
                      I10n.of('sfa.deal_closed'),
                      Colors.green.withOpacity(0.7),
                    ),
                    SizedBox(width: 16.w),
                    _buildLegendItem(
                      I10n.of('sfa.in_progress'),
                      Colors.blue.withOpacity(0.7),
                    ),
                  ],
                ),
                SizedBox(height: 4.h),
                _buildLegendItem(
                  I10n.of('sfa.not_interested'),
                  Colors.grey.withOpacity(0.7),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建图例项
  Widget _buildLegendItem(String text, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12.w,
          height: 12.h,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2.r),
          ),
        ),
        SizedBox(width: 4.w),
        Text(
          text,
          style: TextStyle(
            fontSize: 10.sp,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// 构建最近活动卡片
  Widget _buildRecentActivitiesCard() {
    final formatCurrency = NumberFormat.currency(
      locale: 'zh_CN',
      symbol: '¥',
      decimalDigits: 0,
    );
    
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        elevation: 2,
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    I10n.of('sfa.recent_activities'),
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  GestureDetector(
                    onTap: () {},
                    child: Text(
                      I10n.of('sfa.view_more'),
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: ThemeManager.currentTheme.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              Obx(() => Column(
                children: controller.recentActivities
                    .take(5) // 只显示前5个活动
                    .map((activity) {
                      IconData activityIcon;
                      Color activityColor;
                      String activityText;
                      
                      switch (activity.type) {
                        case 'visit':
                          activityIcon = Icons.location_on_outlined;
                          activityColor = Colors.blue;
                          activityText = '拜访了 ${activity.customer}';
                          break;
                        case 'order':
                          activityIcon = Icons.shopping_cart_outlined;
                          activityColor = ThemeManager.currentTheme.primaryColor;
                          activityText = '向 ${activity.customer} 下单 ${formatCurrency.format(activity.amount)}';
                          break;
                        case 'payment':
                          activityIcon = Icons.payment_outlined;
                          activityColor = Colors.green;
                          activityText = '收到 ${activity.customer} 付款 ${formatCurrency.format(activity.amount)}';
                          break;
                        case 'return':
                          activityIcon = Icons.assignment_return_outlined;
                          activityColor = Colors.orange;
                          activityText = '处理 ${activity.customer} 退货 ${formatCurrency.format(activity.amount)}';
                          break;
                        default:
                          activityIcon = Icons.event_note_outlined;
                          activityColor = Colors.grey;
                          activityText = '与 ${activity.customer} 互动';
                      }
                      
                      return _buildActivityItem(
                        activityIcon,
                        activityColor,
                        activityText,
                        activity.time,
                      );
                    })
                    .toList(),
              )),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建活动项
  Widget _buildActivityItem(
    IconData icon,
    Color color,
    String text,
    DateTime time,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 36.w,
            height: 36.h,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              icon,
              color: color,
              size: 18.sp,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  text,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  _getTimeAgo(time),
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 获取时间描述
  String _getTimeAgo(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} 天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} 小时前';
    } else {
      return '${difference.inMinutes} 分钟前';
    }
  }

  /// 构建底部导航栏
  Widget _buildBottomNavigationBar() {
    return Container(
      height: 64.h,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildNavItem(Icons.home_outlined, I10n.of('HOME'), true),
          _buildNavItem(Icons.people_outline, I10n.of('sfa.customer_info'), false),
          _buildNavItem(Icons.handshake_outlined, I10n.of('sfa.opportunities'), false),
          _buildNavItem(Icons.calendar_today_outlined, I10n.of('sfa.visits'), false),
          _buildNavItem(Icons.person_outline, I10n.of('ME'), false),
        ],
      ),
    );
  }

  /// 构建导航项
  Widget _buildNavItem(IconData icon, String text, bool isActive) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          size: 24.sp,
          color: isActive
              ? ThemeManager.currentTheme.primaryColor
              : Colors.grey[500],
        ),
        SizedBox(height: 4.h),
        Text(
          text,
          style: TextStyle(
            fontSize: 10.sp,
            color: isActive
                ? ThemeManager.currentTheme.primaryColor
                : Colors.grey[500],
          ),
        ),
      ],
    );
  }
} 