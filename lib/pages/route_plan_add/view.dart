import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../customers/controller.dart';
import 'controller.dart';

class RoutePlanAddPage extends GetView<RoutePlanAddController> {
  const RoutePlanAddPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<RoutePlanAddController>(
      init: RoutePlanAddController(),
      id: "rouote_plan_add",
      builder: (_) {
        return Scaffold(
          backgroundColor: const Color(0xFFF6F6F6),
          appBar: AppBar(
            title: const Text("添加拜访计划"),
            centerTitle: true,
          ),
          body: SafeArea(
            child: Column(
              children: [
                _buildDatePicker(context),
                _buildSearchBar(),
                _buildSelectAllCheckbox(),
                Expanded(child: _buildStoreList()),
                _buildBottomButton(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDatePicker(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Row(
        children: [
          const Icon(Icons.calendar_today, color: Colors.blue),
          SizedBox(width: 8.w),
          Obx(() => GestureDetector(
                onTap: () => controller.pickDate(context),
                child: Text(
                  DateFormat('yyyy-MM-dd').format(controller.selectedDate.value),
                  style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500),
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Container(
        height: 40.h,
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: TextField(
          controller: controller.searchController,
          onChanged: controller.searchStores,
          decoration: InputDecoration(
            contentPadding: EdgeInsets.symmetric(vertical: 0.h),
            prefixIcon: Icon(
              Icons.search,
              color: Colors.grey[500],
              size: 20.sp,
            ),
            hintText: '搜索门店',
            hintStyle: TextStyle(
              color: Colors.grey[500],
              fontSize: 14.sp,
            ),
            border: InputBorder.none,
          ),
        ),
      ),
    );
  }

  Widget _buildSelectAllCheckbox() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        children: [
          Obx(() => Checkbox(
                value: controller.isAllSelected.value,
                onChanged: (val) => controller.toggleSelectAll(val ?? false),
              )),
          const Text('全选'),
        ],
      ),
    );
  }

  Widget _buildStoreList() {
    return Obx(() {
      final stores = controller.filteredStores;
      if (stores.isEmpty) {
        return Center(
          child: Text('没有找到匹配的门店', style: TextStyle(fontSize: 16.sp)),
        );
      }
      return ListView.builder(
        itemCount: stores.length,
        itemBuilder: (context, index) {
          final store = stores[index];
          final isSelected = controller.selectedStoreIds.contains(store.id);
          return GestureDetector(
            onTap: () => controller.toggleStore(store.id),
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 2,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Row(
                  children: [
                    Checkbox(
                      value: isSelected,
                      onChanged: (val) => controller.toggleStore(store.id),
                    ),
                    SizedBox(width: 8.w),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8.r),
                      child: Image.network(
                        store.logo,
                        width: 48.w,
                        height: 48.h,
                        fit: BoxFit.cover,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            store.name,
                            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            store.address,
                            style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    });
  }

  Widget _buildBottomButton() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: SizedBox(
        width: double.infinity,
        height: 48.h,
        child: ElevatedButton(
          onPressed: controller.onConfirm,
          child: const Text('确认添加'),
        ),
      ),
    );
  }
}
