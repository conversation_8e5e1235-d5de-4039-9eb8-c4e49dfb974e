import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../customers/controller.dart';
import 'package:intl/intl.dart';

class RoutePlanAddController extends GetxController {
  // 日期选择
  final selectedDate = DateTime.now().obs;
  // 搜索控制器
  late TextEditingController searchController;
  // 搜索关键词
  final searchText = ''.obs;
  // 门店列表（复用customers的测试数据）
  final storesList = <Customer>[].obs;
  // 已选门店id
  final selectedStoreIds = <String>{}.obs;
  // 全选
  RxBool get isAllSelected => storesList.isNotEmpty && selectedStoreIds.length == filteredStores.length && filteredStores.isNotEmpty
      ? true.obs
      : false.obs;

  @override
  void onInit() {
    super.onInit();
    searchController = TextEditingController();
    _initStores();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  void _initStores() {
    // 复用customers的测试数据
    final temp = CustomersController();
    temp.onInit();
    storesList.assignAll(temp.customersList);
  }

  // 日期选择
  Future<void> pickDate(BuildContext context) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: selectedDate.value,
      firstDate: DateTime(2020),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      selectedDate.value = picked;
    }
  }

  // 搜索门店
  void searchStores(String keyword) {
    searchText.value = keyword;
    update(["rouote_plan_add"]);
  }

  // 过滤门店
  List<Customer> get filteredStores {
    return storesList.where((store) {
      if (searchText.value.isEmpty) return true;
      return store.name.toLowerCase().contains(searchText.value.toLowerCase()) ||
          store.contactPerson.toLowerCase().contains(searchText.value.toLowerCase());
    }).toList();
  }

  // 切换单个门店选中
  void toggleStore(String id) {
    if (selectedStoreIds.contains(id)) {
      selectedStoreIds.remove(id);
    } else {
      selectedStoreIds.add(id);
    }
    update(["rouote_plan_add"]);
  }

  // 全选/取消全选
  void toggleSelectAll(bool selectAll) {
    if (selectAll) {
      selectedStoreIds.addAll(filteredStores.map((e) => e.id));
    } else {
      selectedStoreIds.clear();
    }
    update(["rouote_plan_add"]);
  }

  // 确认添加
  void onConfirm() {
    // TODO: 实际业务逻辑
    Get.snackbar('操作', '已选择${selectedStoreIds.length}家门店，日期：${DateFormat('yyyy-MM-dd').format(selectedDate.value)}');
  }
}
