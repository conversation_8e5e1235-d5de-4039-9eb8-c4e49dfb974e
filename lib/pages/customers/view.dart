import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../router/routers_names.dart';
import '../../them/base_them.dart';
import '../../i10n/i10n.dart';
import '../../componse/sfa_app_bar.dart';
import 'controller.dart';

/// 客户管理页面
class CustomersPage extends GetView<CustomersController> {
  const CustomersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CustomersController>(
      init: CustomersController(),
      id: "customers",
      builder: (_) {
        return Scaffold(
      backgroundColor: const Color(0xFFF6F6F6),
       appBar:const SfaAppBar(),
      body: SafeArea(
        child: Column(
          children: [
            _buildAppBar(),
            _buildSearchBar(),
            _buildFilterTabs(),
            Expanded(
              child: _buildCustomersList(),
            ),
          ],
        ),
      )
      );
      }
    );
  }

  /// 构建顶部导航栏
  Widget _buildAppBar() {
    return Container(
      height: 50.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(  
            color: Colors.black.withOpacity(0.05),
            blurRadius: 2,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            I10n.of('sfa.customer_management'),
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: ThemeManager.currentTheme.primaryColor,
            ),
          ),
          Row(
            children: [
              IconButton(
                icon: Icon(
                  Icons.add_task,
                  size: 24.sp,
                  color: ThemeManager.currentTheme.primaryColor,
                ),
                tooltip: '添加拜访计划',
                onPressed: () {
                  Get.toNamed(RoutesNames.routePlanAdd);
                },
              ),
              IconButton(
                icon: Icon(
                  Icons.filter_list,
                  size: 24.sp,
                  color: Colors.grey[600],
                ),
                onPressed: () {
                  // 显示筛选选项
                },
              )
            ],
          ),
        ],
      ),
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar() {
    return Container(
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Container(
        height: 40.h,
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: TextField(
          controller: controller.searchController,
          onChanged: controller.searchCustomers,
          decoration: InputDecoration(
            contentPadding: EdgeInsets.symmetric(vertical: 0.h),
            prefixIcon: Icon(
              Icons.search,
              color: Colors.grey[500],
              size: 20.sp,
            ),
            hintText: I10n.of('sfa.search'),
            hintStyle: TextStyle(
              color: Colors.grey[500],
              fontSize: 14.sp,
            ),
            border: InputBorder.none,
          ),
        ),
      ),
    );
  }

  /// 构建筛选标签
  Widget _buildFilterTabs() {
    return Container(
      height: 50.h,
      width: 1.sw,
      color: Colors.white,
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Obx(() => Row(
          children: [
            _buildFilterChip('all', I10n.of('sfa.all')),
            _buildFilterChip('cafe', I10n.of('route.cafe')),
            _buildFilterChip('grocery', I10n.of('route.grocery')),
            _buildFilterChip('restaurant', I10n.of('route.restaurant')),
            _buildFilterChip('tea_shop', I10n.of('route.tea_shop')),
            _buildFilterChip('bakery', I10n.of('route.bakery')),
          ],
        )),
      ),
    );
  }

  /// 构建筛选芯片
  Widget _buildFilterChip(String type, String label) {
    final isSelected = controller.filterType.value == type;
    
    return GestureDetector(
      onTap: () => controller.filterCustomers(type),
      child: Container(
        margin: EdgeInsets.only(right: 12.w),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: isSelected ? ThemeManager.currentTheme.primaryColor : Colors.grey[100],
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[700],
            fontSize: 16.sp,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  /// 构建客户列表
  Widget _buildCustomersList() {
    return Obx(() {
      final customers = controller.filteredCustomers;
      
      if (customers.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: 64.sp,
                color: Colors.grey[400],
              ),
              SizedBox(height: 16.h),
              Text(
                '没有找到匹配的客户',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        );
      }
      
      return ListView.builder(
        padding:const EdgeInsets.symmetric(vertical: 8),
        itemCount: customers.length,
        itemBuilder: (context, index) {
          final customer = customers[index];
          return _buildCustomerItem(customer);
        },
      );
    });
  }

  /// 构建客户项
  Widget _buildCustomerItem(Customer customer) {
    final formatCurrency = NumberFormat.currency(
      locale: 'zh_CN',
      symbol: '¥',
      decimalDigits: 0,
    );
    
    return GestureDetector(
      onTap: () => controller.navigateToCustomerDetail(customer),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 2,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
            padding: EdgeInsets.all(16.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8.r),
                child: Image.network(
                  customer.logo,
                  width: 60.w,
                  height: 60.h,
                  fit: BoxFit.cover,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            customer.name,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        _buildRatingStars(customer.rating),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      customer.address,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4.h),
                    Row(
                      children: [
                        Text(
                          '${customer.contactPerson} · ${customer.phone}',
                          style: TextStyle(
                            fontSize: 12.sp ,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Wrap(
                            spacing: 6,
                          children: customer.tags.map((tag) => _buildTag(tag)).toList(),
                        ),
                        Text(
                          formatCurrency.format(customer.lastOrderAmount),
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                            color: ThemeManager.currentTheme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建评分星星
  Widget _buildRatingStars(int rating) {
    return Row(
      children: List.generate(5, (index) {
        return Icon(
          index < rating ? Icons.star : Icons.star_border,
          color: index < rating ? Colors.amber : Colors.grey[400],
          size: 16.sp,
        );
      }),
    );
  }

  /// 构建标签
  Widget _buildTag(String tag) {
    Color tagColor;
    
    switch (tag) {
      case 'VIP':
        tagColor = ThemeManager.currentTheme.primaryColor;
        break;
      case '连锁':
        tagColor = Colors.blue;
        break;
      case '稳定':
        tagColor = Colors.green;
        break;
      case '高频':
        tagColor = Colors.orange;
        break;
      case '新客户':
        tagColor = Colors.purple;
        break;
      default:
        tagColor = Colors.grey;
    }
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: tagColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Text(
        tag,
        style: TextStyle(
          color: tagColor,
          fontSize: 10.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建底部导航栏
  Widget _buildBottomNavigationBar() {
    return Container(
      height: 64.h,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildNavItem(Icons.home_outlined, I10n.of('HOME'), false),
          _buildNavItem(Icons.people_outline, I10n.of('sfa.customer_info'), true),
          _buildNavItem(Icons.handshake_outlined, I10n.of('sfa.opportunities'), false),
          _buildNavItem(Icons.calendar_today_outlined, I10n.of('sfa.visits'), false),
          _buildNavItem(Icons.person_outline, I10n.of('ME'), false),
        ],
      ),
    );
  }

  /// 构建导航项
  Widget _buildNavItem(IconData icon, String text, bool isActive) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          size: 24.sp,
          color: isActive
              ? ThemeManager.currentTheme.primaryColor
              : Colors.grey[500],
        ),
        const SizedBox(height: 4),
        Text(
          text,
          style: TextStyle(
              fontSize: 10.sp,
            color: isActive
                ? ThemeManager.currentTheme.primaryColor
                : Colors.grey[500],
          ),
        ),
      ],
    );
  }
} 