import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../../router/routers_names.dart';

/// 客户管理控制器
class CustomersController extends GetxController {
  // 客户列表数据
  final customersList = <Customer>[].obs;
  // 搜索关键词
  final searchText = ''.obs;
  // 筛选条件
  final filterType = 'all'.obs;
  
  // 搜索控制器
  late TextEditingController searchController;
  
  @override
  void onInit() {
    super.onInit();
    searchController = TextEditingController();
    _generateTestData();
  }
  
  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }
  
  /// 导航到客户详情页
  void navigateToCustomerDetail(Customer customer) {
    Get.toNamed(RoutesNames.customerDetail, arguments: customer.id);
  }
  
  /// 生成测试数据
  void _generateTestData() {
    // 生成20条测试客户数据
    customersList.addAll([
      Customer(
        id: 'C001',
        name: '星巴克咖啡（国贸店）',
        type: 'cafe',
        logo: 'https://images.unsplash.com/photo-1578916171728-46686eac8d58?w=200',
        address: '北京市朝阳区建国门外大街1号国贸商城B1层',
        contactPerson: '李经理',
        phone: '010-12345678',
        lastOrderAmount: 5680,
        lastOrderDate: DateTime.now().subtract(const Duration(days: 5)),
        rating: 5,
        tags: ['高频', 'VIP', '连锁'],
      ),
      Customer(
        id: 'C002',
        name: '全家便利店（望京店）',
        type: 'grocery',
        logo: 'https://images.unsplash.com/photo-1578916171728-46686eac8d58?w=200',
        address: '北京市朝阳区望京西园三区1号楼底商',
        contactPerson: '张店长',
        phone: '010-87654321',
        lastOrderAmount: 3240,
        lastOrderDate: DateTime.now().subtract(const Duration(days: 2)),
        rating: 4,
        tags: ['连锁', '稳定'],
      ),
      Customer(
        id: 'C003',
        name: '喜茶（三里屯店）',
        type: 'tea_shop',
        logo: 'https://images.unsplash.com/photo-1578916171728-46686eac8d58?w=200',
        address: '北京市朝阳区三里屯太古里北区N8-30',
        contactPerson: '王经理',
        phone: '010-23456789',
        lastOrderAmount: 4120,
        lastOrderDate: DateTime.now().subtract(const Duration(days: 7)),
        rating: 5,
        tags: ['高频', '连锁'],
      ),
      Customer(
        id: 'C004',
        name: '麦当劳（西单店）',
        type: 'restaurant',
        logo: 'https://images.unsplash.com/photo-1578916171728-46686eac8d58?w=200',
        address: '北京市西城区西单北大街131号',
        contactPerson: '刘店长',
        phone: '010-34567890',
        lastOrderAmount: 8650,
        lastOrderDate: DateTime.now().subtract(const Duration(days: 1)),
        rating: 4,
        tags: ['VIP', '连锁', '稳定'],
      ),
      Customer(
        id: 'C005',
        name: '肯德基（王府井店）',
        type: 'restaurant',
        logo: 'https://images.unsplash.com/photo-1578916171728-46686eac8d58?w=200',
        address: '北京市东城区王府井大街253号',
        contactPerson: '赵经理',
        phone: '010-45678901',
        lastOrderAmount: 7320,
        lastOrderDate: DateTime.now().subtract(const Duration(days: 3)),
        rating: 4,
        tags: ['连锁', '稳定'],
      ),
    ]);
    
    // 添加更多测试数据，确保总数达到20
    for (int i = 6; i <= 20; i++) {
      final typeIndex = i % 5;
      String type;
      String name;
      
      switch (typeIndex) {
        case 0:
          type = 'cafe';
          name = '咖啡店${i.toString().padLeft(2, '0')}';
          break;
        case 1:
          type = 'grocery';
          name = '便利店${i.toString().padLeft(2, '0')}';
          break;
        case 2:
          type = 'tea_shop';
          name = '茶饮店${i.toString().padLeft(2, '0')}';
          break;
        case 3:
          type = 'restaurant';
          name = '餐厅${i.toString().padLeft(2, '0')}';
          break;
        default:
          type = 'bakery';
          name = '面包店${i.toString().padLeft(2, '0')}';
      }
      
      customersList.add(Customer(
        id: 'C${i.toString().padLeft(3, '0')}',
        name: name,
        type: type,
        logo: 'https://picsum.photos/seed/$i/200',
        address: '北京市示例区示例街道${i}号',
        contactPerson: '联系人$i',
        phone: '010-${(10000000 + i * 12345).toString()}',
        lastOrderAmount: 1000 + i * 200.0,
        lastOrderDate: DateTime.now().subtract(Duration(days: i % 10)),
        rating: 3 + (i % 3),
        tags: i % 3 == 0 ? ['VIP', '稳定'] : (i % 2 == 0 ? ['连锁'] : ['新客户']),
      ));
    }
  }
  
  /// 搜索客户
  void searchCustomers(String keyword) {
    searchText.value = keyword;
  }
  
  /// 筛选客户
  void filterCustomers(String type) {
    filterType.value = type;
  }
  
  /// 获取筛选后的客户列表
  List<Customer> get filteredCustomers {
    return customersList.where((customer) {
      // 先筛选类型
      bool matchesFilter = filterType.value == 'all' || customer.type == filterType.value;
      
      // 再筛选搜索关键词
      bool matchesSearch = searchText.value.isEmpty ||
          customer.name.toLowerCase().contains(searchText.value.toLowerCase()) ||
          customer.contactPerson.toLowerCase().contains(searchText.value.toLowerCase());
      
      return matchesFilter && matchesSearch;
    }).toList();
  }
}

/// 客户模型
class Customer {
  final String id;
  final String name;
  final String type;
  final String logo;
  final String address;
  final String contactPerson;
  final String phone;
  final double lastOrderAmount;
  final DateTime lastOrderDate;
  final int rating;
  final List<String> tags;
  
  Customer({
    required this.id,
    required this.name,
    required this.type,
    required this.logo,
    required this.address,
    required this.contactPerson,
    required this.phone,
    required this.lastOrderAmount,
    required this.lastOrderDate,
    required this.rating,
    required this.tags,
  });
} 