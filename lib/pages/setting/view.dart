import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../componse/sfa_app_bar.dart';
import '../../them/base_them.dart';
import 'index.dart';

class SettingPage extends GetView<SettingController> {
  const SettingPage({super.key});

  // 构建语言选择项
  Widget _buildLanguageItem(Map<String, String> language) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 14),
        title: Text(
          language["key"]!.tr,
          style: const TextStyle(fontSize: 14),
        ),
        trailing: Obx(() => Radio<String>(
              value: language["value"]!,
              groupValue: controller.selectedLanguage.value,
              onChanged: null,
            )),
        onTap: () => controller.changeLanguage(language["value"]!),
      ),
    );
  }

  // 构建市场选择项
  Widget _buildMarketItem(Map<String, String> market) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 14),
        title: Text(
          market["key"]!.tr,
          style: const TextStyle(fontSize: 14),
        ),
        trailing: Obx(() => Radio<String>(
              value: market["value"]!,
              groupValue: controller.selectedMarket.value,
              onChanged: null,
            )),
        onTap: () => controller.changeMarket(market["value"]!),
      ),
    );
  }

  // 构建市场选择项
  Widget _buildThemItem(Map<String, String> them) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 14),
        title: Text(
          them["key"]!.tr,
          style: const TextStyle(fontSize: 14),
        ),
        trailing: Obx(() => Radio<String>(
              value: them["value"]!,
              groupValue: controller.selectedTheme.value,
              onChanged: null,
            )),
        onTap: () => controller.changeTheme(them["value"]!),
      ),
    );
  }

  // 构建数据源选择项
  Widget _buildDataSourceItem(Map<String, String> dataSource) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 14),
        title: Text(dataSource["key"]!, style: const TextStyle(fontSize: 14)),
        trailing: Obx(() => Radio<String>(
              value: dataSource["value"]!,
              groupValue: controller.selectedDataSource.value,
              onChanged: null,
            )),
        onTap: () => controller.changeDataSource(dataSource["value"]!),
      ),
    );
  }

  // 构建格式化展示部分
  Widget _buildFormatDisplay() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(controller.formatCurrency()),
          const SizedBox(height: 8),
          Text(controller.formatNumber()),
          const SizedBox(height: 8),
          Text(controller.formatDate()),
          const SizedBox(height: 8),
          Text(controller.formatDateTime()),
        ],
      ),
    );
  }

  // 主视图
  Widget _buildView() {
    return Container(
      width: Get.width,
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                // 数据源设置
                Container(
                  margin: const EdgeInsets.only(top: 4),
                  child: const ListTile(
                    title: Text('数据源'),
                    trailing:  Icon(Icons.arrow_forward_ios, size: 16),
                  ),
                ),
                 ...controller.dataSources.map( _buildDataSourceItem),
                const Divider(),
                // 语言设置
                Container(
                  margin: const EdgeInsets.only(top: 4),  
                  child: ListTile(
                    title: Text('LANGUAGE'.tr),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  ),
                ),
                ...controller.languages.map(_buildLanguageItem),
                const Divider(),
                // 市场设置
                ListTile(
                  title: Text('MARKET'.tr),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                ),
                ...controller.markets.map(_buildMarketItem),
                const Divider(),
                // // 主图设置
                // ListTile(
                //   title: Text('THEME'.tr),
                //   trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                // ),
                // ...controller.thems.map(_buildThemItem),

                // 格式化展示
                _buildFormatDisplay(),
              ],
            ),
          ),
          // 添加保存按钮
          // Padding(
          //   padding: const EdgeInsets.all(16.0),
          //   child: SizedBox(
          //     width: double.infinity,
          //     child: ElevatedButton(
          //       onPressed: controller.saveSettings,
          //       child: Text(
          //         'SAVE'.tr,
          //         style: const TextStyle(
          //           color: Colors.white,
          //           fontSize: 18,
          //           fontWeight: FontWeight.bold
          //         ),
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SettingController>(
      init: SettingController(),
      id: "setting",
      builder: (_) {
        return Scaffold(
          backgroundColor: ThemeManager.currentTheme.primaryColor,
          // appBar: AppBar(
          //   title: Text(
          //     'COMMON_SETTINGS'.tr,
          //     style: const TextStyle(
          //       color: Colors.white,
          //       fontSize: 20,
          //       fontWeight: FontWeight.bold,
          //     ),
          //   ),
          //   elevation: 0,
          // ),
          appBar:const SfaAppBar(),
          body: _buildView(),
        );
      },
    );
  }
}
