import 'dart:ui';

import 'package:get/get.dart';
import 'package:teemo_coca/sfaglobal.dart';

import '../../common/global_mixin.dart';
import '../../common/util.dart';
import '../../common/common.dart';
import '../../them/base_them.dart';
import '../../them/ebest_theme.dart';
import '../../them/red_theme.dart';

class SettingController extends GetxController with GlobalMixin {
  // 当前选择的语言
  final selectedLanguage = "zh".obs;
  // 当前选择的市场
  final selectedMarket = "CN".obs;
  // 当前选择的主题
  final selectedTheme = "Red".obs;
  // 当前选择的数据源
  final selectedDataSource = Common.sourceType.sfdc.obs;
  // 测试数据
  final testAmount = 1234567.12;
  final testNumber = 1234567.1;
  final testDate = DateTime.now().toString().split(" ")[0];
  final testDateTime = DateTime.now().toString();

  // 语言选项
  final languages = [
    {"key": "CHINESE", "value": "zh"},
    {"key": "ENGLISH", "value": "en"},
  ];

  // 市场选项
  final markets = [
    {"key": "CHINA", "value": "CN"},
    {"key": "HONG_KONG", "value": "HK"},
    {"key": "VIETNAM", "value": "VN"},
    {"key": "CAMBODIA", "value": "KH"},
  ];

    // 主题选项
  final thems = [
     {"key": "Red", "value": "Red"},
     {"key": "eBest", "value": "eBest"}
  ];

  // 数据源选项
  final dataSources = [
    {"key": Common.sourceType.sfdc, "value": Common.sourceType.sfdc},
    {"key": Common.sourceType.eBest, "value": Common.sourceType.eBest},
    {"key": Common.sourceType.local, "value": Common.sourceType.local},
  ];

  // 切换语言
  void changeLanguage(String key) {
    selectedLanguage.value = key;
    final lang = languages.firstWhere((element) => element["value"] == key);
    Get.updateLocale(Locale(lang["value"]!));
    saveSettings();
  }

  // 切换市场
  void changeMarket(String key) {
    selectedMarket.value = key;
    update(["setting"]);
    saveSettings();
  }

  // 切换主图
  void changeTheme(String key) {
    selectedTheme.value = key;
    ThemeManager.setTheme(key == "Red" ? RedTheme() : EBestTheme());
    Get.changeTheme(ThemeManager.currentTheme!.themeData);
  }

  // 切换数据源
  void changeDataSource(String key) async {
    selectedDataSource.value = key;
    await SfaGlobal.setDataSource(key);
    saveSettings();
    update(["setting"]);
  }

  // 获取当前市场的区域代码
  String getCurrentMarketRegion() {
    return "${selectedLanguage.value}_${selectedMarket.value}";
  }

  // 格式化货币
  String formatCurrency() {
    return $ncf(testAmount.toString(), getCurrentMarketRegion());
  }

  // 格式化数字
  String formatNumber() {
    return $nf(testNumber.toString(), getCurrentMarketRegion());
  }

  // 格式化日期
  String formatDate() {
    return $ds(testDate, getCurrentMarketRegion());
  }

  // 格式化日期时间
  String formatDateTime() {
    return $dl(testDateTime, getCurrentMarketRegion());
  }

  @override
  void onReady() {
    super.onReady();
    _initData();
  }

  _initData() async {
    // 从持久化存储中读取设置
    String? savedLanguage = await Util.getStorage(Common.localStorageKey.selectedLanguage);
    String? savedMarket = await Util.getStorage(Common.localStorageKey.selectedMarket);
    // String? savedTheme = await Util.getStorage(Common.localStorageKey.selectedTheme);
    if (savedLanguage != null) {
      final lang = languages.firstWhere((element) => element["value"] == savedLanguage);
      if(lang.isNotEmpty){
        selectedLanguage.value = lang["value"]!;
      }
    }
    
    if (savedMarket != null) {
      final market = markets.firstWhere((element) => element["value"] == savedMarket);
      if(market.isNotEmpty){
        selectedMarket.value = market["value"]!;
      }
    }
    print('selectedLanguage.value ${selectedLanguage.value} selectedMarket.value ${selectedMarket.value}');
    // Get.updateLocale(Locale(selectedLanguage.value,selectedMarket.value));  
    print('Get.locale ${Get.locale}');
    Get.updateLocale(const Locale('zh', 'CN')); 
    // 简体中文（中国大陆）：zh_CN 或 Locale('zh', 'CN')
    // 繁体中文（台湾）：zh_TW 或 Locale('zh', 'TW') 

    String? savedDataSource = await Util.getStorage(Common.localStorageKey.selectedDataSource);
    if (savedDataSource != null) {
      final ds = dataSources.firstWhere((element) => element["value"] == savedDataSource, orElse: () => dataSources[0]);
      selectedDataSource.value = ds["value"]!;
    }
    await SfaGlobal.initDataSource();
    update(["setting"]);
  }

  // 保存设置
  Future<void> saveSettings() async {
    final lang = languages.firstWhere((element) => element["value"] == selectedLanguage.value);
    final market = markets.firstWhere((element) => element["value"] == selectedMarket.value);
    await Util.setStorage(Common.localStorageKey.selectedLanguage, lang['value']);
    await Util.setStorage(Common.localStorageKey.selectedMarket, market['value']);
    await Util.setStorage(Common.localStorageKey.selectedDataSource, selectedDataSource.value);
    // await Util.setStorage(Common.localStorageKey.selectedTheme, selectedTheme.value);
    // Get.back();
  }
}
