// import 'package:finclip_flutter_plugin/finclip_flutter_plugin.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:teemo_coca/common/screen_adapter.dart';
import '../../common/fin_clp_manager.dart'; 
import 'index.dart'; 


class MopKpiPage extends GetView<MopKpiController> {
  const MopKpiPage({super.key});

   @override
  Widget build(BuildContext context) {
    return GetBuilder<MopKpiController>(
      init: MopKpiController(),
      id: "mopKpi",
      builder: (_) {
        return Scaffold( 
          body:  SafeArea(
            child:  SizedBox(
            width: ScreenAdapter.screenWidth(),
            height:ScreenAdapter.screenHeight(),
            // child: FinClipWidget(
            //           widgetId: 'fc2624074717001157',
            //           widgetServer: 'https://www.finclip.com',
            //           offlineMiniprogramZipPath: 'assets/minp/cocohk_kpi_widget-1.0.0.zip',
            //           offlineFrameworkZipPath: 'assets/minp/mop-flutter-sdk.zip',
            //           useOfflineMode: true,
            //           // offlinZipPath: 'assets/finclip/mop_kpi.zip'
            //         ),
            child: Container(
              color: Colors.red,
            )
            ),
          )
        );
      },
    );
  }
}
