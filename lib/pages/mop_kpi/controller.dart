import 'package:get/get.dart';
// import 'package:mop/api.dart';
import 'package:mop/mop.dart';
import '../../common/fin_clp_manager.dart';

class MopKpiController extends GetxController {
  MopKpiController();

  _initData() {  
  }
 

  void onTap() {
   
  }

  // @override
  // void onInit() {
  //   super.onInit();
  // }

  @override
  void onReady() async {
    super.onReady();
    _initData();
  }



  @override
  void onClose() {
    super.onClose();
  }
}
