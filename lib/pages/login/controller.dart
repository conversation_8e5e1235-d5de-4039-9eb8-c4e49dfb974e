import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../common/jpush_manager.dart';
import '../../modules/baseResultModel.dart';
import '../../modules/loginUserInfoModel.dart';
import '../../router/routers_names.dart';
import '../../common/util.dart';
import '../../common/common.dart';
import '../../server/login/sfdcLoginServer.dart';
import '../../server/login/eBestloginServer.dart';
import '../../server/login/localLoginServer.dart';
import '../../common/sfa_dialog.dart';
import '../../server/sync/ISyncServer.dart';
import '../../sfaglobal.dart';
import '../../server/sync/baseSyncServer.dart';
import '../../server/login/ILoginServer.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth/error_codes.dart' as auth_error;
import 'package:local_auth_android/local_auth_android.dart';
import 'package:local_auth_darwin/local_auth_darwin.dart';
import 'package:flutter/services.dart';


class LoginController extends GetxController {
  final usernameController = TextEditingController();
  final passwordController = TextEditingController();
  
  final isPasswordVisible = false.obs;
  final errorMessage = ''.obs;
  final rememberMe = false.obs;
  final isBiometricAvailable = false.obs;
  final isBiometricEnabled = false.obs;

  final LocalAuthentication _localAuth = LocalAuthentication();
  Map<String, dynamic>? _savedCredentials;

  ILoginServer? loginServer;

  @override
  void onReady() async{
    super.onReady();
    // SfaDialog.showLoading(message: '加载中...');
    _loadSavedCredentials();
    _checkBiometricAvailability();

     var bid = await Util.getAppBundleId();
     print('getAppBundleId $bid');
   
  }

  // 检查生物识别是否可用
  Future<void> _checkBiometricAvailability() async {
    try {
      final canAuthenticateWithBiometrics = await _localAuth.canCheckBiometrics;
      final canAuthenticate = canAuthenticateWithBiometrics || await _localAuth.isDeviceSupported();
      isBiometricAvailable.value = canAuthenticate;
      print('生物识别是否可用: $canAuthenticate');
       _checkLastLogin();
    } catch (e) {
      print('检查生物识别可用性出错: $e');
      isBiometricAvailable.value = false;
    }
  }

  // 检查最后登录时间，如果是今天则提示生物识别登录
  Future<void> _checkLastLogin() async {
    try {
      final lastSyncTimeStr = await Util.getStorage(Common.localStorageKey.lastSyncTime,false);
       print('lastSyncTimeStr: $lastSyncTimeStr');
      if (lastSyncTimeStr != null) {
        final lastSyncTime = DateTime.parse(lastSyncTimeStr.toString());
        final now = DateTime.now();
        
        // 判断是否是同一天
        final isSameDay = lastSyncTime.year == now.year && 
                          lastSyncTime.month == now.month && 
                          lastSyncTime.day == now.day;
        
        if (isSameDay && isBiometricAvailable.value && _savedCredentials != null) {
          isBiometricEnabled.value = true;
          update();
          authenticateWithBiometrics();
        }
      }
    } catch (e) {
      print('检查最后登录时间出错: $e');
    }
  }
 

  // 执行生物识别认证
  Future<void> authenticateWithBiometrics() async {
    try {
      final authenticated = await _localAuth.authenticate(
        localizedReason: '请按下指纹感应区验证指纹',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true, //只使用生物识别
        ),
        authMessages: const <AuthMessages>[
          AndroidAuthMessages(
            signInTitle: '请按下指纹感应区验证指纹',
            biometricHint: '请按下指纹感应区验证指纹',
            cancelButton: '取消'
          ),
          IOSAuthMessages(
            cancelButton: '取消',
          ),
        ]);
      
      if (authenticated) {
        // 生物识别认证成功，使用保存的凭据登录
         var loginUserInfo = await  Util.getStorage(Common.localStorageKey.loginUserInfo);
        SfaGlobal.loginUserInfo = LoginUserInfoModel.fromJson(loginUserInfo);

        Get.offAllNamed(RoutesNames.sync,arguments: {
          'syncType': SyncType.incremental
        });
         
      } else {
        errorMessage.value = '生物识别认证失败，请使用账号密码登录';
      }
    } catch (e) {
      print('生物识别认证出错: $e');
      if (e is PlatformException) {
        if (e.code == auth_error.notAvailable) {
          errorMessage.value = '设备不支持生物识别';
        } else if (e.code == auth_error.notEnrolled) {
          errorMessage.value = '没有设置生物识别';
        } else {
          errorMessage.value = '生物识别认证失败: ${e.message}';
        }
      } else {
        errorMessage.value = '生物识别认证失败: $e';
      }
    }
  }



  void _initLoginServer() {
    print('SfaGlobal.dataSource --------- : ${SfaGlobal.dataSource}');
    if (SfaGlobal.dataSource == 'SFDC') {
      loginServer = SfdcLoginServer();
    } else if (SfaGlobal.dataSource == Common.sourceType.eBest) {
      loginServer = EBestLoginServer();
    } else {
      loginServer = LocalLoginServer();
    }
    update();
  }

  // 加载保存的账号密码
  void _loadSavedCredentials() async {
    final savedCredentials = await Util.getStorage(Common.localStorageKey.userCredentials);
    if (savedCredentials != null) {
      _savedCredentials = savedCredentials;
       // usernameController.text = credentials['username'] ?? '';
      // passwordController.text = credentials['password'] ?? '';
    }
     
      print('SfaGlobal.dataSource --------- : ${SfaGlobal.dataSource}');
      if (SfaGlobal.dataSource == Common.sourceType.sfdc) {
        usernameController.text = '<EMAIL>.dp2';
        passwordController.text = 'ebest@2024';
      } else if (SfaGlobal.dataSource == Common.sourceType.eBest) {
        usernameController.text = 'B7429';
        passwordController.text = '1qaz@WSX';
      } else {
        usernameController.text = 'ebestsfa';
        passwordController.text = 'ebestsfa';
      }
      rememberMe.value = true;
    // }
  }

  // 保存账号密码
  Future<void> _saveCredentials(String username, String password,bool isRemember) async {
    final credentials = {
      'username': username,
      'password': password,
      'isRemember': isRemember,
    };
    _savedCredentials = credentials;
    await Util.setStorage(Common.localStorageKey.userCredentials, credentials);
  }

  // 清除保存的账号密码
  Future<void> _clearCredentials() async {
    _savedCredentials = null;
    await Util.removeStorage(Common.localStorageKey.userCredentials);
  }

  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  void login({bool skipBiometricCheck = false}) async {
    final username = usernameController.text;
    final password = passwordController.text;

    if (username.isEmpty || password.isEmpty) {
      errorMessage.value = 'PLEASE_ENTER_USERNAME_AND_PASSWORD'.tr;
      return;
    }
    errorMessage.value = '';
    try {
      _initLoginServer();
      //loading
      SfaDialog.showLoading(message: "...");
      // 等待loginServer初始化完成
      while (loginServer == null) {
        await Future.delayed(const Duration(milliseconds: 10));
      }
      BaseResultModel<LoginUserInfoModel> result = await loginServer!.login(username, password);
      
        
      SfaDialog.dismissLoading();
      if (result.code == 0) {
        print("setAlias username: $username");
        JPushManager().setAlias(username);
        //保存账号密码
        await _saveCredentials(username, password,rememberMe.value);
        //更新最后登录时间
        await Util.setStorage(Common.localStorageKey.lastSyncTime, DateTime.now().toIso8601String());
        //保存token
        final token = result.data?.token; 
        Util.setStorage(Common.localStorageKey.token, token);
        Util.setStorage(Common.localStorageKey.sfdcInstanceUrl, result.data?.instanceUrl ?? '');
        SfaGlobal.token = token;
        //保存用户信息
        if (result.data != null) {
          print(" Common.localStorageKey.loginUserInfo 1");
          print(" Common.localStorageKey.loginUserInfo "+jsonEncode( result.data));
          SfaGlobal.loginUserInfo = result.data;
          await Util.setStorage(Common.localStorageKey.loginUserInfo, result.data!.toJson());
        } 
        var syncType = await BaseSyncServer.getSyncType(username, result.data?.version ?? '');
        Get.offAllNamed(RoutesNames.sync,arguments: {
          'syncType': syncType,
        }); 
      } else {
        errorMessage.value = result.message ?? '';
      }
    } catch (e) {
      print(e);
      errorMessage.value = e.toString();
      SfaDialog.dismissLoading();
    } 
  }

  bool versionCheck(String version) {
    var sfaVersion = Common.sfaVersion;
    print('sfaVersion: ${sfaVersion}----${version}');
    //版本号比较
    return sfaVersion == version;
  }

  void adLogin() {
    // 实现AD登录逻辑
    print('AD Login tapped');
  }

 void openSettings() {
    Get.toNamed(RoutesNames.setting);
  }

  @override
  void onClose() {
    usernameController.dispose();
    passwordController.dispose();
    super.onClose();
  }
}
