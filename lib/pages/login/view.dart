import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// import 'package:flutter_camera_stitching/flutter_camera_stitching.dart';
import 'package:get/get.dart';
import 'package:mop/mop.dart';
import 'package:teemo_coca/common/screen_adapter.dart';
import 'package:teemo_coca/common/util.dart';
import '../../componse/sfa_app_bar.dart';
import '../../them/base_them.dart';
import 'controller.dart'; 

class LoginPage extends GetView<LoginController> {
  const LoginPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const SfaAppBar(
        showAppBar: false,
      ),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.only(left: 20.0, right: 20.0),
            decoration: BoxDecoration(
              image: DecorationImage(
                  image: AssetImage(
                      "${ThemeManager.currentTheme.assetsPath}loginbg.png"),
                  fit: BoxFit.fill),
            ),
            child: Column(
              children: [
                const SizedBox(height: 140),
                _buildAccountInput(),
                const SizedBox(height: 20),
                _buildPasswordInput(),
                const SizedBox(height: 10),
                _buildErrorMessage(),
                const SizedBox(height: 10),
                Obx(() => controller.isBiometricEnabled.value
                    ? _buildBiometricLoginButton()
                    : const SizedBox.shrink()),
                const SizedBox(height: 15),
                _buildLoginButton(),
                // _buildADLoginButton(),
                const SizedBox(height: 20),
                _buildBottomRow(),
                const SizedBox(height: 20),
                // _buildApplet(),
              ],
            ),
          )
        ],
      ),
      // bottomNavigationBar: const SizedBox(height: 50),
    );
  }

  Widget _buildApplet() {
    return TextButton(
        onPressed: () {
          Mop.instance.startApplet(RemoteAppletRequest(
              appletId: 'fc2615029311976901',
              apiServer: 'https://www.finclip.com'));
        },
        child: const Text('小程序'));
  }

  Widget _buildAccountInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.person),
            const SizedBox(width: 8),
            Text(
              'USERNAME'.tr,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller.usernameController,
          decoration: InputDecoration(
            hintText: 'USERNAME'.tr,
            filled: true,
          ),
          style: TextStyle(color: ThemeManager.currentTheme.textColorPrimary),
        ),
      ],
    );
  }

  Widget _buildPasswordInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.lock),
            const SizedBox(width: 8),
            Text(
              'PASSWORD'.tr,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Obx(() => TextField(
              controller: controller.passwordController,
              obscureText: !controller.isPasswordVisible.value,
              decoration: InputDecoration(
                hintText: 'PASSWORD'.tr,
                filled: true,
                suffixIcon: IconButton(
                  icon: Icon(
                    controller.isPasswordVisible.value
                        ? Icons.visibility
                        : Icons.visibility_off,
                  ),
                  onPressed: controller.togglePasswordVisibility,
                ),
              ),
            )),
      ],
    );
  }

  Widget _buildErrorMessage() {
    return Obx(() => controller.errorMessage.value.isNotEmpty
        ? Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              children: [
                Icon(Icons.error_outline, color: ThemeManager.currentTheme.errorColor, size: 20),
                const SizedBox(width: 4),
                Flexible(
                  child: Text(
                    controller.errorMessage.value,
                    style:TextStyle(color: ThemeManager.currentTheme.errorColor),
                    softWrap: true,
                    maxLines: 4,
                    overflow: TextOverflow.visible,
                  ),
                ),
              ],
            ),
          )
        : const SizedBox.shrink());
  }

  Widget _buildLoginButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: controller.login,
        child: Text(
          'LOGIN'.tr,
        ),
      ),
    );
  }

  Widget _buildADLoginButton() {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton(
        onPressed: controller.adLogin,
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
        ),
        child: Text('AD_LOGIN'.tr),
      ),
    );
  }

  Widget _buildBottomRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Obx(() => Checkbox(
                  value: controller.rememberMe.value,
                  onChanged: (value) => controller.rememberMe.value = value!,
                )),
            Text('REMEMBER_ME'.tr),
          ],
        ),
        IconButton(
            onPressed: controller.openSettings,
            icon: const Icon(
              Icons.settings,
              size: 30,
            )),
      ],
    );
  }

  Widget _buildBiometricLoginButton() {
    return SizedBox(
      width: 60,
      height: 60,
      child: IconButton(
        onPressed: controller.authenticateWithBiometrics,
        icon: const Icon(
          Icons.fingerprint,
          size: 45,
        ),
      ),
    );
  }
 
}
