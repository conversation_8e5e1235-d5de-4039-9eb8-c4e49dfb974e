import 'package:get/get.dart';
import 'package:flutter/material.dart';

/// 客户详情控制器
class CustomerDetailController extends GetxController {
  // 客户ID
  late final String customerId;
  
  // 客户基本信息
  final customer = {
    'id': '100001',
    'name': '上海万科集团',
    'avatar': 'https://images.unsplash.com/photo-1560250097-0b93528c311a',
    'industry': '房地产开发',
    'level': 'A级客户',
    'tags': ['重点客户', '地产行业'],
    'cooperationTime': '2年3个月',
    'orderCount': 12,
    'totalAmount': '¥1,230,000',
  }.obs;

  // 联系人列表
  final contacts = [
    {
      'name': '张总监',
      'isMain': true,
      'department': '市场部',
      'position': '总监',
      'phone': '18612345678',
    },
    {
      'name': '李先生',
      'isMain': false,
      'department': '采购部',
      'position': '经理',
      'phone': '18600000000',
    },
  ].obs;

  // 跟进记录列表
  final followUps = [
    {
      'id': '1',
      'type': '电话沟通',
      'date': '2023-06-15 09:30',
      'content': '与张总监沟通了智能办公解决方案，客户对新一代智能打印设备表现出兴趣，计划近期拜访演示产品。',
      'followUpPerson': '张经理',
      'status': '有效沟通',
      'images': [],
    },
    {
      'id': '2',
      'type': '客户拜访',
      'date': '2023-06-10 14:00',
      'content': '上门拜访客户，了解到客户计划在Q3季度更新全公司的办公设备，初步预算约200万元，已提供相关解决方案资料。',
      'followUpPerson': '张经理',
      'status': '有效沟通',
      'images': [
        'https://images.unsplash.com/photo-1600880292203-757bb62b4baf',
        'https://images.unsplash.com/photo-1573497620053-ea5300f94f21',
      ],
    },
    {
      'id': '3',
      'type': '邮件沟通',
      'date': '2023-05-25 10:15',
      'content': '发送了最新产品目录和价格表，客户回复会在内部讨论后给予反馈。',
      'followUpPerson': '张经理',
      'status': '信息传递',
      'images': [],
    },
  ].obs;

  // 当前选中的标签页
  final selectedTabIndex = 0.obs;

  // 标签页列表
  final tabs = ['跟进记录', '销售机会', '合同订单'];
  
  @override
  void onInit() {
    super.onInit();
    
    // 获取路由参数
    if (Get.arguments != null) {
      customerId = Get.arguments as String;
      // 在实际应用中，这里应该根据customerId从数据库或API获取客户详情
      print('加载客户ID: $customerId');
      loadCustomerDetails();
    } else {
      customerId = 'unknown';
      print('未提供客户ID，使用默认数据');
    }
  }
  
  // 加载客户详情
  void loadCustomerDetails() {
    // 模拟从后端加载数据
    // 实际应用中，这里应该调用API或从数据库获取数据
    
    // 为演示，根据customerId修改客户名称，以便确认参数传递成功
    if (customerId.startsWith('C')) {
      final Map<String, dynamic> updated = Map<String, dynamic>.from(customer);
      updated['name'] = '客户 $customerId';
      updated['industry'] = '零售业';
      if (customerId == 'C001') {
        updated['tags'] = ['高频', 'VIP', '连锁'];
      }
      customer.value = updated.cast<String, Object>();
    }
  }

  // 切换标签页
  void changeTab(int index) {
    selectedTabIndex.value = index;
  }

  // 添加跟进记录
  void addFollowUp(Map<String, Object> followUp) {
    followUps.insert(0, followUp);
  }

  // 拨打电话
  void makePhoneCall(String phone) {
    // 实际应用中需要使用平台通道调用原生功能
    print('拨打电话: $phone');
  }

  // 发送短信
  void sendMessage(String phone) {
    // 实际应用中需要使用平台通道调用原生功能
    print('发送短信: $phone');
  }

  // 返回上一页
  void goBack() {
    Get.back();
  }

  // 编辑客户信息
  void editCustomer() {
    print('编辑客户信息');
  }

  // 显示更多操作菜单
  void showMoreActions() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.share, color: Colors.blue),
              title: const Text('分享客户信息'),
              onTap: () {
                Get.back();
                print('分享客户信息');
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('删除客户'),
              onTap: () {
                Get.back();
                print('删除客户');
              },
            ),
            ListTile(
              leading: const Icon(Icons.history, color: Colors.orange),
              title: const Text('查看操作历史'),
              onTap: () {
                Get.back();
                print('查看操作历史');
              },
            ),
          ],
        ),
      ),
    );
  }
} 