import 'package:flutter/material.dart';
import 'package:get/get.dart'; 
// import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../them/base_them.dart';
import 'controller.dart';

/// 客户详情页面
class CustomerDetailPage extends GetView<CustomerDetailController> {
  const CustomerDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF6F6F6),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        title: Text(
          'customer_detail'.tr,
          style: const TextStyle(
            color: Colors.black,
            fontSize: 17,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.grey),
          onPressed: controller.goBack,
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit, color: Colors.grey),
            onPressed: controller.editCustomer,
          ),
          IconButton(
            icon: const Icon(Icons.more_horiz, color: Colors.grey),
            onPressed: controller.showMoreActions,
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildCustomerHeader(),
            _buildContactsSection(),
            _buildQuickActions(),
            _buildTabSection(),
            Obx(() => controller.selectedTabIndex.value == 0
                ? _buildFollowUpTimeline()
                : controller.selectedTabIndex.value == 1
                    ? _buildOpportunitiesSection()
                    : _buildOrdersSection()),
            const SizedBox(height: 80), // 为底部按钮留出空间
          ],
        ),
      ),
      bottomSheet: Container(
        width: double.infinity,
        padding:const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius:const BorderRadius.only(
              topLeft: Radius.circular(15),
            topRight: Radius.circular(15),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: () {
            // 添加跟进记录功能
            print("添加跟进记录");
          },
          style: ElevatedButton.styleFrom(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            padding:const EdgeInsets.symmetric(vertical: 12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.add, size: 16),
              const SizedBox(width: 5),
              Text(
                'add_follow_up'.tr,
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 客户头部信息区域
  Widget _buildCustomerHeader() {
    return Stack(
      children: [
        // 背景渐变色
        Container(
          height: 100,
          width: double.infinity,
          decoration:  BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [ThemeManager.currentTheme.primaryColor, ThemeManager.currentTheme.primaryColor],
            ),
          ),
        ),
        
        // 客户信息卡片
        Padding(
          padding: EdgeInsets.all(15),
          child: Obx(() => Card(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            elevation: 2,
            child: Padding(
              padding:const EdgeInsets.all(15),
              child: Column(
                children: [
                  // 客户头像和基本信息
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundImage: NetworkImage(controller.customer['avatar'] as String),
                      ),
                      const SizedBox(width: 15),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              controller.customer['name'] as String,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 5),
                            Text(
                              '${controller.customer['industry']} | ${controller.customer['level']}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 5),
                            Wrap(
                              spacing: 5,
                              children: (controller.customer['tags'] as List).map((tag) {
                                return _buildTag(tag);
                              }).toList(),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 15),
                  Divider(height: 1, color: Colors.grey[200]),
                  const SizedBox(height: 15),
                  
                  // 客户统计信息
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatItem(
                          'cooperation_time'.tr,
                          controller.customer['cooperationTime'] as String,
                        ),
                      ),
                      Expanded(
                        child: _buildStatItem(
                          'order_count'.tr,
                          '${controller.customer['orderCount']}单',
                          valueColor: const Color(0xFFFE2C55),
                        ),
                      ),
                      Expanded(
                        child: _buildStatItem(
                          'total_amount'.tr,
                          controller.customer['totalAmount'] as String,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          )),
        ),
      ],
    );
  }

  // 标签样式
  Widget _buildTag(String text) {
    Color backgroundColor;
    Color textColor;
    
    if (text.contains('重点')) {
      backgroundColor = const Color(0xFFFEEAF0);
      textColor = const Color(0xFFFE2C55);
    } else if (text.contains('地产')) {
      backgroundColor = const Color(0xFFE6F2FF);
      textColor = const Color(0xFF1876F2);
    } else {
      backgroundColor = const Color(0xFFF0F0F0);
      textColor = const Color(0xFF666666);
    }
    
    return Container(
      padding:const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 11,
          color: textColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  // 统计项样式
  Widget _buildStatItem(String label, String value, {Color? valueColor}) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[500],
          ),
        ),
        const SizedBox(height: 3),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: valueColor ?? Colors.black,
          ),
        ),
      ],
    );
  }

  // 联系人信息区域
  Widget _buildContactsSection() {
    return Container(
      margin:const EdgeInsets.all(15),
      padding:const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'contact_info'.tr,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 10),
          Obx(() => Column(
            children: controller.contacts.asMap().entries.map((entry) {
              final index = entry.key;
              final contact = entry.value;
              return Column(
                children: [
                  _buildContactItem(contact),
                  if (index < controller.contacts.length - 1)
                    Divider(height: 20, color: Colors.grey[200]),
                ],
              );
            }).toList(),
          )),
        ],
      ),
    );
  }

  // 联系人项样式
  Widget _buildContactItem(Map<String, dynamic> contact) {
    return Row(
      children: [
        Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: const Color(0xFFE6F2FF),
            borderRadius: BorderRadius.circular(18),
          ),
          child: const Icon(
            Icons.person,
            color: Color(0xFF1876F2),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    contact['name'] as String,
                    style: TextStyle(
                        fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (contact['isMain'] == true)
                    Text(
                      ' (${'main_contact'.tr})',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 3),
              Text(
                '${contact['department']} | ${contact['position']}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        ),
        Row(
          children: [
            _buildContactActionButton(
              Icons.phone,
              () => controller.makePhoneCall(contact['phone'] as String),
            ),
            const SizedBox(width: 8),
            _buildContactActionButton(
              Icons.message,
              () => controller.sendMessage(contact['phone'] as String),
            ),
          ],
        ),
      ],
    );
  }

  // 联系人操作按钮
  Widget _buildContactActionButton(IconData icon, VoidCallback onPressed) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(15),
      child: Container(
        width: 30,
        height: 30,
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(15),
        ),
        child: Icon(
          icon,
          size: 14,
          color: Colors.grey[600],
        ),
      ),
    );
  }

  // 快捷操作区域
  Widget _buildQuickActions() {
    return Container(
      padding:const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      child: Row(
        children: [
          _buildQuickActionButton(
            Icons.phone,
            'make_call'.tr,
            const Color(0xFFFEEAF0),
            const Color(0xFFFE2C55),
            () {},
          ),
          _buildQuickActionButton(
            Icons.edit_note,
            'add_follow_up'.tr,
            const Color(0xFFE6F2FF),
            const Color(0xFF1876F2),
            () {},
          ),
          _buildQuickActionButton(
            Icons.handshake,
            'add_opportunity'.tr,
            const Color(0xFFE6F7ED),
            const Color(0xFF07C160),
            () {},
          ),
          _buildQuickActionButton(
            Icons.calendar_today,
            'schedule_visit'.tr,
            const Color(0xFFF0E6FF),
            const Color(0xFF8A2BE2),
            () {},
          ),
        ],
      ),
    );
  }

  // 快捷操作按钮
  Widget _buildQuickActionButton(
    IconData icon,
    String text,
    Color backgroundColor,
    Color iconColor,
    VoidCallback onPressed,
  ) {
    return Expanded(
      child: InkWell(
        onTap: onPressed,
        child: Container(
            padding:const EdgeInsets.symmetric(vertical: 10),
          child: Column(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: backgroundColor,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                    size: 20,
                ),
              ),
              const SizedBox(height: 5),
              Text(
                text,
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[800],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 标签页区域
  Widget _buildTabSection() {
    return Container(
      color: Colors.white,
      child: Obx(() => Row(
        children: controller.tabs.asMap().entries.map((entry) {
          final index = entry.key;
          final tab = entry.value;
          final isSelected = controller.selectedTabIndex.value == index;
          
          return Expanded(
            child: InkWell(
              onTap: () => controller.changeTab(index),
              child: Container(
                padding:const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: isSelected ? const Color(0xFFFE2C55) : Colors.transparent,
                      width: 2,
                    ),
                  ),
                ),
                child: Text(
                  tab,
                  style: TextStyle(
                    fontSize: 14,
                    color: isSelected ? const Color(0xFFFE2C55) : Colors.grey[600],
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          );
        }).toList(),
      )),
    );
  }

  // 跟进记录时间线
  Widget _buildFollowUpTimeline() {
    return Padding(
      padding:const EdgeInsets.symmetric(horizontal: 15),
      child: Obx(() => Column(
        children: [
          ...controller.followUps.map((followUp) => _buildTimelineItem(followUp)).toList(),
          InkWell(
            onTap: () {
              // 查看更多记录
              print("查看更多记录");
            },
            child: Padding(
              padding:const EdgeInsets.symmetric(vertical: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'view_more_records'.tr,
                    style: TextStyle(
                      fontSize: 13,
                      color: const Color(0xFFFE2C55),
                    ),
                  ),
                  const SizedBox(width: 5),
                  const Icon(
                    Icons.keyboard_arrow_down,
                    color: Color(0xFFFE2C55),
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
        ],
      )),
    );
  }

  // 时间线项
  Widget _buildTimelineItem(Map<String, dynamic> followUp) {
    return Container(
      margin:const EdgeInsets.only(top: 15),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: const Color(0xFFFE2C55),
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              Container(
                width: 2,
                height: followUp['images'] != null && (followUp['images'] as List).isNotEmpty
                      ? 120
                    : 80,
                color: Colors.grey[300],
              ),
            ],
          ),
          const SizedBox(width: 15),
          Expanded(
            child: Container(
              padding:const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    followUp['date'] as String,
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey[500],
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    followUp['type'] as String,
                    style:const  TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    followUp['content'] as String,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Text(
                        '${'follow_up_person'.tr}: ${followUp['followUpPerson']}',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[500],
                        ),
                      ),
                      const SizedBox(width: 5),
                      Container(
                        padding:const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE6F7ED),
                            borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          followUp['status'] as String,
                          style: TextStyle(
                            fontSize: 11,
                            color: const Color(0xFF07C160),
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (followUp['images'] != null && (followUp['images'] as List).isNotEmpty)
                    Padding(
                      padding:const EdgeInsets.only(top: 10),
                      child: Wrap(
                        spacing: 8,
                        children: (followUp['images'] as List).map((image) {
                          return ClipRRect(
                            borderRadius: BorderRadius.circular(6),
                            child: Image.network(
                              image as String,
                              width: 60,
                              height: 60,
                              fit: BoxFit.cover,
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 销售机会区域（未选中时显示空白）
  Widget _buildOpportunitiesSection() {
    return Container(
      height: 200,
      alignment: Alignment.center,
      child: Text(
        'opportunities_coming_soon'.tr,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[500],
        ),
      ),
    );
  }

  // 合同订单区域（未选中时显示空白）
  Widget _buildOrdersSection() {
    return Container(
      height: 200,
      alignment: Alignment.center,
      child: Text(
        'orders_coming_soon'.tr,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[500],
        ),
      ),
    );
  }
} 