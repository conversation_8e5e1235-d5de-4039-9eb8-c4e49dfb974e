import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'controller.dart';

class ProfileSettingsView extends GetView<ProfileSettingsController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF6F6F6),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        title: Text('设置', style: TextStyle(color: Colors.black, fontSize: 18.sp, fontWeight: FontWeight.w600)),
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.grey, size: 20.sp),
          onPressed: () => Get.back(),
        ),
      ),
      body: ListView(
        padding: EdgeInsets.zero,
        children: [
          _buildProfileCard(),
          _buildSectionTitle('账号安全'),
          _buildListTile(Icons.lock, '修改密码', onTap: () {}),
          _buildListTile(Icons.phone_iphone, '绑定手机', trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('186****5678', style: TextStyle(fontSize: 14.sp, color: Colors.grey)),
              Icon(Icons.chevron_right, color: Colors.grey[400], size: 20.sp),
            ],
          ), onTap: () {}),
          _buildListTile(Icons.fingerprint, '生物识别', trailing: Switch(value: true, onChanged: (v) {})),
          _buildSectionTitle('通知设置'),
          _buildListTile(Icons.notifications, '应用通知', trailing: Switch(value: true, onChanged: (v) {})),
          _buildListTile(Icons.email, '邮件通知', trailing: Switch(value: false, onChanged: (v) {})),
          _buildListTile(Icons.sms, '短信通知', trailing: Switch(value: true, onChanged: (v) {})),
          _buildSectionTitle('通用设置'),
          _buildListTile(Icons.language, '语言', trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('简体中文', style: TextStyle(fontSize: 14.sp, color: Colors.grey)),
              Icon(Icons.chevron_right, color: Colors.grey[400], size: 20.sp),
            ],
          ), onTap: () {}),
          _buildListTile(Icons.dark_mode, '深色模式', trailing: Switch(value: false, onChanged: (v) {})),
          _buildListTile(Icons.storage, '清除缓存', trailing: Icon(Icons.chevron_right, color: Colors.grey[400], size: 20.sp), onTap: () {}),
          _buildSectionTitle('其他设置'),
          _buildListTile(Icons.info, '关于我们', trailing: Icon(Icons.chevron_right, color: Colors.grey[400], size: 20.sp), onTap: () {}),
          _buildListTile(Icons.headset_mic, '联系客服', trailing: Icon(Icons.chevron_right, color: Colors.grey[400], size: 20.sp), onTap: () {}),
          _buildListTile(Icons.privacy_tip, '隐私政策', trailing: Icon(Icons.chevron_right, color: Colors.grey[400], size: 20.sp), onTap: () {}),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFFFE2C55),
                elevation: 0,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
                side: const BorderSide(color: Color(0xFFFE2C55)),
                padding: EdgeInsets.symmetric(vertical: 14.h),
              ),
              onPressed: () {},
              child: Text('退出登录', style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600)),
            ),
          ),
          Center(
            child: Padding(
              padding: EdgeInsets.only(bottom: 24.h),
              child: Text('版本号: v1.0.0', style: TextStyle(color: Colors.grey[400], fontSize: 12.sp)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileCard() {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: Row(
        children: [
          CircleAvatar(
            radius: 32.r,
            backgroundImage: const NetworkImage('https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80'),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('张经理', style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600)),
                SizedBox(height: 4.h),
                Text('高级销售经理 | 华东大区', style: TextStyle(fontSize: 14.sp, color: Colors.grey)),
              ],
            ),
          ),
          Icon(Icons.chevron_right, color: Colors.grey[400], size: 24.sp),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      child: Text(title, style: TextStyle(fontSize: 13.sp, color: Colors.grey[500], fontWeight: FontWeight.w500)),
    );
  }

  Widget _buildListTile(IconData icon, String title, {Widget? trailing, VoidCallback? onTap}) {
    return Container(
      color: Colors.white,
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
        leading: Icon(icon, color: Colors.grey[500], size: 22.sp),
        title: Text(title, style: TextStyle(fontSize: 15.sp)),
        trailing: trailing,
        onTap: onTap,
        dense: true,
      ),
    );
  }
} 