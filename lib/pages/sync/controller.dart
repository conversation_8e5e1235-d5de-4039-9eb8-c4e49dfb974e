import 'dart:async';
import 'package:get/get.dart';
import '../../server/sync/eBestSyncServer.dart';
import '../../server/sync/sfdcSyncServer.dart';
import '../../server/sync/ISyncServer.dart';
import '../../server/sync/LocalSyncServer.dart';
import '../../common/sfa_event_bus/sfa_event_bus.dart';
import '../../common/sfa_event_bus/event_bus_s.dart';
import '../../common/sfa_dialog.dart';
import '../../router/routers_names.dart';
import '../../common/db_helper.dart';
import '../../sfaglobal.dart';

class SyncController extends GetxController {
  final currentCarouselIndex = 0.obs;
  final progress = 0.0.obs;
  final progressText = ''.obs;
  final carouselImages = [
  'https://images.unsplash.com/photo-1523205771623-e0faa4d2813d?ixlib=rb-0.3.5&ixid=eyJhcHBfaWQiOjEyMDd9&s=89719a0d55dd05e2deae4120227e6efc&auto=format&fit=crop&w=1953&q=80',
  'https://images.unsplash.com/photo-1508704019882-f9cf40e475b4?ixlib=rb-0.3.5&ixid=eyJhcHBfaWQiOjEyMDd9&s=8c6e5e3aba713b17aa1fe71ab4f0ae5b&auto=format&fit=crop&w=1352&q=80',
  'https://images.unsplash.com/photo-1519985176271-adb1088fa94c?ixlib=rb-0.3.5&ixid=eyJhcHBfaWQiOjEyMDd9&s=a0c8d632e977f94e5d312d9893258f59&auto=format&fit=crop&w=1355&q=80'
  ];  

  StreamSubscription<EventBusSyncProgress>? _subscription;
  ISyncServer? syncServer;
  @override 
  void onReady() {
    super.onReady();
    _initSyncServer();
    initEventBus();
    var syncType = Get.arguments['syncType'];
    print('syncType --------- : ${syncType}');
     syncData(syncType);
  }

  void _initSyncServer() {
    if (SfaGlobal.dataSource == 'SFDC') {
      syncServer = SfdcSyncServer();
    } else if (SfaGlobal.dataSource == 'eBest') {
      syncServer = Ebestsyncserver();
    } else if (SfaGlobal.dataSource == 'Local') {
      syncServer = LocalSyncServer();
    }
    update();
  }

  initEventBus() {  
    _subscription = eventBus.on<EventBusSyncProgress>().listen((event) {
      progress.value = event.progress.toDouble();
      progressText.value = event.msg;
    });
  }
  
  syncData(SyncType syncType) async {
    try {
      await syncServer!.syncData(syncType);
      // var clist =  await dbHelper.executeRead('select id,code,name,name2,shortname from customer');
      // print('customer --------- : ${clist} ');
      Get.offAllNamed(RoutesNames.main);
    } catch (e) {
      SfaDialog.bottomSheet('同步失败', e.toString(), onColse: () {
        Get.back();
      },onConfirm: () {
         syncData(syncType);
      });
    }
  }

  @override
  void onClose() {
    _subscription?.cancel();
    super.onClose();
  }
  
}