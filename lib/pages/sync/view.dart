import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '/componse/sfa_app_bar.dart';
import '../../them/base_them.dart';
import 'index.dart';


class SyncPage extends GetView<SyncController> {
  const SyncPage({super.key});
  // 主视图
  Widget _buildView() {
    return Column(
      children: [
        Expanded(
          child: CarouselSlider(
            options: CarouselOptions(
              height: double.infinity,
              viewportFraction: 1.0,
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 3),
              onPageChanged: (index, reason) {
                controller.currentCarouselIndex.value = index;
              },
            ),
            items: controller.carouselImages.map((asset) {
              return Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: NetworkImage(asset),
                    fit: BoxFit.cover,
                  ),
                ),
              );
            }).toList(),
          ),
        ),
        // 轮播指示器
        Obx(
          () => Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: controller.carouselImages.asMap().entries.map((entry) {
              return Container(
                width: 8.0,
                height: 8.0,
                margin:
                    const EdgeInsets.symmetric(vertical: 10.0, horizontal: 4.0),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: controller.currentCarouselIndex.value == entry.key
                      ? ThemeManager.currentTheme.primaryColor
                      : Colors.grey[300],
                ),
              );
            }).toList(),
          ),
        ),
        // 进度条和文字
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
          child: Obx(() => Column(
                children: [
                  LinearProgressIndicator(
                    value: controller.progress.value / 100,
                    minHeight: 8,
                    backgroundColor: Colors.grey[200],
                  ),
                  const SizedBox(height: 8),
                  Text('${controller.progress.value.toInt()}%'),
                ],
              )),
        ),
      ],
    );
  }
 

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SyncController>(
      init: SyncController(),
      id: "sync",
      builder: (_) {
        // 监听错误信息并弹出
        return Scaffold(
          appBar:const SfaAppBar(showAppBar: false),
            body: _buildView(),
          );
      },
    );
  }
}
