import 'package:get/get.dart';
import 'package:flutter/material.dart';

/// 销售机会控制器
class OpportunitiesController extends GetxController {
  // 销售机会列表
  final opportunities = <Map<String, dynamic>>[].obs;
  // 当前选中的阶段索引
  final selectedStageIndex = 0.obs;
  // 阶段标签
  final stages = ['全部', '初步接触', '需求确认', '方案报价', '商务谈判', '赢单'];
  // 搜索关键词
  final searchText = ''.obs;
  // 搜索控制器
  late TextEditingController searchController;

  @override
  void onInit() {
    super.onInit();
    searchController = TextEditingController();
    _generateTestData();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  void _generateTestData() {
    opportunities.assignAll([
      {
        'title': '上海万科办公设备采购项目',
        'stage': '商务谈判',
        'customer': '上海万科集团',
        'customerAvatar': 'https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
        'amount': '¥1,200,000',
        'owner': '张经理',
        'expectedDate': '2023-07-15',
        'winRate': 75,
        'lastUpdate': '2023-06-12',
      },
      {
        'title': '北京朗科IT基础设施更新',
        'stage': '方案报价',
        'customer': '北京朗科科技',
        'customerAvatar': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
        'amount': '¥800,000',
        'owner': '李销售',
        'expectedDate': '2023-08-20',
        'winRate': 60,
        'lastUpdate': '2023-06-10',
      },
      {
        'title': '广州佳能打印智能办公方案',
        'stage': '赢单',
        'customer': '广州佳能打印',
        'customerAvatar': 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
        'amount': '¥500,000',
        'owner': '王销售',
        'expectedDate': '2023-06-30',
        'winRate': 100,
        'lastUpdate': '2023-06-15',
      },
      {
        'title': '深圳鼎盛实业自动化设备升级',
        'stage': '需求确认',
        'customer': '深圳鼎盛实业',
        'customerAvatar': 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
        'amount': '¥650,000',
        'owner': '赵销售',
        'expectedDate': '2023-09-10',
        'winRate': 40,
        'lastUpdate': '2023-06-05',
      },
      {
        'title': '杭州网络科技网络升级项目',
        'stage': '初步接触',
        'customer': '杭州网络科技',
        'customerAvatar': 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
        'amount': '¥500,000',
        'owner': '钱销售',
        'expectedDate': '2023-10-01',
        'winRate': 20,
        'lastUpdate': '2023-06-01',
      },
    ]);
  }

  // 切换阶段
  void changeStage(int index) {
    selectedStageIndex.value = index;
  }

  // 搜索
  void search(String keyword) {
    searchText.value = keyword;
  }

  // 获取筛选后的机会列表
  List<Map<String, dynamic>> get filteredOpportunities {
    final stage = stages[selectedStageIndex.value];
    return opportunities.where((item) {
      final matchStage = stage == '全部' || item['stage'] == stage;
      final matchSearch = searchText.value.isEmpty ||
        item['title'].toString().contains(searchText.value) ||
        item['customer'].toString().contains(searchText.value);
      return matchStage && matchSearch;
    }).toList();
  }
} 