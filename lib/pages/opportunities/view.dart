import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'controller.dart';

/// 销售机会页面
class OpportunitiesPage extends GetView<OpportunitiesController> {
  const OpportunitiesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF6F6F6),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        title: Text(
          '销售机会',
          style: TextStyle(
            color: Colors.pink[600],
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_alt_outlined, color: Colors.grey),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(Icons.add, color: Colors.grey),
            onPressed: () {},
          ),
        ],
      ),
      body: Column(
        children: [
          _buildStageTabs(),
          _buildSearchBar(),
          Expanded(child: _buildOpportunityList()),
        ],
      ),
    );
  }

  // 阶段选项卡
  Widget _buildStageTabs() {
    return Obx(() => Container(
      color: Colors.white,
      child: Row(
        children: List.generate(controller.stages.length, (index) {
          final isSelected = controller.selectedStageIndex.value == index;
          return Expanded(
            child: InkWell(
              onTap: () => controller.changeStage(index),
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 12.h),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: isSelected ? Colors.pink : Colors.transparent,
                      width: 2,
                    ),
                  ),
                ),
                child: Text(
                  controller.stages[index],
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: isSelected ? Colors.pink : Colors.grey[600],
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          );
        }),
      ),
    ));
  }

  // 搜索栏
  Widget _buildSearchBar() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: TextField(
        controller: controller.searchController,
        onChanged: controller.search,
        decoration: InputDecoration(
          prefixIcon: const Icon(Icons.search, size: 20, color: Colors.grey),
          hintText: '搜索商机名称、客户名称',
          contentPadding: EdgeInsets.symmetric(vertical: 0, horizontal: 12.w),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.r),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
        ),
        style: TextStyle(fontSize: 14.sp),
      ),
    );
  }

  // 商机列表
  Widget _buildOpportunityList() {
    return Obx(() {
      final list = controller.filteredOpportunities;
      if (list.isEmpty) {
        return Center(
          child: Text('暂无商机', style: TextStyle(color: Colors.grey, fontSize: 14.sp)),
        );
      }
      return ListView.separated(
        padding: EdgeInsets.symmetric(horizontal: 0, vertical: 0),
        itemCount: list.length,
        separatorBuilder: (_, __) => Divider(height: 1, color: Colors.grey[200]),
        itemBuilder: (context, index) {
          final item = list[index];
          return _buildOpportunityItem(item);
        },
      );
    });
  }

  // 单个商机项
  Widget _buildOpportunityItem(Map<String, dynamic> item) {
    Color stageColor;
    switch (item['stage']) {
      case '赢单':
        stageColor = Colors.green;
        break;
      case '方案报价':
        stageColor = Colors.blue;
        break;
      case '需求确认':
        stageColor = Colors.orange;
        break;
      case '商务谈判':
        stageColor = Colors.pink;
        break;
      case '初步接触':
        stageColor = Colors.grey;
        break;
      default:
        stageColor = Colors.pink;
    }
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  item['title'],
                  style: TextStyle(fontWeight: FontWeight.w600, fontSize: 15.sp),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: stageColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  item['stage'],
                  style: TextStyle(color: stageColor, fontSize: 12.sp),
                ),
              ),
            ],
          ),
          SizedBox(height: 6.h),
          Row(
            children: [
              CircleAvatar(
                radius: 12.r,
                backgroundImage: NetworkImage(item['customerAvatar']),
              ),
              SizedBox(width: 8.w),
              Text(
                item['customer'],
                style: TextStyle(fontSize: 13.sp, color: Colors.grey[700]),
              ),
            ],
          ),
          SizedBox(height: 6.h),
          Row(
            children: [
              _buildInfoColumn('金额', item['amount']),
              _buildInfoColumn('负责人', item['owner']),
              _buildInfoColumn('预计成交', item['expectedDate']),
            ],
          ),
          SizedBox(height: 6.h),
          Row(
            children: [
              Text('赢率', style: TextStyle(fontSize: 12.sp, color: Colors.grey[500])),
              SizedBox(width: 6.w),
              Expanded(
                child: LinearProgressIndicator(
                  value: (item['winRate'] as int) / 100.0,
                  backgroundColor: Colors.grey[200],
                  color: stageColor,
                  minHeight: 6.h,
                  borderRadius: BorderRadius.circular(3.r),
                ),
              ),
              SizedBox(width: 8.w),
              Text(
                '${item['winRate']}%',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: stageColor,
                  fontSize: 12.sp,
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('更新时间: ${item['lastUpdate']}', style: TextStyle(fontSize: 11.sp, color: Colors.grey[500])),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoColumn(String label, String value) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: TextStyle(fontSize: 11.sp, color: Colors.grey[500])),
          Text(value, style: TextStyle(fontSize: 13.sp, fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }
} 