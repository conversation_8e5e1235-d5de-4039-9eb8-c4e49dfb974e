import 'package:get/get.dart';
import 'package:flutter/material.dart';

/// 拜访计划控制器
class VisitsController extends GetxController {
  // 日历数据
  final calendarDays = <Map<String, dynamic>>[].obs;
  // 今日拜访计划
  final todayPlans = <Map<String, dynamic>>[].obs;

  @override
  void onInit() {
    super.onInit();
    _generateCalendarData();
    _generateTodayPlans();
  }

  void _generateCalendarData() {
    // 这里只生成静态数据，实际可根据当前月份动态生成
    calendarDays.assignAll(List.generate(42, (i) {
      return {
        'date': (i % 30) + 1,
        'currentMonth': i >= 2 && i < 32,
        'isToday': i == 15,
        'hasPlan': i == 10 || i == 15 || i == 20,
      };
    }));
  }

  void _generateTodayPlans() {
    todayPlans.assignAll([
      {
        'title': '上海万科集团拜访',
        'time': '10:30 - 12:00',
        'address': '上海市浦东新区世纪大道1号',
        'contact': '张总监 (18612345678)',
        'desc': '沟通智能办公设备解决方案，准备产品演示和方案文档。',
        'iconColor': Colors.pink,
      },
      {
        'title': '北京朗科科技拜访',
        'time': '14:30 - 16:00',
        'address': '北京市海淀区中关村南大街5号',
        'contact': '李经理 (13987654321)',
        'desc': '跟进IT基础设施更新项目，带上技术支持同事一起参与。',
        'iconColor': Colors.blue,
      },
    ]);
  }
} 