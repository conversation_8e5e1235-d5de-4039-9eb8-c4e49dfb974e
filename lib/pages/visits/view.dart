import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'controller.dart';

/// 拜访计划页面
class VisitsPage extends GetView<VisitsController> {
  const VisitsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF6F6F6),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        title: Text(
          '拜访计划',
          style: TextStyle(
            color: Colors.pink[600],
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today, color: Colors.grey),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(Icons.add, color: Colors.grey),
            onPressed: () {},
          ),
        ],
      ),
      body: Column(
        children: [
          _buildCalendar(),
          _buildTodayPlanTitle(),
          Expanded(child: _buildTodayPlans()),
        ],
      ),
    );
  }

  // 日历视图
  Widget _buildCalendar() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.all(16.r),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                icon: const Icon(Icons.chevron_left, color: Colors.grey),
                onPressed: () {},
              ),
              Row(
                children: [
                  Text('六月', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.sp)),
                  SizedBox(width: 4.w),
                  Text('2023', style: TextStyle(fontSize: 14.sp, color: Colors.grey[600])),
                ],
              ),
              IconButton(
                icon: const Icon(Icons.chevron_right, color: Colors.grey),
                onPressed: () {},
              ),
            ],
          ),
          SizedBox(height: 8.h),
          _buildCalendarGrid(),
        ],
      ),
    );
  }

  // 日历网格
  Widget _buildCalendarGrid() {
    final weekDays = ['日', '一', '二', '三', '四', '五', '六'];
    return Column(
      children: [
        Row(
          children: weekDays.map((d) => Expanded(
            child: Center(child: Text(d, style: TextStyle(fontSize: 12.sp, color: Colors.grey[500]))),
          )).toList(),
        ),
        SizedBox(height: 4.h),
        Obx(() => Wrap(
          spacing: 0,
          runSpacing: 0,
          children: List.generate(6 * 7, (i) {
            final day = controller.calendarDays[i];
            final isToday = day['isToday'] as bool;
            final isCurrentMonth = day['currentMonth'] as bool;
            final hasPlan = day['hasPlan'] as bool;
            return SizedBox(
              width: 36.w,
              height: 36.w,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: isToday ? Colors.pink : Colors.transparent,
                      shape: BoxShape.circle,
                    ),
                    padding: EdgeInsets.all(6.r),
                    child: Text(
                      '${day['date']}',
                      style: TextStyle(
                        color: isToday ? Colors.white : (isCurrentMonth ? Colors.black : Colors.grey[400]),
                        fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                        fontSize: 13.sp,
                      ),
                    ),
                  ),
                  if (hasPlan)
                    Container(
                      width: 6.w,
                      height: 6.w,
                      margin: EdgeInsets.only(top: 2.h),
                      decoration: BoxDecoration(
                        color: Colors.pink,
                        shape: BoxShape.circle,
                      ),
                    ),
                ],
              ),
            );
          }),
        )),
      ],
    );
  }

  // 今日拜访计划标题
  Widget _buildTodayPlanTitle() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text('今日拜访计划', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15.sp)),
          Text('2023年6月15日', style: TextStyle(fontSize: 13.sp, color: Colors.pink)),
        ],
      ),
    );
  }

  // 今日拜访计划列表
  Widget _buildTodayPlans() {
    return Obx(() {
      final list = controller.todayPlans;
      if (list.isEmpty) {
        return Center(child: Text('今日暂无拜访计划', style: TextStyle(color: Colors.grey, fontSize: 14.sp)));
      }
      return ListView.separated(
        itemCount: list.length,
        separatorBuilder: (_, __) => Divider(height: 1, color: Colors.grey[200]),
        itemBuilder: (context, index) {
          final item = list[index];
          return _buildPlanItem(item);
        },
      );
    });
  }

  // 单个拜访计划项
  Widget _buildPlanItem(Map<String, dynamic> item) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 36.r,
                height: 36.r,
                decoration: BoxDecoration(
                  color: (item['iconColor'] as Color).withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(Icons.group, color: item['iconColor'], size: 20.sp),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(item['title'], style: TextStyle(fontWeight: FontWeight.w600, fontSize: 15.sp)),
                    SizedBox(height: 2.h),
                    Row(
                      children: [
                        Icon(Icons.access_time, size: 14.sp, color: Colors.grey[500]),
                        SizedBox(width: 4.w),
                        Text(item['time'], style: TextStyle(fontSize: 12.sp, color: Colors.grey[600])),
                      ],
                    ),
                    Row(
                      children: [
                        Icon(Icons.location_on, size: 14.sp, color: Colors.grey[500]),
                        SizedBox(width: 4.w),
                        Text(item['address'], style: TextStyle(fontSize: 12.sp, color: Colors.grey[600])),
                      ],
                    ),
                    Row(
                      children: [
                        Icon(Icons.person, size: 14.sp, color: Colors.grey[500]),
                        SizedBox(width: 4.w),
                        Text('联系人: ${item['contact']}', style: TextStyle(fontSize: 12.sp, color: Colors.grey[600])),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(item['desc'], style: TextStyle(fontSize: 13.sp, color: Colors.grey[700])),
          SizedBox(height: 8.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              _buildActionBtn(Icons.map, '导航', Colors.grey[600]),
              SizedBox(width: 8.w),
              _buildActionBtn(Icons.phone, '联系', Colors.grey[600]),
              SizedBox(width: 8.w),
              _buildActionBtn(Icons.check_circle, '完成拜访', Colors.pink),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionBtn(IconData icon, String text, Color? color) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: color?.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        children: [
          Icon(icon, size: 14.sp, color: color),
          SizedBox(width: 4.w),
          Text(text, style: TextStyle(fontSize: 12.sp, color: color)),
        ],
      ),
    );
  }
} 