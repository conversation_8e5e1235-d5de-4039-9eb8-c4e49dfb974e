import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'sfa_widget_service.dart';
import 'widget_data_model.dart';

class HomeWidget1Page extends StatefulWidget {
  @override
  _HomeWidget1PageState createState() => _HomeWidget1PageState();
}

class _HomeWidget1PageState extends State<HomeWidget1Page> {
  WidgetDataModel? widgetData;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadWidgetData();
  }

  Future<void> _loadWidgetData() async {
    final data = await SfaWidgetService.generateWidgetData();
    setState(() {
      widgetData = data;
      isLoading = false;
    });
  }

  Future<void> _updateWidget() async {
    if (widgetData != null) {
      await SfaWidgetService.updateWidgetData(widgetData!);
      Get.snackbar('成功', 'Widget 已更新');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('桌面卡片预览'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _updateWidget,
          ),
        ],
      ),
      body: isLoading
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '桌面卡片效果预览',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 16),
                  _buildWidgetPreview(),
                  SizedBox(height: 24),
                  _buildInstructions(),
                ],
              ),
            ),
    );
  }

  Widget _buildWidgetPreview() {
    if (widgetData == null) return SizedBox();

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF1876F2), Color(0xFF42A5F5)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 头部信息
          Row(
            children: [
              Icon(Icons.business_center, color: Colors.white, size: 24),
              SizedBox(width: 8),
              Text(
                'Hub SFA',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Spacer(),
              Text(
                '${widgetData!.updateTime.hour.toString().padLeft(2, '0')}:${widgetData!.updateTime.minute.toString().padLeft(2, '0')}',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 16),
          
          // 用户信息
          Text(
            '${widgetData!.userName}，今日工作概览',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
            ),
          ),
          
          SizedBox(height: 16),
          
          // 核心数据区域
          Row(
            children: [
              // 拜访完成情况
              Expanded(
                child: _buildDataCard(
                  '拜访完成',
                  '${widgetData!.todayVisitCompleted}/${widgetData!.todayVisitPlanned}',
                  '${(widgetData!.visitCompletionRate * 100).round()}%',
                  Icons.location_on,
                ),
              ),
              
              SizedBox(width: 12),
              
              // 销售目标
              Expanded(
                child: _buildDataCard(
                  '销售目标',
                  '${(widgetData!.salesCompleted / 10000).toStringAsFixed(0)}万',
                  '${(widgetData!.salesCompletionRate * 100).round()}%',
                  Icons.trending_up,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 12),
          
          // 底部快捷信息
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildQuickInfo('今日订单', '${widgetData!.todayOrders}'),
              _buildQuickInfo('新增客户', '${widgetData!.weeklyNewCustomers}'),
              _buildQuickInfo('待办任务', '${widgetData!.pendingTasks}'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDataCard(String title, String value, String percentage, IconData icon) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: Colors.white, size: 16),
              SizedBox(width: 4),
              Text(
                title,
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            percentage,
            style: TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickInfo(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white70,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildInstructions() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '如何添加桌面卡片',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12),
            Text(
              '1. 长按手机桌面空白处\n'
              '2. 选择"小组件"或"Widget"\n'
              '3. 找到"Hub SFA"应用\n'
              '4. 选择大尺寸卡片并添加到桌面',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
