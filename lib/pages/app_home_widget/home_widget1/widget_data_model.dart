
class WidgetDataModel {
  final String userName;
  final DateTime updateTime;
  
  // 拜访数据
  final int todayVisitPlanned;
  final int todayVisitCompleted;
  final double visitCompletionRate;
  
  // 销售目标数据
  final double salesTarget;
  final double salesCompleted;
  final double salesCompletionRate;
  final int daysRemaining;
  
  // 其他业务数据
  final int todayOrders;
  final int weeklyNewCustomers;
  final int pendingTasks;

  WidgetDataModel({
    required this.userName,
    required this.updateTime,
    required this.todayVisitPlanned,
    required this.todayVisitCompleted,
    required this.visitCompletionRate,
    required this.salesTarget,
    required this.salesCompleted,
    required this.salesCompletionRate,
    required this.daysRemaining,
    required this.todayOrders,
    required this.weeklyNewCustomers,
    required this.pendingTasks,
  });

  Map<String, dynamic> toJson() {
    return {
      'userName': userName,
      'updateTime': updateTime.millisecondsSinceEpoch,
      'todayVisitPlanned': todayVisitPlanned,
      'todayVisitCompleted': todayVisitCompleted,
      'visitCompletionRate': visitCompletionRate,
      'salesTarget': salesTarget,
      'salesCompleted': salesCompleted,
      'salesCompletionRate': salesCompletionRate,
      'daysRemaining': daysRemaining,
      'todayOrders': todayOrders,
      'weeklyNewCustomers': weeklyNewCustomers,
      'pendingTasks': pendingTasks,
    };
  }

  factory WidgetDataModel.fromJson(Map<String, dynamic> json) {
    return WidgetDataModel(
      userName: json['userName'] ?? '',
      updateTime: DateTime.fromMillisecondsSinceEpoch(json['updateTime'] ?? 0),
      todayVisitPlanned: json['todayVisitPlanned'] ?? 0,
      todayVisitCompleted: json['todayVisitCompleted'] ?? 0,
      visitCompletionRate: (json['visitCompletionRate'] ?? 0.0).toDouble(),
      salesTarget: (json['salesTarget'] ?? 0.0).toDouble(),
      salesCompleted: (json['salesCompleted'] ?? 0.0).toDouble(),
      salesCompletionRate: (json['salesCompletionRate'] ?? 0.0).toDouble(),
      daysRemaining: json['daysRemaining'] ?? 0,
      todayOrders: json['todayOrders'] ?? 0,
      weeklyNewCustomers: json['weeklyNewCustomers'] ?? 0,
      pendingTasks: json['pendingTasks'] ?? 0,
    );
  }
}

