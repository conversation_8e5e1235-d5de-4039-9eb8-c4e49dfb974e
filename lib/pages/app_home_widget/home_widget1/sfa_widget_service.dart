
import 'package:home_widget/home_widget.dart'; 
import 'widget_data_model.dart';

class SfaWidgetService {
  static const String _androidWidgetName = 'SfaWidgetProvider';
  static const String _iOSWidgetName = 'SfaWidget';

  /// 初始化 Widget
  static Future<void> initializeWidget() async {
    await HomeWidget.setAppGroupId('group.com.ebestmobile.hubsfa1');
  }

  /// 更新 Widget 数据
  static Future<void> updateWidgetData(WidgetDataModel data) async {
    try {
      // 只用 HomeWidget.saveWidgetData 写入数据
      await _updateWidgetDisplay(data);
      // 刷新 Widget
      await HomeWidget.updateWidget(
        androidName: _androidWidgetName,
        iOSName: _iOSWidgetName,
      );
      print('Widget 数据更新成功');
    } catch (e) {
      print('Widget 数据更新失败: $e');
    }
  }

  /// 获取当前 Widget 数据
  static Future<WidgetDataModel?> getWidgetData() async {
    try {
      // 只从 HomeWidget 读取数据
      final userName = await HomeWidget.getWidgetData<String>('userName', defaultValue: '');
      final updateTimeStr = await HomeWidget.getWidgetData<String>('updateTime', defaultValue: '00:00');
      final todayVisitPlanned = await HomeWidget.getWidgetData<int>('todayVisitPlanned', defaultValue: 0);
      final todayVisitCompleted = await HomeWidget.getWidgetData<int>('todayVisitCompleted', defaultValue: 0);
      final visitCompletionRate = await HomeWidget.getWidgetData<int>('visitCompletionRate', defaultValue: 0);
      final salesTargetStr = await HomeWidget.getWidgetData<String>('salesTarget', defaultValue: '0万');
      final salesCompletedStr = await HomeWidget.getWidgetData<String>('salesCompleted', defaultValue: '0万');
      final salesCompletionRate = await HomeWidget.getWidgetData<int>('salesCompletionRate', defaultValue: 0);
      final daysRemaining = await HomeWidget.getWidgetData<int>('daysRemaining', defaultValue: 0);
      final todayOrders = await HomeWidget.getWidgetData<int>('todayOrders', defaultValue: 0);
      final weeklyNewCustomers = await HomeWidget.getWidgetData<int>('weeklyNewCustomers', defaultValue: 0);
      final pendingTasks = await HomeWidget.getWidgetData<int>('pendingTasks', defaultValue: 0);
      // 解析 updateTime
      final now = DateTime.now();
      final updateTime = updateTimeStr != null && updateTimeStr.contains(':')
          ? DateTime(now.year, now.month, now.day, int.parse(updateTimeStr.split(':')[0]), int.parse(updateTimeStr.split(':')[1]))
          : now;
      return WidgetDataModel(
        userName: userName ?? '',
        updateTime: updateTime,
        todayVisitPlanned: todayVisitPlanned ?? 0,
        todayVisitCompleted: todayVisitCompleted ?? 0,
        visitCompletionRate: (visitCompletionRate ?? 0) / 100.0,
        salesTarget: double.tryParse(salesTargetStr?.replaceAll('万', '') ?? '0') ?? 0,
        salesCompleted: double.tryParse(salesCompletedStr?.replaceAll('万', '') ?? '0') ?? 0,
        salesCompletionRate: (salesCompletionRate ?? 0) / 100.0,
        daysRemaining: daysRemaining ?? 0,
        todayOrders: todayOrders ?? 0,
        weeklyNewCustomers: weeklyNewCustomers ?? 0,
        pendingTasks: pendingTasks ?? 0,
      );
    } catch (e) {
      print('获取 Widget 数据失败: $e');
    }
    return null;
  }

  /// 从业务数据生成 Widget 数据
  static Future<WidgetDataModel> generateWidgetData() async {
    // 这里应该从你的业务控制器获取实际数据
    // 暂时使用模拟数据
    return WidgetDataModel(
      userName: '张经理',
      updateTime: DateTime.now(),
      todayVisitPlanned: 6,
      todayVisitCompleted: 4,
      visitCompletionRate: 0.67,
      salesTarget: 1000000,
      salesCompleted: 680000,
      salesCompletionRate: 0.68,
      daysRemaining: 12,
      todayOrders: 3,
      weeklyNewCustomers: 8,
      pendingTasks: 5,
    );
  }

  /// 更新 Widget 显示数据
  static Future<void> _updateWidgetDisplay(WidgetDataModel data) async {
    // 用户信息
    await HomeWidget.saveWidgetData<String>('userName', data.userName);
    // await HomeWidget.saveWidgetData<String>('updateTime', 
    //     '${data.updateTime.hour.toString().padLeft(2, '0')}:${data.updateTime.minute.toString().padLeft(2, '0')}');
    //时间格式 日期 时分
    await HomeWidget.saveWidgetData<String>('updateTime', '${data.updateTime.month.toString().padLeft(2, '0')}-${data.updateTime.day.toString().padLeft(2, '0')} ${data.updateTime.hour.toString().padLeft(2, '0')}:${data.updateTime.minute.toString().padLeft(2, '0')}');

    
    // 拜访数据
    await HomeWidget.saveWidgetData<int>('todayVisitPlanned', data.todayVisitPlanned);
    await HomeWidget.saveWidgetData<int>('todayVisitCompleted', data.todayVisitCompleted);
    await HomeWidget.saveWidgetData<int>('visitCompletionRate', (data.visitCompletionRate * 100).round());
    
    // 销售目标数据
    await HomeWidget.saveWidgetData<String>('salesTarget', '${(data.salesTarget / 10000).toStringAsFixed(0)}万');
    await HomeWidget.saveWidgetData<String>('salesCompleted', '${(data.salesCompleted / 10000).toStringAsFixed(0)}万');
    await HomeWidget.saveWidgetData<int>('salesCompletionRate', (data.salesCompletionRate * 100).round());
    await HomeWidget.saveWidgetData<int>('daysRemaining', data.daysRemaining);
    
    // 其他业务数据
    await HomeWidget.saveWidgetData<int>('todayOrders', data.todayOrders);
    await HomeWidget.saveWidgetData<int>('weeklyNewCustomers', data.weeklyNewCustomers);
    await HomeWidget.saveWidgetData<int>('pendingTasks', data.pendingTasks);
  }

  /// 处理 Widget 点击事件
  static Future<void> handleWidgetTap(String action) async {
    switch (action) {
      case 'visit_plan':
        // 跳转到拜访计划页面
        break;
      case 'dashboard':
        // 跳转到数据看板
        break;
      case 'orders':
        // 跳转到订单页面
        break;
      default:
        // 打开应用首页
        break;
    }
  }
}

