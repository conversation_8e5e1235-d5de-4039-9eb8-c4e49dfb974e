import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../them/base_them.dart';
import '../dashboard/view.dart';
import '../mop_kpi/view.dart';
import '../customers/view.dart';
import '../profile/view.dart';
class MainController extends GetxController {
  // 页面控制器
  late PageController pageController;
  // 当前页面索引
  final currentIndex = 0.obs;


   List<Map<String, String>> barIcons = [
   {
    'unselected': "${ThemeManager.currentTheme.assetsPath}icon_bar_home.png",
    'selected': "${ThemeManager.currentTheme.assetsPath}icon_bar_home_active.png",
   },
   {
    'unselected': "${ThemeManager.currentTheme.assetsPath}icon_bar_route.png",
    'selected': "${ThemeManager.currentTheme.assetsPath}icon_bar_route_active.png",
   },
   {
    'unselected': "${ThemeManager.currentTheme.assetsPath}icon_bar_flow.png",
    'selected': "${ThemeManager.currentTheme.assetsPath}icon_bar_flow_active.png",
   },
   {
    'unselected': "${ThemeManager.currentTheme.assetsPath}icon_bar_me.png",
    'selected': "${ThemeManager.currentTheme.assetsPath}icon_bar_me_active.png",
   }
  ];


  List<Widget>  pages= const [
              DashboardPage(),
              CustomersPage(),
              ProfilePage()
            ];
  
  
  @override
  void onInit() {
    super.onInit();
    pageController = PageController(initialPage: 0);
  }

  // 切换页面
  void changePage(int index) {
    currentIndex.value = index;
    pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
    update(["main"]);
  }

  @override
  void onClose() {
    pageController.dispose();
    super.onClose();
  }
} 