import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../common/screen_adapter.dart';
import '../../them/base_them.dart';
import '../home/<USER>';

import 'index.dart';
import '../../componse/sfa_app_bar.dart';

class MainPage extends GetView<MainController> {
   const MainPage({super.key});


  Widget _buildBottomNavigationBar() {
    return Obx(() => Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 10,
          ),
        ],
      ),
      child: BottomNavigationBar(
        
        items: [
          _buildNavItem(controller.barIcons[0]['unselected']!, controller.barIcons[0]['selected']!, 'HOME'.tr, 0),
          _buildNavItem(controller.barIcons[1]['unselected']!, controller.barIcons[1]['selected']!, 'ROUTE'.tr, 1),
          // _buildNavItem(controller.barIcons[2]['unselected']!, controller.barIcons[2]['selected']!, 'MiniProgram'.tr, 2),
          _buildNavItem(controller.barIcons[2]['unselected']!, controller.barIcons[2]['selected']!, 'ME'.tr, 3),
        ],
        currentIndex: controller.currentIndex.value,
        type: BottomNavigationBarType.fixed,
        onTap: controller.changePage,
      ),
    ));
  }

  BottomNavigationBarItem _buildNavItem(
      String unselectedIcon, String selectedIcon, String label, int index) {
    return BottomNavigationBarItem(
      icon: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return ScaleTransition(scale: animation, child: child);
        },
        child: Image.asset(controller.currentIndex.value == index ? selectedIcon : unselectedIcon),
      ),
      label: label,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<MainController>(
      init: MainController(),
      id: "main",
      builder: (_) {
        return Scaffold(
          appBar:const SfaAppBar(showAppBar: false),
          body: Obx(()=>
            controller.pages[controller.currentIndex.value]
          ),
          // body: PageView(
          //   controller: controller.pageController,
          //   physics: const NeverScrollableScrollPhysics(),
          //   children: const [
          //     // HomePage(),
          //     DashboardPage(),
          //     // RoutePlanPage(),
          //     CustomersPage(),
          //     ProfilePage()
          //   ],
          // ),
          bottomNavigationBar: _buildBottomNavigationBar(),
        );
      },
    );
  }
} 