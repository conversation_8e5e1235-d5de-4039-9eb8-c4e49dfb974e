import 'package:get/get.dart';
import '../pages/login/index.dart';
import '../pages/main/index.dart';
import '../pages/setting/index.dart';
import '../pages/sync/index.dart';
import '../pages/dashboard/index.dart';
import '../pages/customers/index.dart';
import '../pages/customer_detail/index.dart'; 
import '../pages/profile/index.dart';
import '../binds/bindings.dart';
import '../pages/profile_settings/index.dart';
import '../pages/route_plan_add/index.dart';
import 'routers_names.dart';

List<GetPage> phonePage = [
  // GetPage(
  //   name: RoutesNames.splash, 
  //   page: () => const SplashPage(),
  //   binding: SplashPageBinding(),
  // ),
  GetPage(
    name: RoutesNames.login, 
    page: () => const LoginPage(),
    binding: LoginPageBinding(),
  ),
  GetPage(
    name: RoutesNames.main, 
    page: () => const MainPage(),
  ),
  GetPage(
    name: RoutesNames.setting, 
    page: () => const SettingPage(),
  ),
  GetPage(
    name: RoutesNames.sync, 
    page: () => const SyncPage(),
  ),
  // 新增原型页面路由配置
  GetPage(
    name: RoutesNames.dashboard,
    page: () => const DashboardPage(),
    binding: DashboardPageBinding(),
  ),
  GetPage(
    name: RoutesNames.customers,
    page: () => const CustomersPage(),
    binding: CustomersPageBinding(),
  ),
  // 暂时注释未实现的页面路由
  GetPage(
    name: RoutesNames.customerDetail,
    page: () => const CustomerDetailPage(),
    binding: CustomerDetailPageBinding(),
  ),
  GetPage(
    name: RoutesNames.profile,
    page: () => const ProfilePage(),
    binding: ProfileBinding(),
  ),
  GetPage(
    name: RoutesNames.profileSetting,
    page: () =>  ProfileSettingsPage(),
    binding: ProfileSettingBinding(),
  ), 
  GetPage(
    name: RoutesNames.routePlanAdd,
    page: () => const RoutePlanAddPage(),
  ),
];
