import 'package:json_annotation/json_annotation.dart';  

@JsonSerializable()
class SFOauth2Model extends Object {

  @J<PERSON><PERSON><PERSON>(name: 'access_token')
  String? accessToken;

  @J<PERSON><PERSON>ey(name: 'instance_url')
  String? instanceUrl;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'id')
  String? id;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'token_type')
  String? tokenType;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'issued_at')
  String? issuedAt;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'signature')
  String? signature;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'error')
  String? error;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'error_description')
  String? errorDescription;
 
  SFOauth2Model(this.accessToken,this.instanceUrl,this.id,this.tokenType,this.issuedAt,this.signature,this.error,this.errorDescription);

  factory SFOauth2Model.fromJson(Map<String, dynamic> json) =>  SFOauth2Model(
      json['access_token'] as String?,
      json['instance_url'] as String?,
      json['id'] as String?,
      json['token_type'] as String?,
      json['issued_at'] as String?,
      json['signature'] as String?,
      json['error'] as String?,
      json['error_description'] as String?,
    );

  Map<String, dynamic> toJson() => {
      'access_token': accessToken,
      'instance_url': instanceUrl,
      'id': id,
      'token_type': tokenType,
      'issued_at': issuedAt,
      'signature': signature,
      'error': error,
      'error_description': errorDescription,
    };
}

  
