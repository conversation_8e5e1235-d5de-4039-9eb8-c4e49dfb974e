import 'package:json_annotation/json_annotation.dart'; 

@JsonSerializable()
class SfdcDownParamModel extends Object {

  @Json<PERSON><PERSON>(name: 'eventFlag')
  int eventFlag;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'syncType')
  int syncType;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'deviceId')
  String deviceId;

  @J<PERSON><PERSON><PERSON>(name: 'groupNumber')
  int groupNumber;

  SfdcDownParamModel(this.eventFlag,this.syncType,this.deviceId,this.groupNumber,);

  factory SfdcDownParamModel.fromJson(Map<String, dynamic> json) =>SfdcDownParamModel(
      json['eventFlag'] as int,
      json['syncType'] as int,
      json['deviceId'] as String,
      json['groupNumber'] as int,
    );

      Map<String, dynamic> toJson()=>{
      'eventFlag': eventFlag,
      'syncType': syncType,
      'deviceId': deviceId,
      'groupNumber': groupNumber,
    };
  }


