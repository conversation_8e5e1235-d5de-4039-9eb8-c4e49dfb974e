import 'package:json_annotation/json_annotation.dart'; 
  
@JsonSerializable()
class SfdcRecordsModel {

  @Json<PERSON>ey(name: 'values')
  List<String> values;

  @Json<PERSON>ey(name: 'name')
  String name;

  @Json<PERSON>ey(name: 'fields')
  String fields;

  @Json<PERSON>ey(name: 'lastObjId')
  String? lastObjId;
  

  SfdcRecordsModel(this.values,this.name,this.fields);

  factory SfdcRecordsModel.fromJson(Map<String, dynamic> json) =>
    SfdcRecordsModel(
      (json['values'] as List<dynamic>).map((e) => e as String).toList(),
      json['name'] as String,
      json['fields'] as String,
    )..lastObjId = json['lastObjId'] as String?;

  Map<String, dynamic> toJson() => {
    'values': values,
    'name': name,
    'fields': fields,
    'lastObjId': lastObjId,
  };

}

  
