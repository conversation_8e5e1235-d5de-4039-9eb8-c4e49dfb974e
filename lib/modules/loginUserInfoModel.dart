/**
 * 登录用户信息
 */

/// 登录用户信息完整模型
class LoginUserInfoModel {
  String token;
  String? instanceUrl;
  String version;
  String versionUrl;
  String timeZone;
  String userId;
  String userCode;
  String userName;
  String? position;
  String? routeNo;
  String? currentDateTime;
  List<_AppPage>? appPage;
  List<_SflashScreenCfgDto>? sflashScreenCfgDtoList;
  LoginUserInfo? currentUserInfo;
  LoginUserInfoModel({  
    required this.token,
    required this.instanceUrl,
    required this.version,
    required this.versionUrl,
    required this.timeZone,
    required this.userId,
    required this.userCode,
    required this.userName,
    this.position,
    this.routeNo,
    required this.currentDateTime,
    this.appPage,
    this.sflashScreenCfgDtoList,
    this.currentUserInfo,
  });

  factory LoginUserInfoModel.fromJson(Map<String, dynamic> json) {
    print('LoginUserInfoModel json: ${json}');
    return LoginUserInfoModel(
      instanceUrl: json['instanceUrl']?.toString() ?? '',
      token: json['token']?.toString() ?? '',
      version: json['version']?.toString() ?? '',
      versionUrl: json['versionUrl']?.toString() ?? '',
      timeZone: json['timeZone']?.toString() ?? '',
      userId: json['userId']?.toString() ?? '',
      userCode: json['userCode']?.toString() ?? '',
      userName: json['userName']?.toString() ?? '',
      currentDateTime: json['currentDateTime']?.toString(),
      appPage: (json['appPage'] as List?)?.map((e) => _AppPage.fromJson(e)).toList(),
      sflashScreenCfgDtoList: (json['sflashScreenCfgDtoList'] as List?)?.map((e) => _SflashScreenCfgDto.fromJson(e)).toList(),
      currentUserInfo: json['currentUserInfo']!=null && json['currentUserInfo'] is Map ? LoginUserInfo.fromJson(json['currentUserInfo']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'instanceUrl': instanceUrl,
      'version': version,
      'versionUrl': versionUrl,
      'timeZone': timeZone,
      'userId': userId,
      'userCode': userCode,
      'userName': userName,
      'position': position,
      'routeNo': routeNo,
      'currentDateTime': currentDateTime,
    };
  }
}

class LoginUserInfo {
  String? companyCode;
  String? userName;
  String? userCode;
  String? userId;
  String? loginName;
  String? position;
  String? routeNo;
  String? locationCode;
  String? picAvatar;
  String? mobile;
  String? tenantId;
  String? version;
  String? domainAccount;
  String? adDomainName;
  List<String>? subordinateRoutes;
  String? localDateTime;
  List<String>? routeCustomerIdList;
  List<_UserRole>? userRoleList;
  String? positionName;
  List<_OrganizationDto>? organizationDto;

  LoginUserInfo({
    this.companyCode,
    this.userName,
    this.userCode,
    this.userId,
    this.loginName,
    this.position,
    this.routeNo,
    this.locationCode,
    this.picAvatar,
    this.mobile,
    this.tenantId,
    this.version,
    this.domainAccount,
    this.adDomainName,
    this.subordinateRoutes,
    this.localDateTime,
    this.routeCustomerIdList,
    this.userRoleList,
    this.positionName,
    this.organizationDto,
  });

  factory LoginUserInfo.fromJson(Map<String, dynamic> json) {
    return LoginUserInfo(
      companyCode: json['companyCode']?.toString(),
      userName: json['userName']?.toString(),
      userCode: json['userCode']?.toString(),
      userId: json['userId']?.toString(),
      loginName: json['loginName']?.toString(),
      position: json['position']?.toString(),
      routeNo: json['routeNo']?.toString(),
      locationCode: json['locationCode']?.toString(),
      picAvatar: json['picAvatar']?.toString(),
      mobile: json['mobile']?.toString(),
      tenantId: json['tenantId']?.toString(),
      version: json['version']?.toString(),
      domainAccount: json['domainAccount']?.toString(),
      adDomainName: json['adDomainName']?.toString(),
      subordinateRoutes: (json['subordinateRoutes'] as List?)?.map((e) => e.toString()).toList(),
      localDateTime: json['localDateTime']?.toString(),
      routeCustomerIdList: (json['routeCustomerIdList'] as List?)?.map((e) => e.toString()).toList(),
      userRoleList: (json['userRoleList'] as List?)?.map((e) => _UserRole.fromJson(e)).toList(),
      positionName: json['positionName']?.toString(),
      organizationDto: (json['organizationDto'] as List?)?.map((e) => _OrganizationDto.fromJson(e)).toList(),
    );
  }
}

class _UserRole {
  String? roleID;
  String? roleCode;
  String? roleName;

  _UserRole({this.roleID, this.roleCode, this.roleName});

  factory _UserRole.fromJson(Map<String, dynamic> json) {
    return _UserRole(
      roleID: json['roleID']?.toString(),
      roleCode: json['roleCode']?.toString(),
      roleName: json['roleName']?.toString(),
    );
  }
}

class _OrganizationDto {
  String? id;
  String? code;
  String? nameCN;
  String? nameEN;
  int? level;
  String? parent;
  String? fullPath;
  String? buCode;
  bool? isDeleted;
  String? ownerID;

  _OrganizationDto({
    this.id,
    this.code,
    this.nameCN,
    this.nameEN,
    this.level,
    this.parent,
    this.fullPath,
    this.buCode,
    this.isDeleted,
    this.ownerID,
  });

  factory _OrganizationDto.fromJson(Map<String, dynamic> json) {
    return _OrganizationDto(
      id: json['id']?.toString(),
      code: json['code']?.toString(),
      nameCN: json['nameCN']?.toString(),
      nameEN: json['nameEN']?.toString(),
      level: json['level'] is int ? json['level'] : int.tryParse(json['level']?.toString() ?? ''),
      parent: json['parent']?.toString(),
      fullPath: json['fullPath']?.toString(),
      buCode: json['buCode']?.toString(),
      isDeleted: json['isDeleted'] is bool ? json['isDeleted'] : null,
      ownerID: json['ownerID']?.toString(),
    );
  }
}

class _AppPage {
  String? code;
  String? name;

  _AppPage({this.code, this.name});

  factory _AppPage.fromJson(Map<String, dynamic> json) {
    return _AppPage(
      code: json['Code']?.toString(),
      name: json['Name']?.toString(),
    );
  }
}

class _SflashScreenCfgDto {
  String? companyCode;
  int? showDuration;
  String? picSwitch;
  String? picSwitchUrl;

  _SflashScreenCfgDto({this.companyCode, this.showDuration, this.picSwitch, this.picSwitchUrl});

  factory _SflashScreenCfgDto.fromJson(Map<String, dynamic> json) {
    return _SflashScreenCfgDto(
      companyCode: json['companyCode']?.toString(),
      showDuration: json['showDuration'] is int ? json['showDuration'] : int.tryParse(json['showDuration']?.toString() ?? ''),
      picSwitch: json['picSwitch']?.toString(),
      picSwitchUrl: json['picSwitchUrl']?.toString(),
    );
  }
}