/*** 
 * 基础返回结果
 */
class BaseResultModel<T> {
  int code;
  int? limit;
  int? offset;
  int? rows;
  String? message;
  T? data;
  BaseResultModel({required this.code, this.limit, this.offset, this.rows, this.message, this.data});

  factory BaseResultModel.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic json)? fromJsonT,
  ) {
    return BaseResultModel(
      limit: json['limit'] is int ? json['limit'] : null,
      offset: json['offset'] is int ? json['offset'] : null,
      rows: json['rows'] is int ? json['rows'] : null,
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: json.containsKey('data') && fromJsonT != null && json['data'] != null
          ? fromJsonT(json['data'])
          : null,
    );
  }
}