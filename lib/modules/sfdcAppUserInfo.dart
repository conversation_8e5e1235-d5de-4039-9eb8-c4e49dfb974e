import 'package:json_annotation/json_annotation.dart'; 

@JsonSerializable()
class SfdcAppUserInfo {

  @J<PERSON><PERSON><PERSON>(name: 'userCode')
  String userCode;

  @Json<PERSON><PERSON>(name: 'userName')
  String userName;

  @J<PERSON><PERSON><PERSON>(name: 'firstName')
  String firstName;

  @J<PERSON><PERSON><PERSON>(name: 'name')
  String name;

  @Json<PERSON><PERSON>(name: 'sfaVersion')
  String sfaVersion;

  @Json<PERSON>ey(name: 'roleId')
  String roleId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'role')
  String role;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'userId')
  String userId;

  @Json<PERSON><PERSON>(name: 'sfdcApiVersion')
  String sfdcApiVersion;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'isSchemaCreated')
  bool isSchemaCreated;

  @Json<PERSON>ey(name: 'isInitialDataDownloaded')
  bool isInitialDataDownloaded;

  @<PERSON>son<PERSON>ey(name: 'deleteExistingRecords')
  bool deleteExistingRecords;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'downloadUrl')
  String downloadUrl;

  @Json<PERSON><PERSON>(name: 'lastOnlineLogin')
  String lastOnlineLogin;

  @<PERSON>son<PERSON><PERSON>(name: 'countryCode')
  String countryCode;

  @JsonKey(name: 'isSuperVisor')
  bool isSuperVisor;
 

  @JsonKey(name: 'serverTime')
  String serverTime;

  @JsonKey(name: 'isBatchDownload')
  String isBatchDownload;

  @JsonKey(name: 'maxDownloadGroupNumber')
  String maxDownloadGroupNumber;

  @JsonKey(name: 'RequireInitSync')
  String requireInitSync;

  @JsonKey(name: 'photoExpiredDay')
  int photoExpiredDay;

 @JsonKey(name: 'localTimeGMT')
  String localTimeGMT;
  
 @JsonKey(name: 'timeZoneSidKey')
  String timeZoneSidKey;

  SfdcAppUserInfo({required this.userCode,required this.userName,required this.firstName,required this.name,required this.sfaVersion,required this.roleId,required this.role,required this.userId,required this.sfdcApiVersion,required this.isSchemaCreated,required this.isInitialDataDownloaded,required this.deleteExistingRecords,required this.downloadUrl,required this.lastOnlineLogin,required this.countryCode,required this.isSuperVisor,required this.serverTime,required this.isBatchDownload,required this.maxDownloadGroupNumber,required this.requireInitSync,required this.photoExpiredDay
  ,required this.localTimeGMT
  ,required this.timeZoneSidKey});

  factory SfdcAppUserInfo.fromJson(Map<String, dynamic> json) => SfdcAppUserInfo(
      userCode: json['userCode'] as String,
      userName: json['userName'] as String,
      firstName: json['firstName'] as String,
      name: json['name'] as String,
      sfaVersion: json['sfaVersion'] as String,
      roleId: json['roleId'] as String,
      role: json['role'] as String,
      userId: json['userId'] as String,
      sfdcApiVersion: json['sfdcApiVersion'] as String,
      isSchemaCreated: json['isSchemaCreated'] as bool,
      isInitialDataDownloaded: json['isInitialDataDownloaded'] as bool,
      deleteExistingRecords: json['deleteExistingRecords'] as bool,
      downloadUrl: json['downloadUrl'] as String,
      lastOnlineLogin: json['lastOnlineLogin'] as String,
      countryCode: json['countryCode'] as String,
      isSuperVisor: json['isSuperVisor'] as bool,
      serverTime: json['serverTime'] as String,
      isBatchDownload: json['isBatchDownload'] as String,
      maxDownloadGroupNumber: json['maxDownloadGroupNumber'] as String,
      requireInitSync: json['RequireInitSync'] as String,
      photoExpiredDay: json['photoExpiredDay'] as int,
      localTimeGMT: json['localTimeGMT'] as String,
      timeZoneSidKey: json['timeZoneSidKey'] as String,
    );
  Map<String, dynamic> toJson() => {
      'userCode': userCode,
      'userName': userName,
      'firstName': firstName,
      'name': name,
      'sfaVersion': sfaVersion,
      'roleId': roleId,
      'role': role,
      'userId': userId,
      'sfdcApiVersion': sfdcApiVersion,
      'isSchemaCreated': isSchemaCreated,
      'isInitialDataDownloaded': isInitialDataDownloaded,
      'deleteExistingRecords': deleteExistingRecords,
      'downloadUrl': downloadUrl,
      'lastOnlineLogin': lastOnlineLogin,
      'countryCode': countryCode,
      'isSuperVisor': isSuperVisor,
      'serverTime': serverTime,
      'isBatchDownload': isBatchDownload,
      'maxDownloadGroupNumber': maxDownloadGroupNumber,
      'RequireInitSync': requireInitSync,
      'photoExpiredDay': photoExpiredDay,
      'localTimeGMT': localTimeGMT,
      'timeZoneSidKey': timeZoneSidKey,
    };
}

  
