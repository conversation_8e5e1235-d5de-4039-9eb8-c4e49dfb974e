import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../common/util.dart';
import '../common/common.dart';
import 'ebest_theme.dart';
import 'red_theme.dart';

//主题基类
abstract class BaseTheme{
   static const String fontName = 'Roboto';
   //资源路径  主题图片按规则命名 assets/images/{themeName}/
   String get assetsPath;
   //主色调 
   Color get primaryColor; 
   //辅助色调
   Color get secondaryColor;
   //背景色

   Color get backgroundColor;
   //卡片背景色
   Color get cardBackground;
   //文字颜色
   Color get textColorPrimary;
   Color get textColorSecondary;
   Color get  textColorTertiary;
   Color get errorColor;
   Color get successColor;
   Color get warningColor;

    
  //虚拟主题子类各自实现
  ThemeData get  themeData => ThemeData(
  // 颜色主题
  colorScheme: ColorScheme.light(
    primary: primaryColor, // 主色
    secondary: secondaryColor, // 辅助色
    surface: backgroundColor, // 背景色：白色
    onPrimary: textColorPrimary, // 主色上的文字颜色：白色
    onSecondary: textColorSecondary, // 辅助色上的文字颜色：黑色
    onSurface: textColorTertiary, // 背景色上的文字颜色：黑色
    error: errorColor, // 错误颜色
  ),
 // 应用栏主题
  appBarTheme: AppBarTheme(
    backgroundColor: primaryColor, // 应用栏背景色：深红色
    foregroundColor: Colors.white, 
     systemOverlayStyle: SystemUiOverlayStyle.light.copyWith(
          statusBarColor: primaryColor, // 状态栏颜色
          statusBarIconBrightness: Brightness.light, // 状态栏图标颜色
        ),// 应用栏文字颜色：白色
    titleTextStyle: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold), // 标题文字样式
    iconTheme: const IconThemeData(color: Colors.white), // 应用栏图标颜色：白色
    
  ),
  // PreferredSize 
  iconButtonTheme: IconButtonThemeData(
    style: IconButton.styleFrom(
      foregroundColor: primaryColor, // 按钮文字颜色：白色
    ),
  ),
  // 文本主题
  textTheme: const TextTheme(
    headlineLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.bold, color: Color(0xFFCD0720)),
    headlineMedium: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Color(0xFFCD0720)),
    bodyLarge: TextStyle(fontSize: 16, color: Colors.black),
    bodyMedium: TextStyle(fontSize: 14, color: Colors.grey),
    labelLarge: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Colors.white),
  ),
   
  // 按钮主题
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: primaryColor, // 按钮背景色：深红色
      foregroundColor: Colors.white, // 按钮文字颜色：白色
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12), // 内边距
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)), // 圆角
    ),
  ),
 
  outlinedButtonTheme: OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      foregroundColor: primaryColor, // 按钮文字颜色：深红色
      side: BorderSide(color: primaryColor), // 边框颜色：深红色
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12), // 内边距
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)), // 圆角
    ),
  ),

  // 输入框主题
  inputDecorationTheme: InputDecorationTheme(
    fillColor: Colors.white,
    border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
    focusedBorder: OutlineInputBorder(
      borderSide: BorderSide(color: primaryColor, width: 1), // 聚焦边框：深红色
      borderRadius: BorderRadius.circular(8),
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
    enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
    labelStyle: const TextStyle(color: Colors.grey), // 标签文字颜色：灰色
    hintStyle: const TextStyle(color: Colors.grey), // 提示文字颜色：灰色
  ),
  // 图标主题
  iconTheme: IconThemeData(
    color: primaryColor, // 图标颜色：深红色
    size: 24,
  ), 

  // 卡片主题
  cardTheme: CardThemeData(
    color: Colors.white, // 卡片背景色：白色
    elevation: 4, // 阴影
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)), // 圆角
  ),
  // 单选框主题
  radioTheme: RadioThemeData(
    fillColor: WidgetStateProperty.resolveWith<Color>(
      (Set<WidgetState> states) {
        if (states.contains(WidgetState.selected)) {
          return primaryColor; // 选中颜色：深红色
        }
        return Colors.grey; // 默认颜色：灰色
      },
    ),
    overlayColor: WidgetStateProperty.resolveWith<Color>(
      (Set<WidgetState> states) {
        if (states.contains(WidgetState.selected)) {
          return primaryColor; // 选中颜色：深红色
        }
        return Colors.grey; // 默认颜色：灰色
      },
    ),
  ),

  // 复选框主题
  checkboxTheme: CheckboxThemeData(
    fillColor: WidgetStateProperty.resolveWith<Color>(
      (Set<WidgetState> states) {
        if (states.contains(WidgetState.selected)) {
          return primaryColor; // 选中颜色：深红色
        }
        return Colors.grey; // 默认颜色：灰色
      },
    ),
     checkColor: WidgetStateProperty.resolveWith<Color>(
      (Set<WidgetState> states) {
        if (states.contains(WidgetState.selected)) {
          return Colors.white; // 选中颜色：白色
        }
        return Colors.grey; // 默认颜色：白色
      },
    ),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)), // 圆角
  ),

  // 进度指示器主题
  progressIndicatorTheme: ProgressIndicatorThemeData(
    color: primaryColor, // 进度条颜色：深红色
    linearTrackColor: Colors.grey[300], // 线性进度条背景色：灰色
  ),
  listTileTheme:ListTileThemeData(
    dense: true,
    contentPadding:  const EdgeInsets.symmetric(horizontal: 14),
    minTileHeight: 40,
    textColor: textColorPrimary,
    iconColor: primaryColor,
  ),

  // 开关主题
  switchTheme: SwitchThemeData(
    thumbColor: WidgetStateProperty.resolveWith<Color>(
      (Set<WidgetState> states) {
        if (states.contains(WidgetState.selected)) {
          return primaryColor; // 选中颜色：深红色
        }
        return Colors.grey; // 默认颜色：灰色
      },
    ),
    trackColor: WidgetStateProperty.resolveWith<Color>( 
      (Set<WidgetState> states) {
        if (states.contains(WidgetState.selected)) {
          return primaryColor.withOpacity(0.5); // 选中轨道颜色：深红色（半透明）
        }
        return Colors.grey[300]!; // 默认轨道颜色：灰色
      },
    ),
  ),

  // 下拉菜单主题
  dropdownMenuTheme: DropdownMenuThemeData(
    menuStyle: MenuStyle(
      backgroundColor: WidgetStateProperty.all(Colors.white), // 菜单背景色：白色
      elevation: WidgetStateProperty.all(4), // 阴影
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8), // 圆角
          side: const BorderSide(color: Colors.grey), // 边框颜色：灰色
        ),
      ),
    ),
    textStyle: const TextStyle(color: Colors.black), // 文字颜色：黑色
  ),

  // 分割线主题
  dividerTheme:  DividerThemeData(
    color: textColorTertiary, // 分割线颜色：灰色
    thickness: .5, // 分割线厚度
    space: 10, // 分割线上下间距
  ),

  bottomNavigationBarTheme: BottomNavigationBarThemeData(
    backgroundColor: backgroundColor,
    selectedItemColor: primaryColor,
    unselectedItemColor: textColorTertiary,
    selectedLabelStyle:  TextStyle(color: textColorTertiary),
    unselectedLabelStyle:  TextStyle(color: textColorTertiary),
    unselectedIconTheme:  IconThemeData(color: textColorTertiary),
  ),
  // dividerColor: primaryColor,
   
);
 
}

class ThemeManager {
  // 默认使用eBest主题
  static BaseTheme _currentTheme = RedTheme();
  static BaseTheme get currentTheme => _currentTheme;
  static void setTheme(BaseTheme theme) {
    _currentTheme = theme;
  }

    // 加载主题
  static Future<void> loadTheme() async {
    final savedTheme = await Util.getStorage(Common.localStorageKey.selectedTheme);
    print("savedTheme: $savedTheme");
    // 确保根据 savedTheme 正确实例化
    BaseTheme selectedTheme;

    // if (savedTheme == "Red") {
    //   print("RedTheme");
    //   selectedTheme = RedTheme();
    // } else {
    //   print("EBestTheme");
    //   selectedTheme = EBestTheme();
    // }
    selectedTheme = RedTheme();

    Get.changeTheme(selectedTheme.themeData);
    ThemeManager.setTheme(selectedTheme);
  }
}
 
