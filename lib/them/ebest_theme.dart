import 'package:flutter/material.dart';
import '/common/screen_adapter.dart';
import '/common/util.dart';
import 'base_them.dart';


class EBestTheme  extends BaseTheme { 
  @override
  String get assetsPath => "assets/images/ebest/";

  @override
  Color get primaryColor => Util.hexColor('#2395FB'); 
  @override
  Color get secondaryColor => Util.hexColor('#2395FB');

  Color get backgroundColor => Util.hexColor('#F6F6F6');
  @override
  Color get cardBackground => Util.hexColor('#FFFFFF');
  @override
  Color get textColorPrimary => Util.hexColor('#333333');
  @override
  Color get textColorSecondary => Util.hexColor('#666666');
  @override
  Color get textColorTertiary => Util.hexColor('#999999');
  @override
  Color get errorColor => Util.hexColor('#FF3B30');
  @override
  Color get successColor => Util.hexColor('#07C160');
  @override
  Color get warningColor => Util.hexColor('#FF9500');
}