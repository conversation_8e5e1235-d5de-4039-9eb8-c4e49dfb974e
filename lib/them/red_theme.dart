import 'package:flutter/material.dart';
import '/common/util.dart';
import 'base_them.dart';
class RedTheme extends BaseTheme {  
  @override
  String get assetsPath => "assets/images/red/";

  @override
  Color get primaryColor => Util.hexColor('#CD0720'); //乐可红 
  @override
  Color get secondaryColor => Util.hexColor('#F5A623'); //橙色
  @override
  Color get backgroundColor => Util.hexColor('#F6F6F6');
  @override
  Color get cardBackground => Util.hexColor('#FFFFFF');
  @override
  Color get textColorPrimary => Util.hexColor('#333333');
  @override
  Color get textColorSecondary => Util.hexColor('#666666');
  @override
  Color get textColorTertiary => Util.hexColor('#999999');
  @override
  Color get errorColor => Util.hexColor('#FF3B30');
  @override
  Color get successColor => Util.hexColor('#07C160');
  @override
  Color get warningColor => Util.hexColor('#FF9500');

   
}