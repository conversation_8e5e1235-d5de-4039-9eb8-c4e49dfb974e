import 'package:get/get.dart';
import '/common/db_helper.dart';
import 'db_script/db_script.dart';
import 'db_script/db_script_mapping_ebest.dart';
import '/common/util.dart';
import '/common/common.dart';
import '/common/sfa_event_bus/sfa_event_bus.dart';
import '/common/sfa_event_bus/event_bus_s.dart';
import 'ISyncServer.dart';
import 'baseSyncServer.dart';
import 'db_script/local_data.dart';

class LocalSyncServer implements ISyncServer {
  static int progress = 0;
  static DateTime? dwonloadStartTime;
  static DateTime? lastSyncTime;
  LocalSyncServer() {
    BaseSyncServer.setSyncType('LocalSync');
  }

  @override
  Future<void> syncData(SyncType syncType) async {
    lastSyncTime = DateTime.now();
    progress = 0;
    var results = [];
    switch (syncType) {
      case SyncType.initial:
        Util.removeStorage(Common.localStorageKey.lastSyncTime);
        await BaseSyncServer.dropTables();
        await BaseSyncServer.createTables();
        await BaseSyncServer.createDbIndex();
        break;
      case SyncType.incremental:
        break;
    }
    results = LocalData.generateAllMockData();
    await manipulateData(results);
    await _syncSuccessAfter(syncType);
  }

  static _syncSuccessAfter(SyncType syncType) async {
    await BaseSyncServer.syncSuccessAfter(syncType);
  }

  static Future<void> manipulateData(List<dynamic> results) async {
    // 将data中的数据插入到数据库中
    int index = 0;
    for (var item in results) {
      var data = item['data'];
      if (data == null || data.isEmpty) {
        continue;
      }
      BaseSyncServer.saveClientLog(
          'manipulateData item: ${item['dataType']} data: ${data.length} start');
      var tableObj = EBestDbScriptMapping.DbMapping.firstWhereOrNull(
          (t) => t.localTableName == item['dataType']);
      if (tableObj == null) {
        continue;
      }
      var querys = await processBulkTableData(item['data'], tableObj);
      if (querys.isNotEmpty) {
        await intoBulkTableDataByTable(querys);
        index++;
        var progressValue = (index / results.length * 100 * 0.2).toInt();
        eventBus.fire(EventBusSyncProgress(
            60 + progressValue, 'insert ${item['dataType']}'));
      }
      BaseSyncServer.saveClientLog(
          'manipulateData item: ${item['dataType']} end');
    }
    eventBus.fire(EventBusSyncProgress(100, 'manipulateData'));
  }

  // 将数据分批插入到数据库中
  static Future<void> intoBulkTableDataByTable(
      List<Map<String, dynamic>> querys) async {
    List<Future> arr = [];
    for (var i = 0; i < (querys.length / 10000).ceil(); i++) {
      var start = i * 10000;
      var end = (i + 1) * 10000;
      if (end > querys.length) {
        end = querys.length;
      }
      var subQuerys = querys.sublist(start, end);
      var p = dbHelper.executes(subQuerys);
      arr.add(p);
    }
    await Future.wait(arr);
  }

  static Future<List<Map<String, dynamic>>> processBulkTableData(records, DbMappingTableModule tableObj) async {
    var localTableName = tableObj.localTableName;
    BaseSyncServer.saveClientLog('processBulkTableData $localTableName start');
    if (records == null || records.length == 0) {
      return [];
    }
    List<Map<String, dynamic>> querys = [];
    var ids = {};
    var columns = tableObj.columnMapping.keys
        .map((e) => e.toString().toLowerCase())
        .toList();
    for (Map<String, dynamic> record in records) {
      var lowerCaseObj = Map.fromIterables(
          record.keys.map((e) => e.toString().toLowerCase()),
          record.values.toList());
      var idk =
          record.keys.toList().firstWhereOrNull((t) => t.toLowerCase() == 'id');
      // if (tableObj.localTableName == 'routedetail') {
        if (record.isNotEmpty && idk == null) {
          throw Exception('dataType [$localTableName] undefind id');
        }
        var id = record[idk]?.toString();
        if (ids[id] != null) {
          throw Exception('dataType [$localTableName] duplicate ‘${ids[id]}’');
        }
        record['RecordAction'] = '0'; //添加默认值
        ids[id] = id;
        var values = columns.map((t) => lowerCaseObj[t] ?? '').toList();
        var columnsString = columns.map((t) => '`$t`').join(',');
        var valString = values.map((t) => '?').join(',');

        var query =
            'INSERT OR REPLACE INTO [$localTableName] ($columnsString) values($valString)';
        querys.add({
          'query': query,
          'params': values,
        });
        print('query: $query');
      // }
     
    }
     BaseSyncServer.saveClientLog('processBulkTableData $localTableName end');
    return querys;
  }
}
