import 'package:get/get.dart';
import '../../common/common.dart';
import '/server/commonServer.dart';
import '/common/sfa_event_bus/sfa_event_bus.dart';
import '/common/sfa_event_bus/event_bus_s.dart';
import '/common/db_helper.dart';
import 'db_script/db_script.dart';
import 'db_script/db_script_mapping_sfdc.dart';
import 'db_script/db_script_mapping_ebest.dart';
import '/common/util.dart';
import 'ISyncServer.dart';

class BaseSyncServer {
  //保存日志
  static String syncType= 'Sync';
  static setSyncType(String type) {
    syncType = type;
  }
  static saveClientLog(msg) {
    print('saveClientLog ------------- $msg');
    CommonServer.saveClientAppLog(msg, syncType);
  }

  //清理已存在的所有表
  static Future<void> dropTables() async {
    BaseSyncServer.saveClientLog('dropTables start');
    var sql =
        "SELECT name FROM sqlite_master WHERE type ='table' and name not in('__WebKitDatabaseInfoTable__','sqlite_sequence')";
    var tables = await dbHelper.executeRead(sql);
    List<Map<String, dynamic>> querys = [];
    for (var table in tables) {
      querys.add({
        'query': 'DROP TABLE IF EXISTS [${table['name']}]',
      });
    }
    if (querys.isNotEmpty) {
      await dbHelper.executes(querys);
    }
    eventBus.fire(EventBusSyncProgress(5, 'dropTables'));
    BaseSyncServer.saveClientLog('dropTables end');
  }

  //创建表
  static Future<void> createTables() async {
    BaseSyncServer.saveClientLog('createTables start');
    List<Map<String, dynamic>> querys = [];
    for (var table in DbScript.dbScript) {
      var keys = table.dataObject.keys.toList();
      keys.add('recordaction');
      keys.add('guid');
      var keysString = keys.map((key) => '`${key.toLowerCase()}` TEXT').toList().join(',');
      querys.add({
        'query': 'CREATE TABLE IF NOT EXISTS [${table.objName}] ($keysString)',
      });
    }
    await dbHelper.executes(querys);
    eventBus.fire(EventBusSyncProgress(10, 'createTables'));
    // print('createTables end ${querys}');
    BaseSyncServer.saveClientLog('createTables end');
  }

  // 创建索引
  static Future<void> createDbIndex() async {
    BaseSyncServer.saveClientLog('createDbIndex start');
    List<Map<String, dynamic>> querys = [
      {
        'query':'CREATE INDEX IF NOT EXISTS [idx_customer_code1] ON [customer] ([code])'
      },
    ];
    
    // await dbHelper.executeRead('PRAGMA table_info(customer);');
    await dbHelper.executes(querys);
    eventBus.fire(EventBusSyncProgress(15, 'createDbIndex'));
    BaseSyncServer.saveClientLog('createDbIndex end');
  }

  static Future<void> clearAppLog() async {
    List<Map<String, dynamic>> querys = [
      {
        'query':
            'delete from appuploadlog where DateTime < datetime(\'now\', \'-60 day\')',
      },
    ];
    await dbHelper.executes(querys);
  }

  // 获取需要上传的数据
  static Future getUploadData({String? sourceType}) async {
    sourceType ??= Common.sourceType.eBest;
    var data = DbScript.dbScript;
    var recordActions = Common.recordActionValues.join(',');
    var modifiedRecords = [];

    if (data.isEmpty) {
      return [];
    }
    for (var i = 0; i < data.length; i++) {
      var item = data[i];
      var sql = '';
      var eBestObjName = item.objName;
      List fileds = [];
      DbMappingTableModule? columnMapping;
      if (sourceType == Common.sourceType.eBest) {
        columnMapping = EBestDbScriptMapping.DbMapping.firstWhereOrNull((t) =>t.localTableName.toLowerCase() == item.objName.toLowerCase());
      } else {
        columnMapping = SfdcDbScriptMapping.DbMapping.firstWhereOrNull((t) =>t.localTableName.toLowerCase() == item.objName.toLowerCase());
      }
      print('columnMapping --------- ${item.objName}  -- ${columnMapping}');
      if (columnMapping == null) {
        continue;
      }
      var sfdcObjName = columnMapping.objName;
      var sfdcColumnMapping = columnMapping.columnMapping;
      var columnMappingAfter = Map.fromEntries(sfdcColumnMapping.entries.where((e) => e.value != null && e.value != ''));
      var mappingColumns = columnMappingAfter.keys.map((t) => '`$t` as `${columnMappingAfter[t]}`').join(',');
      sql ='select $mappingColumns from [${item.objName}] where recordaction in($recordActions)';
      fileds = columnMappingAfter.values.toList();
      var resutl = await dbHelper.executeRead(sql);

      if (resutl.isNotEmpty) {
        modifiedRecords.add({
          'type': eBestObjName,
          'objName': sfdcObjName,
          'records': resutl,
          'fileds': fileds,
        });
      }
    }
    return modifiedRecords;
  }

  static Future<void> syncSuccessAfter(SyncType syncType,{ Future<void> Function()? func}) async {
    if (syncType == SyncType.incremental) {
      //清理日志
      await clearAppLog();
    }
    await func?.call();
    //保存当前系统版本和最后同步时间
    await Util.setStorage(Common.localStorageKey.lastSyncTime, DateTime.now().toString(), false);
    await Util.setStorage(Common.localStorageKey.appVersion, Common.appConf.appVersion, false);
  }

  static Future<SyncType> getSyncType(String username, String version) async {
    return SyncType.initial;
    //  return SyncType.incremental;
    String lastSyncTime = await Util.getStorage(Common.localStorageKey.lastSyncTime,false) ?? '';
    String appVersion = await Util.getStorage(Common.localStorageKey.appVersion,false) ?? ''; 
      final appUserInfo = await Util.getStorage(Common.localStorageKey.userCredentials);
    if (appVersion.isEmpty || lastSyncTime.isEmpty) {
      return SyncType.initial;
    } else if (appUserInfo!=null && appUserInfo['username'] != username) {
      return SyncType.initial;
    } else if (version != appVersion) {
      return SyncType.initial;
    }
    return SyncType.incremental;
  }
}
