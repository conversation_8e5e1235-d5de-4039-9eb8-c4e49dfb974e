import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'ISyncServer.dart';
import 'baseSyncServer.dart';
import '/common/util.dart';
import '/common/common.dart';
import '/common/db_helper.dart';
import '/modules/SfdcDownParamModel.dart';
import '/modules/SfdcRecordsModel.dart';
import '/server/apis.dart';
import '/common/http_helper.dart';
import 'db_script/db_script.dart';
import 'db_script/db_script_mapping_sfdc.dart';
import '/common/sfa_event_bus/sfa_event_bus.dart';
import '/common/sfa_event_bus/event_bus_s.dart';

class SfdcSyncServer implements ISyncServer {
  static int progress = 0;
  static DateTime? dwonloadStartTime;
  static DateTime? lastSyncTime;

  SfdcSyncServer() {
    BaseSyncServer.setSyncType('SfdcSync');
  }

  @override
  Future<void> syncData(SyncType syncType) async {
    lastSyncTime = DateTime.now();
    progress = 0;
    try {
      //同步
      if (syncType == SyncType.initial) {
        await Util.removeStorage(Common.localStorageKey.lastSyncTime);
        await BaseSyncServer.dropTables();
        await BaseSyncServer.createTables();
        await BaseSyncServer.createDbIndex();
      }
      if (syncType == SyncType.incremental) {
        await _uploadData();
      }
      //by group sync
      await _downloadData(syncType);
      //by table sync
      await _syncSuccessAfter(syncType);
    } catch (ex) {
      rethrow;
    } finally {}
  }

  Future<void> _uploadData() async {
    try {
      var data = await _getUploadData();
      if (data == null) {
        return;
      }
      var res = await _syncUploadData(data);
      if (res['Result'].toString() != "SUCCESS") {
        throw Exception(res);
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  ///同步成功你过后执行
  _syncSuccessAfter(syncType) async {
    await BaseSyncServer.syncSuccessAfter(syncType, func: () async {
      //同步成功调用接口
      await _updateSyncSuccess();
      if (syncType == SyncType.incremental) {
        //清理临时数据
        await _deleteTempRecords();
      }
    });
  }

  Future _processBulkTableData(List records, String tableName, List<String> fieldsArray) async {
    if (records.isEmpty) {
      return;
    }
    BaseSyncServer.saveClientLog('processBulkTableData tableName : $tableName start');

    List<Map<String, dynamic>> querys = [];

    var sfdcDbMapping = SfdcDbScriptMapping.DbMapping.firstWhereOrNull((element) =>element.objName.toLowerCase() == tableName.toLowerCase());
    print('sfdcDbMapping ------------- : $tableName  $sfdcDbMapping');
    //如果sfdcDbMapping为null，则不进行处理
    if (sfdcDbMapping == null) {
      return;
    }
    Map<String, dynamic> columnMapping = sfdcDbMapping.columnMapping;
    var columnMappingAfter = Map.fromEntries(
        columnMapping.entries.where((e) => e.value != null && e.value != ''));
    for (var record in records) {
      var res = _getTableValues(
          record, fieldsArray, columnMappingAfter, sfdcDbMapping.localTableName);
      var query = {
        "query":
            'INSERT OR REPLACE INTO [${sfdcDbMapping.localTableName}] (${res['fields']}) values(${res['temps']})',
        "params": res['values']
      };
      print('query ------------- : $query');
      querys.add(query);
    }

    if (querys.isNotEmpty) {
      await dbHelper.executes(querys);
    }
    BaseSyncServer.saveClientLog('processBulkTableData tableName : $tableName end');
  }

  Map<String, dynamic> _getTableValues(String record, List<String> fieldsArray,
      Map<String, dynamic?> columnMapping, String tableName) {
    //将record转换为map 将record的key转换为小写
    Map<String, String> recordMap = Map.fromIterables(
        fieldsArray.map((e) => e.toString().toLowerCase()),
        record.split(Common.appConf.splitChart));

    List<String> tempValues = [];
    List<String> values = [];
    // 过滤 value 不是 null 且不是空字符串的 key
    if (tableName == 'customer') {
      print('record ------------- tableName : $tableName  record : $record');
      print('recordMap ------------- : $columnMapping');
    }
    columnMapping.forEach((key, val) {
      String value = recordMap[val.toLowerCase()] ?? '';
      var val1 = value.trim();
      if (val1.toLowerCase() == "true") {
        val1 = '1';
      } else if (val1.toLowerCase() == "false") {
        val1 = '0';
      }
      //有效性字段转换
      if (key.toLowerCase() == "isdeleted") {
        val1 = val1 == "1" ? "0" : "1";
      }
      values.add(val1);
      tempValues.add("?");
    });
    return {
      "fields": columnMapping.keys.join(','),
      "temps": tempValues.join(','),
      "values": values,
    };
  }

  //开始同步前执行一些操作
  Future _beforSyncOption() async {
    await _syncClientLog2MobileLog();
  }

  ///将本地日志同步到sfdc对象  本地只保留进60天日志
  Future _syncClientLog2MobileLog() async {}

  ///登录后获取配置数据
  Future<Map<String, dynamic>> _getConfigData() async {
    var token = await Util.getStorage(Common.localStorageKey.token);
    Map<String, dynamic> headers = {"Authorization": "Bearer $token"};
    String instanceUrl = "https://test.salesforce.com";
    String url = "$instanceUrl${SfdcApis.configData}";
    try {
      var response = await HttpHelper.get(url, Options(headers: headers));
      return response;
    } catch (ex) {
      throw Exception(ex.toString());
    }
  }

  ///解析登录后获取配置数据
  List<Map<String, dynamic>> _loginConfigDataParse(
      List<SfdcRecordsModel> confingJson) {
    List<Map<String, dynamic>> dataMap = [];
    for (var table in confingJson) {
      List<Map<String, String>> result = [];
      var currentFields = table.fields.toString().split(',');
      String tableName = table.name.toString();
      List<String> records = table.values;
      for (var j = 0; j < records.length; j++) {
        Map<String, String> value = {};
        for (var i = 0; i < currentFields.length; i++) {
          var val = records[j].toString().split(Common.appConf.splitChart)[i];
          value[currentFields[i]] = val.trim();
        }
        result.add(value);
      }
      Map<String, dynamic> table_ = {"Name": tableName, "Data": result};
      dataMap.add(table_);
    }

    return dataMap;
  }

  //根据对象名获取配置数据
  List<Map<String, dynamic>> _getLoginConfigData(
      List<Map<String, dynamic>> dataMap, String tableName) {
    List<Map<String, String>> datas = ([]);
    for (var item in dataMap) {
      if (item["Name"] == tableName) {
        datas = item["Data"];
        return datas.map((m) => Map.of(m)).toList();
      }
    }
    return datas;
  }

  //分组下载数据
  Future _downloadData(SyncType syncType) async {
    BaseSyncServer.saveClientLog(
        'sfdc downloadData syncType : $syncType start ');
    var groupCount =
        await Util.getStorage(Common.localStorageKey.sfdcDownloadGroupCount);
    if (groupCount == null) {
      groupCount = 10;
    } else {
      groupCount = int.parse(groupCount);
    }
    List<Future> promArray = [];
    var deviceId = await Util.getDeviceId();
    for (var i = 1; i <= groupCount; i++) {
      var downloadParam = SfdcDownParamModel(1, syncType == SyncType.initial ? 0 : 1, deviceId, i);
      promArray.add(_downloadDataByGroup(downloadParam));
    }

    promArray.map((f) {
      return f.whenComplete(() {
        progress++;
        var progressValue = (progress / groupCount * 100 * 0.6).toInt();
        eventBus.fire(EventBusSyncProgress(20 + progressValue, ''));
      });
    }).toList();

    var records = await Future.wait(promArray);
    List newRecords = [];
    for (var item in records) {
      for (var r in item['records']) {
        newRecords.add(r);
      }
    }
    // List newRecords =  [
    //     {
    //         "values": [
    //           "a0R41000001fP0VEAU ▏ebMobile__KPI9__c ▏2017-06-29 17:32:50 ▏true ▏true ▏それ以外 ▏3 ▏ディスプレイ展開可否 ▏Number ▏ ▏1 ▏サイドネット ▏1",
    //             "a0R41000001fP0UEAU ▏ebMobile__KPI7__c ▏2017-06-29 17:32:50 ▏true ▏true ▏カテゴリー付近 ▏1 ▏ディスプレイ展開可否 ▏Number ▏ ▏1 ▏サイドネット ▏1",
    //             "a0R41000001fP0TEAU ▏ebMobile__KPI8__c ▏2017-06-29 17:32:50 ▏true ▏true ▏レジ前 ▏2 ▏ディスプレイ展開可否 ▏Number ▏ ▏1 ▏サイドネット ▏1"
    //         ],
    //         "name": "ebMobile__ForecastKPIDetail__c",
    //         "fields": "Id,Name,LastModifiedDate,ebMobile__IsActive__c,ebMobile__IsEditable__c,ebMobile__KPIName__c,ebMobile__Sequence__c,ebMobile__Type__c,DataType__c,ExcludeCategory__c,SubTypeSequence__c,SubType__c,TypeSequence__c"
    //     }
    // ];
    await _manipulateData(newRecords);
  }

  //将下载到的数据写入表
  Future _manipulateData(List tables) async {
    var msg =
        "Downloaded data and manipulating ${tables.length} tables records";
    print(msg);
    for (var item in tables) {
       var table = SfdcRecordsModel.fromJson(item);
      
      var tableName = table.name,
          records = table.values,
          fieldsArray = table.fields.split(","),
          idFieldIndex = fieldsArray.indexOf("Id");
      if (idFieldIndex == -1) idFieldIndex = fieldsArray.indexOf("Id");
      if (idFieldIndex == -1) {
        var err =
            "Error processing data, table - $tableName Id field not found";
        throw Exception(err);
      }
      // print('item ------------- tableName: $tableName');
      await _processBulkTableData(records, tableName, fieldsArray);
    }
  }

  //分组下载数据接口
  Future _downloadDataByGroup(SfdcDownParamModel params) async {
    var token = await Util.getStorage(Common.localStorageKey.token);
    var headers = {
      "Content-Type": "application/json",
      "Authorization": "Bearer $token"
    };
    var base64_ = '{"jsonData":"${Util.base64En(params)}"}';
    try {
      String url = await getSfdcUrl(SfdcApis.iMarketDownload);
      return HttpHelper.post(url,
          data: base64_, options: Options(headers: headers));
    } catch (ex) {
      throw Exception(ex.toString());
    }
  }

  //上传数据接口
  Future _syncUploadData(params) async {
    var token = await Util.getStorage(Common.localStorageKey.token);
    var headers = {
      "Content-Type": "application/json",
      "Authorization": "Bearer $token"
    };
    //params
    var base64_ = '{"jsonData":"${Util.base64En(params)}"}';

    try {
      var response = await HttpHelper.post(
          await getSfdcUrl(SfdcApis.uploadData),
          data: base64_,
          options: Options(headers: headers));
      return response;
    } catch (ex) {
      throw Exception(ex.toString());
    }
  }

  getSfdcUrl(url) async {
    var instanceUrl =
        await Util.getStorage(Common.localStorageKey.sfdcInstanceUrl);
    return instanceUrl + url;
  }

  //上传数据转换
  Future<Map<String, dynamic>> _getResultBySchema(
      List datas, String table, List fields) async {
    List jsonData = [];
    for (var item in datas) {
      List itemDatas = [];
      for (var filed in fields) {
        String val = item[filed] == "undefined" ||
                item[filed] == "null" ||
                item[filed] == null
            ? ""
            : item[filed].toString();
        itemDatas.add(val);
      }
      jsonData.add(itemDatas.join(Common.appConf.splitChart));
    }
    Map<String, dynamic> schemaDatas = {
      "fields": fields.join(","),
      "values": jsonData,
      "name": table,
    };
    return schemaDatas;
  }

  //获取需要上传的数据
  Future<Map<String, dynamic>?> _getUploadData() async {
    var data =
        await BaseSyncServer.getUploadData(sourceType: Common.sourceType.sfdc);
    List modifiedRecords = [];
    if (data.isEmpty) {
      return null;
    }

    for (var item in data) {
      var record = await _getResultBySchema(
          item['records'], item['objName'], item['fileds']);
      if (record.isNotEmpty) {
        modifiedRecords.add(record);
      }
    }
    return {"records": modifiedRecords};
  }

  Future _updateSyncSuccess() async {
    var deviceId = await Util.getDeviceId();
    var token = await Util.getStorage(Common.localStorageKey.token);

    var headers = {
      "Content-Type": "application/json",
      "Authorization": "Bearer $token"
    };
    var base64_ = '{"deviceId":"${Util.base64En(deviceId, false)}"}';
    try {
      //同步成功调用接口
      var response = await HttpHelper.post(
          await getSfdcUrl(SfdcApis.iMarketUpdateSyncSuccess),
          data: base64_,
          options: Options(headers: headers));
      String result = response["Result"];
      if (result != "SUCCESS") {
        throw Exception(result);
      }
      return response;
    } catch (ex) {
      throw Exception(ex.toString());
    }
  }

  /// 删除本地临时数据
  Future _deleteTempRecords() async {
    BaseSyncServer.saveClientLog('deleteTempRecords start');
    //上传成功后删除本地temp数据
    List<DbTableModule> data = DbScript.dbScript;
    List<Map<String, dynamic>> querys = [];
    List recordValue = [
      Common.recordAction.defaultRec,
      Common.recordAction.insertRec,
      Common.recordAction.updateRec
    ];
    for (var tab in data) {
      String table = tab.objName.toString().trim();
      String query =
          "DELETE FROM [$table] WHERE (Id LIKE 'GUID%' AND recordaction IN (${recordValue.join(',')}))";
      querys.add({"query": query, "params": []});
    }
    if (querys.isNotEmpty) {
      await dbHelper.executes(querys);
    }
    BaseSyncServer.saveClientLog('deleteTempRecords end');
  }

  ///检查时区
  // String? checkTimeZone(AppUserInfo userInfo) {
  //   print(userInfo.toJson());
  //   String timeZoneSidKey = userInfo.timeZoneSidKey;
  //   String localTimeGMT = userInfo.localTimeGMT;
  //   DateTime nowTime = DateTime.now();
  //   DateTime serverTime = DateTime.parse(userInfo.serverTime);
  //   String userTz = "GMT${localTimeGMT.split(" ")[2]}";
  //   var diff = nowTime.difference(serverTime);
  //   var min = diff.inMinutes;
  //   if ((min).abs() > 5) {
  //     return "Common.CHANGE_DEVICE_TIMEZONE".trParams(
  //         {"zone": "$timeZoneSidKey $userTz", "time": userInfo.serverTime});
  //   }
  //   return null;
  // }
}
