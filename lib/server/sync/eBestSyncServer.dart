import 'dart:convert';
import 'dart:ffi';
import 'package:get/get.dart';
import 'package:dio/dio.dart';
import 'package:crypto/crypto.dart';
import '/common/db_helper.dart';
import 'db_script/db_script.dart';
import 'db_script/db_script_mapping_ebest.dart';
import '/common/util.dart';
import '/common/common.dart';
import '/common/sfa_event_bus/sfa_event_bus.dart';
import '/common/sfa_event_bus/event_bus_s.dart';
import '/common/http_helper.dart';
import '/server/apis.dart';
import 'ISyncServer.dart';
import 'baseSyncServer.dart';

class Ebestsyncserver implements ISyncServer {
  static int progress = 0;
  static DateTime? dwonloadStartTime;
  static DateTime? lastSyncTime;
  Ebestsyncserver(){
    BaseSyncServer.setSyncType('eBestSync');
  }

  // 签名加密
  static Future<String> _getstrEncrypt(params) async {
    String token = await Util.getStorage(Common.localStorageKey.token) ?? '';
    var sla = Common.slaKey + token.substring(token.length - 10);
    var sha = sha256.convert(utf8.encode(params + sla));
    var strEncrypt = sha.toString();
    return strEncrypt;
  }

  // 同步下载数据
  static Future<dynamic> _syncDownloadData(params) async {
    var strEncrypt = await _getstrEncrypt(params);
    var token = await Util.getStorage(Common.localStorageKey.token);
    var headers = {
      'Content-Type': 'application/json',
      'authorization': 'Bearer $token',
      'x-signature': strEncrypt,
      'x-tenant-id': 'swire_tw_tb_q',
    };
    //params
    return HttpHelper.post(EBestApis.syncDownload,
        data: params, options: Options(headers: headers));
  }

  // 同步上传数据
  static Future<dynamic> _syncUploadData(params) async {
    var token = await Util.getStorage(Common.localStorageKey.token);
    var strEncrypt = await _getstrEncrypt(params);
    var headers = {
      'Content-Type': 'application/json',
      'x-signature': strEncrypt,
      'authorization': 'Bearer $token',
      'x-tenant-id': 'swire_tw_tb_q',
    };
    return HttpHelper.post(EBestApis.asyncUploadData,
        data: params, options: Options(headers: headers));
  }

  @override
  Future<void> syncData(SyncType syncType) async {
    lastSyncTime = DateTime.now();
    progress=0;
    var results = [];
    switch (syncType) {
      case SyncType.initial:
        Util.removeStorage(Common.localStorageKey.lastSyncTime);
        await BaseSyncServer.dropTables();
        await BaseSyncServer.createTables();
        await BaseSyncServer.createDbIndex();
        break;
      case SyncType.incremental:
        await uploadData();
        break;
    }
     results = await syncDataByTable(syncType);
    await manipulateData(results);
    await _syncSuccessAfter(syncType);
  }

  static _syncSuccessAfter(SyncType syncType) async {
    await BaseSyncServer.syncSuccessAfter(syncType);
  }

  static Future<List<dynamic>> syncDataByTable(SyncType syncType,
      {String routeNo = ""}) async {
    BaseSyncServer.saveClientLog('syncDataByTable start');
    List<Future> promArray = [];
    var lastSyncTime = '';
    if (syncType == SyncType.incremental) {
      print('syncDataByTable syncType == SyncType.incremental');
      lastSyncTime =
          await Util.getStorage(Common.localStorageKey.lastSyncTime,false) ?? '';

           print('syncDataByTable syncType == SyncType.incremental $lastSyncTime');
      if (lastSyncTime.isNotEmpty) {
        lastSyncTime = DateTime.parse(lastSyncTime)
            .subtract(const Duration(minutes: 30))
            .toString();
      }
    }
    // 同步数据
    for (var table in EBestDbScriptMapping.DbMapping) {
      BaseSyncServer.saveClientLog(
          'syncDataByTable table: ${table.localTableName} start');
      // table包含 isSync 字段，且isSync为true，则同步，否则不同步 默认同步
        bool isBatch = table.isBatch;
        print('${table.localTableName} lastSyncTime -- $lastSyncTime');
        var params = {
          'dataType': table.localTableName,
          'isBatch': isBatch,
          'lastSyncTime': lastSyncTime,
          'batchID': Util.getGuid().replaceAll('-', ''),
          'pageIndex': 1,
          'pageSize': isBatch ? Common.batchDownloadPageSize : 99999999,
          'routeNo': routeNo,
        };
        promArray.add(getAllDataByTable(params));
    }

      promArray.map((f) {
      return f.whenComplete(() {
        progress++;
        var progressValue = (progress / promArray.length * 100 * 0.6).toInt();
        eventBus.fire(EventBusSyncProgress(20 + progressValue, ''));
      });
    }).toList();

    var results = await Future.wait(promArray);
    BaseSyncServer.saveClientLog('syncDataByTable end');
    return results;
  }

  //递归获取接口全部数据
  static Future<dynamic> getAllDataByTable(Map<String, dynamic> params,
      {dynamic results}) async {
    results ??= {};
    var result = await _syncDownloadData(json.encode(params));
    // 如果 result 是字符串，先解析
    if (result is String) {
      result = json.decode(result);
      result['dataType'] = params['dataType'];
    }
    if (!params['isBatch']) {
      progress++;
      var progressValue =
          (progress / EBestDbScriptMapping.DbMapping.length * 100 * 0.6).toInt();
      eventBus.fire(
          EventBusSyncProgress(20 + progressValue, '${params['dataType']}'));
      return result;
    }

    if (result != null && results['data'] != null) {
      results['data'] = [...results['data'], ...result['data']];
    } else {
      if (result is Map) {
        results = Map<String, dynamic>.from(result);
      } else {
        results = <String, dynamic>{};
      }
    }
    if (result != null && result['code'] == 0) {
      if (result['data'] != null &&
          result['data'].length == params['pageSize']) {
        var nextParams = Map<String, dynamic>.from(params);
        nextParams['pageIndex'] = params['pageIndex'] + 1;
        return await getAllDataByTable(nextParams, results: results);
      }
    }
    progress++;
    var progressValue =
        (progress / EBestDbScriptMapping.DbMapping.length * 100 * 0.7).toInt();
    eventBus.fire(
        EventBusSyncProgress(20 + progressValue, '${params['dataType']}'));
    return results;
  }

  static Future<void> manipulateData(List<dynamic> results) async {
    // 将data中的数据插入到数据库中
    int index = 0;
    for (var item in results) {
      var data = item['data'];
      if (data == null || data.isEmpty) {
        continue;
      }
      BaseSyncServer.saveClientLog('manipulateData item: ${item['dataType']} data: ${data.length} start');
      var tableObj =
          EBestDbScriptMapping.DbMapping.firstWhereOrNull((t) => t.localTableName == item['dataType']);
      if (tableObj == null) {
        continue;
      }
      var querys =
          await processBulkTableData(item['data'],tableObj);
      if (querys.isNotEmpty) {
        await intoBulkTableDataByTable(querys);
        index++;
        var progressValue = (index / results.length * 100 * 0.2).toInt();
        eventBus.fire(EventBusSyncProgress(60 + progressValue, 'insert ${item['dataType']}'));
      }
      BaseSyncServer.saveClientLog('manipulateData item: ${item['dataType']} end');
    }
    eventBus.fire(EventBusSyncProgress(100, 'manipulateData'));
  }

  // 将数据分批插入到数据库中
  static Future<void> intoBulkTableDataByTable(
      List<Map<String, dynamic>> querys) async {
    List<Future> arr = [];
    for (var i = 0; i < (querys.length / 10000).ceil(); i++) {
      var start = i * 10000;
      var end = (i + 1) * 10000;
      if (end > querys.length) {
        end = querys.length;
      }
      var subQuerys = querys.sublist(start, end);
      var p = dbHelper.executes(subQuerys);
      arr.add(p);
    }
    await Future.wait(arr);
  }

  static Future<List<Map<String, dynamic>>> processBulkTableData(
      records,DbMappingTableModule tableObj) async {
    var localTableName = tableObj.localTableName;
    BaseSyncServer.saveClientLog('processBulkTableData $localTableName start');
    if (records == null || records.length == 0) {
      return [];
    }
    List<Map<String, dynamic>> querys = [];
    var ids = {};
    var columns = tableObj.columnMapping
        .keys
        .map((e) => e.toString().toLowerCase())
        .toList();
    for (Map<String, dynamic> record in records) {
      var lowerCaseObj = Map.fromIterables(
          record.keys.map((e) => e.toString().toLowerCase()),
          record.values.toList());
      var idk = record.keys.toList().firstWhereOrNull((t) => t.toLowerCase() == 'id');
      if (record.isNotEmpty && idk == null) {
        throw Exception('dataType [$localTableName] undefind id');
      }
      var id = record[idk]?.toString();
      if (ids[id] != null) {
        print('重复Id ${ids[id]}');
        throw Exception('dataType [$localTableName] duplicate ‘${ids[id]}’');
      }
      record['RecordAction'] = '0'; //添加默认值
      ids[id] = id;
      var values = columns.map((t) => lowerCaseObj[t] ?? '').toList();
      var columnsString = columns.map((t) => '`$t`').join(',');
      var valString = values.map((t) => '?').join(',');
      // print('columnsString: $columnsString');
      // print('valString: $valString');
      // print('values: $values');

      var query =
          'INSERT OR REPLACE INTO [$localTableName] ($columnsString) values($valString)';
      querys.add({
        'query': query,
        'params': values,
      });
    }
    BaseSyncServer.saveClientLog('processBulkTableData $localTableName end');
    return querys;
  }

  @override
  Future<void> uploadData() async {
    try {
      var data = await _getUploadData();
      if (data.isEmpty) {
        return;
      }
      var res = await _syncUploadData(json.encode(data));
      data = [];
      if (res['code'] != 0) {
        throw Exception(res);
      }
    } catch (e) {
      throw Exception(e);
    }
  }

  // 获取需要上传的数据
  static Future<List<dynamic>> _getUploadData() async {
    BaseSyncServer.saveClientLog('_getUploadData start');
    var modifiedRecords = [];

    var userInfo =
        await Util.getStorage(Common.localStorageKey.loginUserInfo) ?? {};
    var batchID = Util.getGuid().replaceAll('-', '');
    var uploadTime = DateTime.now().toString();

    var data = await BaseSyncServer.getUploadData(sourceType: Common.sourceType.eBest);
    for (var i = 0; i < data.length; i++) {
      var item = data[i];

      if (item.isNotEmpty) {
        modifiedRecords.add({
          'userID': userInfo['userId'],
          'uploadTime': uploadTime,
          'batchID': batchID,
          'companyCode': userInfo['companyCode'],
          'routeNo': userInfo['routeNo'],
          'type': item['type'],
          'records': item['records'],
        });
      }
    }
    BaseSyncServer.saveClientLog('_getUploadData end');
    return modifiedRecords;
  }
}
