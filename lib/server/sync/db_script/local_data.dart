import 'dart:math';
import 'db_script.dart';
import 'db_script_mapping_ebest.dart';

/// 本地数据生成
class LocalData {
  /// 用于生成 mock 数据的工具函数
  static dynamic _mockValue(String key, int index) {
    final k = key.toLowerCase();
    // ID/主键类
    if (k == 'id')return '${key}_$index';
    if (k == 'userid' || k == 'user_id') return 'user_${index + 1}';
    if (k == 'customerid' || k == 'customer_id') return 'customer_${index + 1}';
    if (k == 'parentid' || k == 'parent_id') return 'parent_${index + 1}';
    if (k == 'routeid' || k == 'route_id') return 'route_${index + 1}';
    if (k == 'visitid' || k == 'visit_id') return 'visit_${index + 1}';
    if (k == 'promotionid' || k == 'promotion_id') return 'promo_${index + 1}';
    if (k == 'processid' || k == 'process_id') return 'process_${index + 1}';
    // 编码/编号类
    if (k.contains('code')) return 'CODE${1000 + index}';
    if (k.contains('no')) return 'NO${1000 + index}';
    // 名称类
    if (k == 'name' || k.endsWith('name')) return '名称${index + 1}';
    if (k == 'shortname' || k == 'short_name') return '简称${index + 1}';
    if (k == 'fullnamecn' || k == 'fullnameen') return '全称${index + 1}';
    if (k == 'kaName' || k == 'kaname') return 'KA名称${index + 1}';
    // 图片/文件类
    if (k.contains('photo') || k.contains('img') || k.contains('logo'))
      return 'https://example.com/photo${index + 1}.png';
    if (k.contains('fileurl'))
      return 'https://example.com/file${index + 1}.png';
    if (k.contains('filename')) return 'file_${index + 1}.png';
    // 地址/路径类
    if (k.contains('address')) return '地址${index + 1}';
    if (k.contains('fullpath')) return '/mock/path/${index + 1}';
    // 联系方式
    if (k.contains('email')) return 'user${index + 1}@example.com';
    if (k.contains('phone')) return '1380000${1000 + index}';
    if (k.contains('mobile')) return '1390000${1000 + index}';
    // 经纬度
    if (k.contains('longitude')) return 113.0 + index * 0.01;
    if (k.contains('latitude')) return 23.0 + index * 0.01;
    // 时间/日期
    if (k.contains('time'))
      return '2024-06-01 12:00:${index.toString().padLeft(2, '0')}';
    if (k.contains('date'))
      return '2024-06-${(index % 30 + 1).toString().padLeft(2, '0')}';
    // 状态/类型/布尔
    if (k.contains('status')) return index % 2;
    if (k.contains('isdeleted')) return false;
    if (k.contains('isoffline')) return index % 2 == 0;
    if (k.contains('isprimary')) return index % 2 == 0;
    if (k.contains('isedit')) return index % 2 == 0;
    if (k.contains('issuggestorder')) return index % 2 == 0;
    if (k.contains('isbatch')) return index % 2 == 0;
    if (k.contains('iscopy')) return index % 2 == 0;
    if (k.contains('isonroute')) return index % 2 == 0;
    if (k.contains('required')) return index % 2 == 0;
    // 数值/金额/数量
    if (k.contains('qty') ||
        k.contains('amount') ||
        k.contains('price') ||
        k.contains('tax') ||
        k.contains('rebate') ||
        k.contains('duration')) return (index + 1) * 10;
    if (k.contains('sequence')) return index + 1;
    if (k.contains('level')) return (index % 5) + 1;
    // 备注/描述/扩展
    if (k.contains('remark')) return '备注${index + 1}';
    if (k.contains('description')) return '描述${index + 1}';
    if (k.contains('msg')) return '消息${index + 1}';
    if (k.contains('extra')) return '扩展${index + 1}';
    // 组/类型/分组
    if (k == 'group') return '组${index % 3 + 1}';
    if (k == 'type' || k.endsWith('type')) return index % 3;
    // 用户/角色
    if (k.contains('user')) return 'user${index + 1}';
    if (k.contains('role')) return 'role${index + 1}';
    // 平台/版本
    if (k.contains('platform')) return 'iOS';
    if (k.contains('version')) return '1.0.${index + 1}';
    // 其他常见字段
    if (k.contains('source')) return 'source${index % 3 + 1}';
    if (k.contains('label')) return '标签${index + 1}';
    if (k.contains('table')) return 'table${index % 5 + 1}';
    if (k.contains('field')) return 'field${index + 1}';
    if (k.contains('value')) return '值${index + 1}';
    if (k.contains('group')) return '组${index % 3 + 1}';
    // 默认字符串
    return 'mock_${key}${index + 1}';
  }

  /// 强关联mock数据生成
  static Map<String, List<Map<String, dynamic>>> generateStrongMockData() {
    final rand = Random();
    // 1. 主表先生成
    // channel
    final channelList = List.generate(
        10,
        (i) => {
              'Code': 'CHANNEL${i + 1}',
              'name': '渠道${i + 1}',
              'ID': 'channel_${i + 1}',
            });
    // category
    final categoryList = List.generate(
        10,
        (i) => {
              'ID': 'category_${i + 1}',
              'Name': '品类${i + 1}',
            });
    // brand
    final brandList = List.generate(
        10,
        (i) => {
              'ID': 'brand_${i + 1}',
              'Name': '品牌${i + 1}',
            });
    // customer
    final customerList = List.generate(
        20,
        (i) => {
              'ID': 'customer_${i + 1}',
              'Code': 'CUST${1000 + i}',
              'Name': '客户${i + 1}',
              'Channel': channelList[rand.nextInt(channelList.length)]['Code'],
              'SubChannel': channelList[rand.nextInt(channelList.length)]
                  ['Code'],
            });
    // product
    final productList = List.generate(
        20,
        (i) => {
              'ID': 'product_${i + 1}',
              'Code': 'PROD${1000 + i}',
              'Name': '产品${i + 1}',
              'CategoryID': categoryList[rand.nextInt(categoryList.length)]
                  ['ID'],
              'BrandID': brandList[rand.nextInt(brandList.length)]['ID'],
            });
    // order
    final orderList = List.generate(
        20,
        (i) => {
              'OrderNo': 'ORDER${i + 1}',
              'CustomerCode': customerList[rand.nextInt(customerList.length)]
                  ['Code'],
              'CustomerID': customerList[rand.nextInt(customerList.length)]
                  ['ID'],
            });
    // 2. 子表生成，引用主表
    final routedetailList = List.generate(
        20,
        (i) => {
              'CustomerCode': customerList[rand.nextInt(customerList.length)]
                  ['Code'],
              'CustomerID': customerList[rand.nextInt(customerList.length)]
                  ['ID'],
            });
    final routecustomerList = List.generate(
        20,
        (i) => {
              'CustomerCode': customerList[rand.nextInt(customerList.length)]
                  ['Code'],
              'CustomerID': customerList[rand.nextInt(customerList.length)]
                  ['ID'],
            });
    final contactList = List.generate(
        20,
        (i) => {
              'CustomerCode': customerList[rand.nextInt(customerList.length)]
                  ['Code'],
              'CustomerID': customerList[rand.nextInt(customerList.length)]
                  ['ID'],
            });
    final orderLineList = List.generate(
        20,
        (i) => {
              'OrderNo': orderList[rand.nextInt(orderList.length)]['OrderNo'],
              'ProductCode': productList[rand.nextInt(productList.length)]
                  ['Code'],
              'ProductName': productList[rand.nextInt(productList.length)]
                  ['Name'],
            });
    // 3. 结果
    return {
      'channel': channelList,
      'category': categoryList,
      'brand': brandList,
      'customer': customerList,
      'product': productList,
      'order': orderList,
      'routedetail': routedetailList,
      'routecustomer': routecustomerList,
      'contact': contactList,
      'orderLine': orderLineList,
    };
  }

  static Map<String, dynamic> generateMockData(
      DbMappingTableModule table) {
      return {
        "dataType": table.objName,
        "data": List.generate(20, (index) {
          final row = <String, dynamic>{};
          table.columnMapping.forEach((key, required) {
            row[key] = _mockValue(key.toLowerCase(), index);
          });
          return row;
        }),
      };
  }

  static List<Map<String, dynamic>> generateAllMockData() {
    final tables = EBestDbScriptMapping.DbMapping;
    // 1. 获取强关联表数据
    final strongData = generateStrongMockData();
    // 2. 需要强关联的表名
    final strongTables = strongData.keys.toSet();

    // 3. 遍历所有表，合并数据
    final List<Map<String, dynamic>> result = [];
    for (final table in tables) {
      if (strongTables.contains(table.localTableName)) {
        // 拿到强mock的原始数据
        final strongList = strongData[table.localTableName] ?? [];
        // 补全所有字段
        final List<Map<String, dynamic>> fullList = List.generate(strongList.length, (i) {
          final row = <String, dynamic>{};
          table.columnMapping.forEach((key, required) {
            if (strongList[i].containsKey(key)) {
              row[key] = strongList[i][key]; // 强关联字段
            } else {
              row[key] = _mockValue(key, i); // 其他字段
            }
          });
          return row;
        });
        result.add({
          "dataType": table.objName,
          "data": fullList,
        });
      } else {
        // 用普通mock
        result.add(generateMockData(table));
      }
    }
    return result;
  }
}
