import 'db_script.dart';
/**
 * 
 * localTableName  本地表名
 * objName mapping的表名
 * columnMapping 字段映射 
 * 如果columnMapping Key是本地表字段 value为sfdc字段名，则将本地表字段映射到sfdc字段名
 * 如果columnMapping Key是本地表字段 value为null，则不进行映射
 */
class SfdcDbScriptMapping {
  static List<DbMappingTableModule> DbMapping = [
    DbMappingTableModule(
      localTableName: 'customer',
      objName: 'account',
      columnMapping: {  
        "Id": 'Id',
        "code": 'AccountNumber',
        "name": 'Name',
        "ShipTo": null,
        "channel": null,
        "kACode": null,
        "deliveryPlant": null,
        "truckTypeLimitation": null,
        "building": null,
        "bankGuarantee": null,
        "outletSalesType": null,
        "bRValidTo": null,
        "IsDeleted": "isActive__c",
      },
    ), 
  ];
}
