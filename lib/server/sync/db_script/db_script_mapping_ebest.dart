import 'db_script.dart';
/**
 * 
 * localTableName  本地表名
 * objName mapping的表明
 * columnMapping 字段映射 
 */
class EBestDbScriptMapping {
  static List<DbMappingTableModule> DbMapping = [
    DbMappingTableModule(
      localTableName: 'customer',
      objName: 'customer',
      isBatch: true,
      columnMapping: {
        "Id": 'ID', //主键
        "Code": 'Code', //客户编码
        "Name": 'Name', //客户名称
        "Name2": 'englishName', //客户名称2
        "ShortName": null, //客户简称
        "HeaderPhoto": 'picDoorHead', //头像
        "Address": 'shortAddressLine1', //地址
        "Channel": 'Channel', //渠道
        "SubChannel": 'SubChannel', //子渠道
        "ContactName": 'ContactName', //主联系人
        "ContactPhone": null, //主联系人电话
        "ContactEmail": null, //主联系人邮箱
        "KACode": 'KACode', //KA编码
        "KAName": null, //KA名称
        "Longitude": 'longitude', //经度
        "Latitude": 'latitude', //纬度
        "IsDeleted": 'IsDeleted', //是否删除
        "LastModifiedBy": 'LastModifiedBy', //最后修改人
        "LastModifiedTime": 'LastModifiedTime', //最后修改时间
        "CreatedBy": null, //创建人
        "CreatedTime": null, //创建时间
      },
    ),
    DbMappingTableModule(
      localTableName: 'dictionary',
      objName: 'dictionary',
      columnMapping: {
        "ID": 'ID', //主键
        "Type": 'Type', //类型
        "Value": 'Value', //值
        "Description": 'Description', //描述
        "Group": 'Group', //组
        "ParentValue": 'ParentValue', //父值
        "ParentType": 'ParentType', //父类型
        "Sequence": 'Sequence', //排序
        "Extra": 'Extra', //扩展属性
        "LabelPhoto": 'LabelPhoto', //标签照片
      }
    ),
    //门店计划
    DbMappingTableModule(
      localTableName: 'routedetail',
      objName: 'routedetail',
      columnMapping: {
        "Id": 'ID', //主键
        "CustomerCode": null, //客户编码
        "CustomerID": 'StoreID', //客户ID
        "StartTime": null, //开始时间
        "EndTime": null, //结束时间
        "VisitDate": 'Date', //拜访日期
        "Sequence": "Sequence", //序列
        "Type": 'Type', //类型
        "RouteNo": 'RouteNo', //路线编号
        "IsOffLine": 'IsOffLine', //是否计划外
        "IsDeleted": 'IsDeleted', //是否删除
        "LastModifiedBy": null, //最后修改人
        "LastModifiedTime": 'LastModifiedTime', //最后修改时间
        "CreatedBy": null, //创建人
        "CreatedTime": null, //创建时间
      },
    ),
    //门店渠道
    DbMappingTableModule(
      localTableName: 'channel',
      objName: 'channel',
      columnMapping: {
        "ID": 'ID',//主键
        "Code": 'Code',//渠道编码
        "name": 'name',//渠道名称
        "Parent": "Parent",//父渠道
        "GTCode": 'GTCode',//GT编码
        "GTText": 'GTText',//GT名称
        "IsDeleted": 'IsDeleted', //是否删除
        "LastModifiedBy": null, //最后修改人
        "LastModifiedTime": 'LastModifiedTime', //最后修改时间
        "CreatedBy": null, //创建人
        "CreatedTime": null, //创建时间
      },
    ),
    DbMappingTableModule(
      localTableName: 'routecustomer',
      objName: 'routecustomer',
      columnMapping: {
        "ID": 'ID',//主键
        "CustomerCode": 'StoreCode', //客户编码
        "CustomerID": 'StoreID', //客户ID
        "RouteNo": 'RouteNo',
        "IsDeleted": 'IsDeleted', //是否删除
        "LastModifiedBy": null, //最后修改人
        "LastModifiedTime": null, //最后修改时间
        "CreatedBy": null, //创建人
        "CreatedTime": null, //创建时间
      },
    ),
    //联系人
    DbMappingTableModule(
      localTableName: 'contact',
      objName: 'contact',
      columnMapping: {
        "ID": 'ID', //主键
        "Name": 'ContactName', //联系人名称
        "Office": 'Office', //办公室
        "Salutation": 'Salutation', //称呼
        "CustomerCode": 'StoreCode', //客户编码
        "CustomerID": 'CustomerID', //客户ID
        "Mobile": 'Mobile', //手机
        "Email": 'Email', //邮箱
        "IsPrimary": 'IsPrimary', //是否主联系人
        "type": 'type', //类型
        "IsDeleted": 'IsDeleted', //是否删除
        "LastModifiedBy": null, //最后修改人
        "LastModifiedTime": null, //最后修改时间
        "CreatedBy": null, //创建人
        "CreatedTime": null, //创建时间
      },
    ),
    //产品
    DbMappingTableModule(
      localTableName: 'product',
      objName: 'product',
      columnMapping: {
        "ID": 'ID', //主键
        "Code": 'Code', //产品编码
        "ShortNameEn": 'ShortNameEn', //产品简称
        "ShortNameCn": 'ShortNameCn', //产品简称
        "FullNameCn": 'FullNameCn', //产品全称
        "FullNameEn": 'FullNameEn', //产品全称
        "LargePhoto": 'LargePhoto', //大图
        "PackType": 'PackType', //包装类型
        "BarCode": 'BarCode', //条码
        "WeightUnit": 'WeightUnit', //重量单位
        "Meins": 'Meins', //计量单位
        "PackageID": 'PackageID', //包装ID
        "CategoryID": 'CategoryID', //品类ID
        "ProductType": 'ProductType', //产品类型
        "BrandID": 'BrandID', //品牌ID
        "BrandCode": null, //品牌编码
        "LargePhoto_Url": 'LargePhoto_Url', //大图URL
        "ThumbnailPhoto_Url": 'ThumbnailPhoto_Url', //缩略图URL
        "DetailPagePhoto_Url": 'DetailPagePhoto_Url', //详情页URL
        "IsDeleted": 'IsDeleted', //是否删除
        "LastModifiedBy": null, //最后修改人
        "LastModifiedTime": null, //最后修改时间
        "CreatedBy": null, //创建人
        "CreatedTime": null, //创建时间
      },
    ),
    //品类
    DbMappingTableModule(
      localTableName: 'category',
      objName: 'category',
      columnMapping: {
        "ID": 'ID', //主键
        "Code": 'Code', //品类编码
        "ParentID": 'ParentID', //父ID
        "Name": 'NameCn', //品类名称
        "NameEn": 'NameEn', //品类名称
        "Level": 'Level', //层级
        "Sequence": 'Sequence', //排序
        "IsDeleted": 'IsDeleted', //是否删除
        "LastModifiedBy": null, //最后修改人
        "LastModifiedTime": "lastModifiedTime", //最后修改时间
        "CreatedBy": null, //创建人
        "CreatedTime": null, //创建时间
      },
    ),
    //品牌
    DbMappingTableModule(
      localTableName: 'brand',
      objName: 'brand',
      columnMapping: {
        "ID": 'ID', //主键
        "Code": 'Code', //品牌编码
        "Name": 'NameCn', //品牌名称
        "NameEn": 'NameEn', //品牌名称英文
        "BrandLogoUrl": 'BrandLogoUrl', //品牌logoURL
        "CategoryID": 'CategoryID', //品类ID
        "CategoryCode": null, //品类编码
        "Sequence": 'Sequence', //排序
        "IsDeleted": 'IsDeleted', //是否删除
        "LastModifiedTime": 'LastModifiedTime', //最后修改时间
        "LastModifiedBy": null, //最后修改人
        "CreatedBy": null, //创建人
        "CreatedTime": null, //创建时间
      },
      ),  
    //动态视图
    DbMappingTableModule(
      localTableName: 'dynamicForm',
      objName: 'dynamicdetail',
      columnMapping: {
        "ID": 'id', //主键
        "DataTable": 'DataTable', //数据表
        "DataType": 'DataType', //数据类型
        "DataTypeDsc": 'DataTypeDsc', //数据类型描述
        "FieldName": 'FieldName', //字段名
        "Label": 'Label', //标签
        "LabelStyle": 'LabelStyle', //标签样式
        "ValueStyle": 'ValueStyle', //值样式
        "InputType": 'InputType', //输入类型
        "Format": 'Format', //格式
        "MinMax": 'MinMax', //最小最大
        "MinMaxLength": 'MinMaxLength', //最小最大长度
        "SuffixDefault": 'SuffixDefault', //后缀默认
        "IsEdit": 'IsEdit', //是否编辑
        "Sequence": 'Sequence', //序列
        "Required": 'Required', //是否必填
        "SourceField": 'SourceField', //源字段
        "DependFieldName": 'DependFieldName', //依赖字段
        "DependFieldValue": 'DependFieldValue', //依赖字段值
        "SourceTable": 'SourceTable', //源表
        "SourceColumn": 'SourceColumn', //源列
        "SubDataTypeDsc": 'SubDataTypeDsc', //子数据类型描述
        "SourceType": 'SourceType', //源类型
        "SourceValue": 'SourceValue', //源值
        "IsCopy": 'IsCopy', //是否复制
        "IsDeleted": 'IsDeleted', //是否删除
        "CreatedBy": 'CreatedBy', //创建人
        "CreatedTime": 'CreatedTime', //创建时间
        "LastModifiedBy": 'LastModifiedBy', //最后修改人
        "LastModifiedTime": 'LastModifiedTime', //最后修改时间
      },
    ),
  ///地里架构
  DbMappingTableModule(
    localTableName: 'location',
    objName: 'area',
    columnMapping: {
      "ID": 'ID',
      "Code": 'Code',
      "Name": 'Name',
      "OrgID": 'OrgID',
      "Parent": 'Parent',
      "FullPath": 'FullPath',
      "Level": 'Level',
      "IsDeleted": 'IsDeleted', //是否删除
      "CreatedBy": null, //创建人
      "CreatedTime": null, //创建时间
      "LastModifiedBy": null, //最后修改人
      "LastModifiedTime": null, //最后修改时间
    },
  ),
    //DependData
  DbMappingTableModule(
    localTableName: 'DependData',
    objName: 'DependData',
    columnMapping: {
      "ID": 'ID', //主键
      "Code": 'Code', //编码
      "Name": 'Name', //名称
      "NameEn": 'NameEn', //名称英文
      "TableName": 'TableName', //表名
      "Type": 'Type', //类型
      "Value": 'Value', //值
      "DependCode": 'DependCode', //依赖编码
      "DependValue": 'DependValue', //依赖值
      "DependentFieldName": 'DependentFieldName', //依赖字段名
      "FieldName": 'FieldName', //字段名
      "IsDeleted": 'IsDeleted', //是否删除
      "CreatedBy": null, //创建人
      "CreatedTime": null, //创建时间
      "LastModifiedBy": null, //最后修改人
      "LastModifiedTime": null, //最后修改时间
    },
  ),
  ];
}
