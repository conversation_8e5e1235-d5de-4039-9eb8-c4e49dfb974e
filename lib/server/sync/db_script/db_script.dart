/**
 * 数据库脚本 
 * ObjectName: 表名
 * isFull: 是否是全量表
 * dataObject: 表结构
 * 表结构是一个map，key是字段名，value是字段是否必须，true是必须，false是可选
 */
class DbScript {
  static List<DbTableModule> dbScript = [
    DbTableModule(
      objName: 'customer',
      dataObject: {
        "Id": true, //主键
        "Code": true, //客户编码
        "Name": true, //客户名称
        "Name2": false, //客户名称2
        "ShortName": false, //客户简称
        "HeaderPhoto": false, //头像
        "Address": false, //地址
        "Channel": false, //渠道
        "SubChannel": false, //子渠道
        "ContactName": false, //主联系人
        "ContactPhone": false, //主联系人电话
        "ContactEmail": false, //主联系人邮箱
        "KACode": false, //KA编码
        "KAName": false, //KA名称
        "Longitude": false, //经度
        "Latitude": false, //纬度
        "IsDeleted": true, //是否删除
        "LastModifiedBy": false, //最后修改人
        "LastModifiedTime": false, //最后修改时间
        "CreatedBy": false, //创建人
        "CreatedTime": false, //创建时间
      },
    ),
    DbTableModule(
      objName: 'dictionary',
      dataObject: {
        "ID": true, //主键
        "Type": true, //类型
        "Value": true, //值
        "Description": false, //描述
        "Group":false, //组
        "ParentValue": false, //父值
        "ParentType": false, //父类型
        "Sequence": false, //排序
        "Extra": false, //扩展属性
        "LabelPhoto": false, //标签照片
        "IsDeleted": true, //是否删除
        "LastModifiedBy": false, //最后修改人
        "LastModifiedTime": false, //最后修改时间
        "CreatedBy": false, //创建人
        "CreatedTime": false, //创建时间
      },
    ),
    //门店计划
    DbTableModule(
      objName: 'routedetail',
      dataObject: {       
        "Id": true, //主键
        "CustomerCode": true, //客户编码
        "CustomerID": true, //客户ID
        "StartTime": false, //开始时间
        "EndTime": false, //结束时间
        "VisitDate": true, //拜访日期
        "Sequence": true, //序列
        "Type": true, //类型
        "RouteNo": false, //路线编号
        "IsOffLine": false, //是否计划外
        "IsDeleted": true, //是否删除
        "LastModifiedBy": false, //最后修改人
        "LastModifiedTime": false, //最后修改时间
        "CreatedBy": false, //创建人
        "CreatedTime": false //创建时间
      },
    ),
    //门店渠道
    DbTableModule(
      objName: 'channel',
      dataObject: {
        "ID":true,//主键
        "Code": true,//渠道编码
        "name": true,//渠道名称
        "Parent": false,//父渠道
        "GTCode": false,//GT编码
        "GTText": false,//GT名称
        "IsDeleted": true, //是否删除
        "LastModifiedBy": false, //最后修改人
        "LastModifiedTime": false, //最后修改时间
        "CreatedBy": false, //创建人
        "CreatedTime": false, //创建时间
      },
    ),
    //覆盖 门店和路线关系
    DbTableModule(
      objName: 'routecustomer',
      dataObject: {
        "ID": true,
        "CustomerCode": true, //客户编码
        "CustomerID": true, //客户ID
        "RouteNo": false,
        "IsDeleted": true, //是否删除
        "LastModifiedBy": false, //最后修改人
        "LastModifiedTime": false, //最后修改时间
        "CreatedBy": false, //创建人
        "CreatedTime": false, //创建时间
      },
      ),
    //联系人
    DbTableModule(
      objName: 'contact',
      dataObject: {
        "ID": true, //主键
        "Name": true, //联系人名称
        "Office": false, //办公室
        "Salutation": false, //称呼
        "CustomerCode": true, //客户编码
        "CustomerID": true, //客户ID
        "Mobile": false, //手机
        "Email": false, //邮箱
        "IsPrimary": false, //是否主联系人
        "type": false, //类型
        "IsDeleted": true, //是否删除
        "LastModifiedBy": false, //最后修改人
        "LastModifiedTime": false, //最后修改时间
        "CreatedBy": false, //创建人
        "CreatedTime": false, //创建时间
      }
    ),
    //产品
    DbTableModule(
      objName: 'product',
      dataObject: {
        "ID": true, //主键
        "Code": false, //产品编码
        "ShortNameEn": false, //产品简称
        "ShortNameCn": false, //产品简称
        "FullNameCn": false, //产品全称
        "FullNameEn": false, //产品全称
        "LargePhoto": false, //大图
        "PackType": false, //包装类型
        "BarCode": false, //条码
        "WeightUnit": false, //重量单位
        "Meins": false, //计量单位
        "PackageID": false, //包装ID
        "CategoryID": false, //品类ID
        "ProductType": false, //产品类型
        "BrandID": false, //品牌ID
        "BrandCode":false, //品牌编码
        "LargePhoto_Url": false, //大图URL
        "ThumbnailPhoto_Url": false, //缩略图URL
        "DetailPagePhoto_Url": false, //详情页URL
        "IsDeleted": true, //是否删除
        "LastModifiedBy": false, //最后修改人
        "LastModifiedTime": false, //最后修改时间
        "CreatedBy": false, //创建人
        "CreatedTime": false, //创建时间
      },
    ),
    //品类
    DbTableModule(
      objName: 'category',
      dataObject: {
        "ID": true, //主键
        "Code": false, //品类编码
        "ParentID": false, //父ID
        "Name": true, //品类名称
        "NameEn": false, //品类名称
        "Level": false, //层级
        "Sequence": false, //排序
        "IsDeleted": true, //是否删除
        "LastModifiedBy": false, //最后修改人
        "LastModifiedTime": false, //最后修改时间
        "CreatedBy": false, //创建人
        "CreatedTime": false, //创建时间
      },
    ),
    //品牌
    DbTableModule(
      objName: 'brand',
      dataObject: {
        "ID": true, //主键
        "Code": false, //品牌编码
        "Name": true, //品牌名称
        "NameEn": false, //品牌名称英文
        "BrandLogoUrl": false, //品牌logoURL
        "CategoryID": false, //品类ID
        "CategoryCode": false, //品类编码
        "Sequence": false, //排序
        "IsDeleted": true, //是否删除
        "LastModifiedBy": false, //最后修改人
        "LastModifiedTime": false, //最后修改时间
        "CreatedBy": false, //创建人
        "CreatedTime": false, //创建时间
      }
    ),  
    //动态视图
    DbTableModule(
      objName: 'dynamicForm',
      dataObject: {
        "ID": true, //主键
        "DataTable": false, //数据表
        "DataType": true, //数据类型
        "DataTypeDsc": false, //数据类型描述
        "FieldName": true, //字段名
        "Label": false, //标签
        "LabelStyle": false, //标签样式
        "ValueStyle": false, //值样式
        "InputType": false, //输入类型
        "Format": false, //格式
        "MinMax": false, //最小最大
        "MinMaxLength":false , //最小最大长度
        "SuffixDefault": false, //后缀默认
        "IsEdit": false, //是否编辑
        "Sequence": false, //序列
        "Required": false, //是否必填
        "SourceField": false, //源字段
        "DependFieldName": false, //依赖字段
        "DependFieldValue": false, //依赖字段值
        "SourceTable": false, //源表
        "SourceColumn": false, //源列
        "SubDataTypeDsc": false, //子数据类型描述
        "SourceType": false, //源类型
        "SourceValue": false, //源值
        "IsCopy": false, //是否复制
        "IsDeleted": true, //是否删除
        "LastModifiedBy": false, //最后修改人
        "LastModifiedTime": false, //最后修改时间
        "CreatedBy": false, //创建人
        "CreatedTime": false, //创建时间
      },
    ),
  ///地里架构
  DbTableModule(
    objName: 'location',
    dataObject: {
      "ID": true,
      "Code": false,
      "Name": true,
      "OrgID": false,
      "Parent": false,
      "FullPath": false,
      "Level": false,
      "IsDeleted": true, //是否删除
      "LastModifiedBy": false, //最后修改人
      "LastModifiedTime": false, //最后修改时间
      "CreatedBy": false, //创建人
      "CreatedTime": false, //创建时间
    },
  ),
  //DependData
  DbTableModule(
    objName: 'DependData',
    dataObject: {
      "ID": true, //主键
      "Code": true, //编码
      "Name": true, //名称
      "NameEn": true, //名称英文
      "TableName": true, //表名
      "Type": true, //类型
      "Value": true, //值
      "DependCode": true, //依赖编码
      "DependValue": true, //依赖值
      "DependentFieldName": true, //依赖字段名
      "FieldName": true, //字段名
      "IsDeleted": true, //是否删除
      "LastModifiedBy": false, //最后修改人
      "LastModifiedTime": false, //最后修改时间
      "CreatedBy": false, //创建人
      "CreatedTime": false, //创建时间
    },
  ),
  // 手机端权限模块
  DbTableModule(
    objName: 'PermissionConfig',
    dataObject: {
      "ID": true, //主键
      "RoleID": true, //角色ID
      "Code": true, //编码
      "Name": true, //名称
      "Description": true, //描述
       "IsDeleted": true, //是否删除
      "LastModifiedBy": false, //最后修改人
      "LastModifiedTime": false, //最后修改时间
      "CreatedBy": false, //创建人
      "CreatedTime": false, //创建时间
    },
  ),
  //拜访记录
  DbTableModule(
    objName: 'visit',
    dataObject: {
      "ID": true, //主键
      "RouteDetailId": false, //路线详情ID
      "CustomerCode": false, //客户编码
      "CustomerID": false, //客户ID
      "CustomerLatitude": false, //客户纬度
      "CustomerLongitude": false, //客户经度
      "CustomerAddress": false, //客户地址
      "InLatitude": false, //入店纬度
      "InLongitude": false, //入店经度
      "OutLatitude": false, //出店纬度
      "OutLongitude": false, //出店经度
      "SignInAddress": false, //签到地址
      "SignOutAddress": false, //签出地址
      "GPSVarianceIn": false, //进店偏差
      "GPSVarianceOut": false, //离店偏差
      "VisitStatus": false, //拜访状态 1：正常完成拜访，2：中途取消拜访
      "VisitType": false, //签到类型  1：正常拜访，2：电话拜访，3：替线拜访，
      "SignInStatus": false, //签到状态  1：正常签到，2：偏差大，3：未获取到GPS
      "SignInTime": false, //进店时间
      "Remark": false, //备注
      "Duration": false, //拜访时长
      "GPSTime": false, //GPS获取到的时间 
      "IsOnRoute": false, //是否在路线 
      "RouteNo": false, //路线编号
      "UserID": false, //用户ID
      "UserCode": false, //用户编码
      "ReplacementUserID": false, //替换用户ID
      "ReplacementUserCode": false, //替换用户编码
       "IsDeleted": true, //是否删除
      "LastModifiedBy": false, //最后修改人
      "LastModifiedTime": false, //最后修改时间
      "CreatedBy": false, //创建人
      "CreatedTime": false, //创建时间
    },
  ),
   //订单头
  DbTableModule(
    objName: 'order',
    dataObject: {
      "ID": true,//主键
      "VisitID": true,//拜访ID
      "OrderNo": true,//订单号
      "CustomerID": true,//客户ID
      "CustomerCode": true,//客户编码
      "CustomerName": true,//客户名称
      "Status": true,//状态
      "OrderMsg": true,//订单备注
      "RouteNo": true,//路线编号
      "Remark": true,//备注
      "TotalQty": true,//总数量
      "TotalQtyCS": true,//总数量CS
      "TotalQtyEA": true,//总数量
      "TotalPrice": true,//总价
      "TaxAmount": true,//税额
      "ActualPayment": true,//实际支付
      "DiscountAmount": true,//折扣金额
      "ReceiveTime": true,//接收时间
      "OrderStatus": true,//订单状态
      "SubmitStatus": true,//提交状态
      "IsSuggestOrder": true,//是否建议订单
      "OrderType": true,//订单类型
      "PaymentStatus": true,//支付状态
      "SalesUserID": true,//销售员ID
      "SyncStatus": true,//同步状态
      "ActualPrice": true,//实际价格
      "PaymentMethod": true,//支付方式
      "OrderSource": true,//订单来源
      "InvoiceAmount": true,//发票金额
      "RebateAmount": true,//返利金额
      "UserID": true,//用户ID
      "UserName": true,//用户名称
      "SubmitReason": true,//提交原因
      "DeletedReason": true,//删除原因
      "ModifyReason": true,//修改原因
      "CancelReason": true,//取消原因
      "OrderDate": true,//订单日期
      "SubmitTime": true,//提交时间
      "IsDeleted": true, //是否删除
      "LastModifiedBy": false, //最后修改人
      "LastModifiedTime": false, //最后修改时间
      "CreatedBy": false, //创建人
      "CreatedTime": false, //创建时间
    },
  ),
  //订单明细
  DbTableModule(
    objName: 'orderLine',
    dataObject: {
      "ID": true,//主键
      "OrderNo": true,//订单号
      "Quantity": true,//数量
      "ProductCode": true,//产品编码
      "ProductName": true,//产品名称
      "ProductType": true,//产品类型
      "PromotionID": true,//促销ID
      "PromotionCode": true,//促销编码
      "PromotionDesc": true,//促销描述
      "ItemPrice": true,//项价格
      "IsMustSKU": true,//是否必须SKU
      "PackageCode": true,//包装编码
      "PackageName": true,//包装名称
      "Remark": true,//备注
      "FromSource": true,//来源 促销页面，产品列表，产品组
      "CancelReason": true,//取消原因
      "IsDeleted": true, //是否删除
      "LastModifiedBy": false, //最后修改人
      "LastModifiedTime": false, //最后修改时间
      "CreatedBy": false, //创建人
      "CreatedTime": false, //创建时间
    },
  ),
   //业务照片
  DbTableModule(
    objName: 'attachment',
    dataObject: {
      "ID": true, //主键
      "VisitID": true,
      "CustomerID": true,//客户ID
      "CustomerCode": true,//客户编码
      "ParentID": true, //父ID 比如门头照：就是门店Id, 订单照片：就是订单Id
      "FileName": true,//文件名
      "FileUrl": true,//文件URL
      "Longitude": true,//经度
      "Latitude": true,//纬度
      "remark": true,//备注
      "Source": true,//来源 相机，本次拜访
      "FileType": true,//文件类型 门头照，订单，问卷，Red,分销
      "TakePhotoTime": true,//拍照时间
      "status": true,//状态 0 待上传 1 上传中 2
      "ProcessID": true,//处理ID
      "IsDeleted": true, //是否删除
      "LastModifiedBy": true,//最后修改人
      "LastModifiedTime": true,//最后修改时间
      "CreatedBy": true,//创建人
      "CreatedTime": true,//创建时间
    },
  ),
  DbTableModule(
      objName: 'appuploadlog',
      dataObject: {
        "Id": true, //主键
        "VersionNumber": true, //版本号
        "Platform": true, //设备信息
        "Type": true, //日志类型
        "Message": true, //日志详细
        "DateTime": true, //日志时间
        "UserId": true, //用户ID
        "UserCode": true, //用户编码
        "IsDeleted": true, //是否删除
      },
  ),
  ];
}

//表结构
class DbTableModule{
  String objName; //对象名
  Map<String, bool>  dataObject; //表结构
  DbTableModule({required this.objName, required this.dataObject});
}

//映射表结构
class DbMappingTableModule{
  String localTableName; //本地表名
  String objName; //对象名
  bool isBatch; //是否是批量
  Map<String, dynamic>  columnMapping; //字段映射
  DbMappingTableModule({required this.localTableName, required this.objName, required this.columnMapping, this.isBatch = false});
}