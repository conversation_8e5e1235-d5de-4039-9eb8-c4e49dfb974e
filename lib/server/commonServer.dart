import '../sfaglobal.dart';
import 'sync/db_script/db_script.dart';
import '../common/db_helper.dart';
import '../common/util.dart';
import '../common/common.dart';
import 'dart:convert'; 


class CommonServer { 
  static Future<List<Map<String, dynamic>>> get2bUploadedCount() async {
    try {
      final recordActions = Common.recordActionValues.join(',');
      final tables = DbScript.dbScript
          .where((table) => table.objName.toLowerCase() != 'appuploadlog')
          .map((table) =>
              "SELECT count(1) as ct, '${table.objName.toLowerCase()}' as object FROM [${table.objName}] WHERE RecordAction IN ($recordActions)")
          .toList();

      if (tables.isEmpty) return [];

      final sql = 'SELECT * FROM (${tables.join(' UNION ALL ')}) WHERE ct > 0';
      final result = await dbHelper.executeRead(sql);
      return result;
    } catch (e) {
      print('get2bUploadedCount error: $e');
      rethrow;
    }
  }
  static Future<void> saveClientAppLog(msg, type) async {
    msg = msg is String ? msg : json.encode(msg);
    var guid = Util.getGuid();
    var deviceInfo = await Util.getDirverInfo().toString();
    var platform = await Util.getPlatform();
    var userInfo =await Util.getStorage(Common.localStorageKey.loginUserInfo) ?? {};

// {
//         "Id": 'string', //主键
//         "VersionNumber": 'string', //版本号
//         "Platform": 'string', //设备信息
//         "Type": 'string', //日志类型
//         "Message": 'string', //日志详细
//         "DateTime": 'string', //日志时间
//         "UserId": 'string', //用户ID
//         "UserCode": 'string', //用户编码
//         "IsDeleted": 'string', //是否删除
//       },
    var query =
        'insert into appuploadlog(Id,GUID,VersionNumber,Platform,Message,Type,UserId,UserCode,recordaction,DateTime) values(?,?,?,?,?,?,?,?,?,DateTime("now", "localtime"))';
    var params = [
      guid,
      guid,
      Common.sfaVersion,
      '$platform $deviceInfo',
      msg,
      type,
      SfaGlobal.loginUserInfo?.userId ?? '',
      SfaGlobal.loginUserInfo?.userCode ?? '',
      Common.recordAction.insertRec,
    ];
    try {
      return dbHelper.execute(query, params);
    } catch (e) {
      print('saveClientAppLog error: $e');
    }
  }

//本地只保留进60天日志
  static Future<void> syncClientLog2MobileLog() async {
    var query =
        'delete from appuploadlog where Date < datetime(\'now\', \'-60 day\')';
    return dbHelper.execute(query);
  }

  static Future<List<Map<String, dynamic>>> getBrand(where) async {
    var sql = 'SELECT * FROM brand where 1=1 ';
    if (where) {
      sql += where;
    }
    return dbHelper.executeRead(sql);
  }

  static Future<List<Map<String, dynamic>>> getChannel(where) async {
    var sql = 'SELECT * FROM channel where 1=1 ';
    if (where) {
      sql += where;
    }
    return dbHelper.executeRead(sql);
  }

  static Future<List<Map<String, dynamic>>> getContact(where) async {
    var sql = 'SELECT * FROM contact where 1=1 ';
    if (where) {
      sql += where;
    }
    return dbHelper.executeRead(sql);
  }

  static Future<List<Map<String, dynamic>>> getDictionary(where) async {
    var sql = 'SELECT *,description as text FROM dictionary where isdeleted=0 ';
    if (where) {
      sql += where;
    }
    sql += 'order by cast(sequence as int) Asc';
    return dbHelper.executeRead(sql);
  }

  static Future<List<Map<String, dynamic>>> getDictionaryByType(
      type, where) async {
    return getDictionary(' and type=\'$type\'  $where');
  }

  static Future<List<Map<String, dynamic>>> getSystemConfig(where) async {
    var sql = 'SELECT * FROM systemconfig where 1=1 ';
    if (where) {
      sql += where;
    }
    return dbHelper.executeRead(sql);
  }

  static Future<void> deletexgklPhoto(ParentId) async {
    var sql = 'delete from xgklPhoto where ParentId=\'$ParentId\'';
    return dbHelper.execute(sql);
  }

/**
 *
 * @param {String} xgklPhotoType  common.xgklPhotoType.xxxx
 * @param {String} ParentId
 * @param {String} StoreID
 * @param {String} VisitID
 * @param {String} FileName
 * @param {String} Source  common.xgklPhotoSource.xxxx
 */
  static Future<void> savexgklPhoto(
      xgklPhotoType, ParentId, StoreID, VisitID, FileName,
      {int? recordAction,
      String? Source,
      String originalid = '',
      String remark = '',
      String processId = '',
      String documentType = ''}) async {
    var id = Util.getRecordId();
    recordAction = recordAction ?? Common.recordAction.insertRec;
    Source = Source ?? Common.xgklPhotoSource.Camera;

    var sql = savePhoto(xgklPhotoType, ParentId, StoreID, VisitID, FileName,
        originalid: originalid,
        recordAction: recordAction,
        Source: Source,
        remark: remark,
        processId: processId,
        documentType: documentType);
    //获取照片坐标
    setXgklPhotoGPS(id);
    return;
  }

  static Future<void> savePhoto(
      xgklPhotoType, ParentId, StoreID, VisitID, FileName,
      {int? recordAction,
      String? Source,
      String originalid = '',
      String remark = '',
      String processId = '',
      String documentType = ''}) async {
    var id = Util.getRecordId();
    recordAction = recordAction ?? Common.recordAction.insertRec;
    Source = Source ?? Common.xgklPhotoSource.Camera.toString();
    var sql =
        "insert into [xgklPhoto](id,StoreID,VisitID,FileType,Url,FileName,status,IsDeleted,ParentId,createtime,TakePhotoTime,recordaction,guid,remark,Source,originalid,processid,documentType) values(?,?,?,?,?,?,?,?,?,DateTime('now','localtime'),DateTime('now','localtime'),?,?,?,?,?,?,?)";
    var params = [
      id,
      StoreID,
      VisitID,
      xgklPhotoType,
      FileName,
      FileName,
      '0',
      '0',
      ParentId,
      recordAction,
      id,
      remark,
      Source,
      originalid,
      processId,
      documentType,
    ];
    await dbHelper.execute(sql, params);
  }

  static Future<void> delPhoto(name) async {
    var sql = 'delete from xgklPhoto where filename=\'$name\'';
    return dbHelper.execute(sql);
  }

  static Future<void> setXgklPhotoGPS(photoId) async {
    // try {
    //   var postion = await Util.getCurrentLocation();
    //   var lat = postion!.latitude;
    //   var lng = postion!.longitude;
    //   var sql = 'update [xgklPhoto] set longitude=?,latitude=? where id=?';
    //   var params = [lng, lat, photoId];
    //   await dbHelper.execute(sql, params);
    // } catch (e) {
    //   print('setXgklPhotoGPS error: $e');
    //   throw e;
    // }
  }

/**
 * burialPointEvent(burialPoint.CMPlan,'p001','b01')
 * @param {*} module
 * @param {*} page
 * @param {*} event
 * @returns
 */
  static Future<void> burialPointEvent(module, page, event) async {
    var moduleName = module.module;
    var pageName = module[page]['page'];
    var eventName = module[page]['events'][event];
    var guid = Util.getRecordId();
    var deviceType = await Util.getPlatform();
    // var loginUserInfo = await Util.getStorage(Common.localStorageKey.loginUserInfo);
    // var position = loginUserInfo['position'];
    // var userId = loginUserInfo['userId'];
    var position =SfaGlobal.loginUserInfo?.position??"";
    var userId = SfaGlobal.loginUserInfo?.userId??"";

    var query =
        'insert into burialPointEvent(Id,appversion,source,devicetype,module,page,eventId,userid,position,recordaction,guid,isDeleted,datetime) values(?,?,?,?,?,?,?,?,?,?,?,0,DateTime("now", "localtime"))';
    var params = [
      guid,
      Common.sfaVersion,
      'APP',
      deviceType,
      moduleName,
      pageName,
      eventName,
      userId,
      position,
      Common.recordAction.insertRec,
      guid,
    ];
    return dbHelper.execute(query, params);
  }

  static Future<Map<dynamic, dynamic>> formatSFAPromiseByRoles() async {
    var sql = 'select roleid,code from moduleConfig';
    var result = await dbHelper.executeRead(sql);
    var ps = {};
    result.forEach((item) {
      if (ps[item['roleid']]) {
        ps[item['roleid']].push(item['code']);
      } else {
        ps[item['roleid']] = [item['code']];
      }
    });
    return ps;
  }
}
