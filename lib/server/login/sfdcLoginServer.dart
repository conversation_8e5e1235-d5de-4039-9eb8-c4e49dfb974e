import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:teemo_coca/server/apis.dart';

import '../../modules/SfdcRecordsModel.dart';
import '../../modules/sfdcAppUserInfo.dart';
import 'ILoginServer.dart';
import '../../modules/SFOauth2Model.dart';
import '../../common/util.dart';
import '../../common/common.dart';
import '../../common/http_helper.dart';
import '../../modules/baseResultModel.dart';
import '../../modules/loginUserInfoModel.dart';



class SfdcLoginServer implements ILoginServer {
  @override
  Future<BaseResultModel<LoginUserInfoModel>> login(String loginName, String password, {String platform = 'SFDC'}) async {
    var oauth = await sfdcOauth(loginName, password);
    var configData = await _getConfigData();
    SfdcAppUserInfo appUserInfo =  _getLoginAppConfig(configData, loginName);
    return BaseResultModel(code: 0, data: LoginUserInfoModel(token: oauth.accessToken! , instanceUrl: oauth.instanceUrl! , version: appUserInfo.sfaVersion, versionUrl: appUserInfo.downloadUrl  , timeZone: appUserInfo.timeZoneSidKey, currentDateTime: appUserInfo.serverTime, userId: appUserInfo.userId, userCode: appUserInfo.userCode, userName: appUserInfo.userName));
  }   

  ///在线登录
  Future<SFOauth2Model> sfdcOauth(String name, String password) async {
    var envObj = Common.SfdcConnects[1]; 
    var loginUrl = envObj["loginUrl"];
    var clientId = envObj["clientId"];
    var clientSecret = envObj["clientSecret"];
    print('loginUrl: $loginUrl');
    var param =
        "grant_type=password&username=$name&password=$password&client_id=$clientId&client_secret=$clientSecret&redirect_uri=";
    try{
    var result = await HttpHelper.post(
        loginUrl,
        data: param,
        options: Options(contentType: "application/x-www-form-urlencoded"));
    SFOauth2Model response = SFOauth2Model.fromJson(result);
    if (response.error == null) {
      await Util.setStorage(Common.localStorageKey.token, response.accessToken);
      await Util.setStorage(Common.localStorageKey.sfdcInstanceUrl, response.instanceUrl); 
      return response;
    } else {
      throw Exception(response.errorDescription);
    }
    }catch(ex){
      throw Exception(ex.toString());
    }
  }



  ///登录后获取配置数据
  Future<Map<String, dynamic>> _getConfigData() async {
    var token = await Util.getStorage(Common.localStorageKey.token);
    Map<String, dynamic> headers = {
      "Authorization": "Bearer $token"
    };
    String url = await getSfdcUrl(SfdcApis.configData);
    try {
      var response =
          await HttpHelper.get(url, Options(headers: headers));
      return response;
    } catch (ex) {
      throw Exception(ex.toString());
    }
  }


  ///解析登录后获取配置数据
  List<Map<String, dynamic>> _loginConfigDataParse(
      List<SfdcRecordsModel> confingJson) {
    List<Map<String, dynamic>> dataMap = [];
    for (var table in confingJson) {
      List<Map<String, String>> result = [];
      var currentFields = table.fields.toString().split(',');
      String tableName = table.name.toString();
      List<String> records = table.values;
      for (var j = 0; j < records.length; j++) {
        Map<String, String> value = {};
        for (var i = 0; i < currentFields.length; i++) {
          var val = records[j].toString().split(Common.appConf.splitChart)[i];
          value[currentFields[i]] = val.trim();
        }
        result.add(value);
      }
      Map<String, dynamic> table_ = {"Name": tableName, "Data": result};
      dataMap.add(table_);
    }

    return dataMap;
  }


  //格式化配置数据
  SfdcAppUserInfo _getLoginAppConfig(Map<String, dynamic> data,
      [String? username]) {
    print('_getLoginAppConfig data: $data');
    List<dynamic> records = data["records"];
    List<SfdcRecordsModel> recordModels = ([]);
    for (var item in records) {
      print('_getLoginAppConfig item: $item');
      recordModels.add(SfdcRecordsModel.fromJson(item));
    }
    List<Map<String, dynamic>> dataMap = _loginConfigDataParse(recordModels);
    print('dataMap: $dataMap'); 

    final users = _getLoginConfigData(dataMap, Common.loginConfigTable.user);
    print('users: $users');
    final userInfo = users.firstWhere(
        (t) => t["Username"].toLowerCase() == username!.toLowerCase());
    print('userInfo: $userInfo');
    final settings = _getLoginConfigData( dataMap, Common.loginConfigTable.eBestSFASetting)[0];
    print('settings: $settings');
    final configuration = _getLoginConfigData(dataMap, Common.loginConfigTable.configuration); 
    print('configuration: $configuration');
    // Ios download url
    Map<String, dynamic>? downloadUrlConfig = configuration.firstWhereOrNull((t) =>
        t["eBest6s__CodeCategory__c"] == 'DownloadUrl' &&
        t["eBest6s__IsActive__c"] == 'true');
    print('downloadUrlConfig: $downloadUrlConfig');
    // app user info
    SfdcAppUserInfo appUserInfo = SfdcAppUserInfo(
        timeZoneSidKey: userInfo["TimeZoneSidKey"],
        localTimeGMT: userInfo["ebMobile__LocalTimeGMT__c"],
        userCode: userInfo["ebMobile__UserCode__c"],
        userName: userInfo["Username"],
        firstName: userInfo["LastName"],
        name: userInfo["Name"],
        sfaVersion: settings["ebMobile__VersionNumber__c"],
        roleId: userInfo["UserRoleId"],
        role: userInfo["ebMobile__IsSupervisor__c"] == "true"
            ? "SuperVisor"
            : "SalesRep",
        userId: userInfo["Id"],
        sfdcApiVersion: settings["ebMobile__SFDCApiVersion__c"],
        isSchemaCreated: false,
        isInitialDataDownloaded: false,
        deleteExistingRecords: false,
        downloadUrl:   downloadUrlConfig == null ? "" : downloadUrlConfig["ebMobile__CodeValue__c"] ?? "",
        lastOnlineLogin: DateTime.now().toString(),
        countryCode: userInfo["ebMobile__CountryCode__c"],
        isSuperVisor: userInfo["ebMobile__IsSupervisor__c"] == "true",
        serverTime: userInfo["ebMobile__ServerTime__c"] == "Invalid Date"
            ? DateTime.now().toString()
            : userInfo["ebMobile__ServerTime__c"],
        isBatchDownload: settings["ebMobile__IsBatchDownload__c"],
        maxDownloadGroupNumber: settings["ebMobile__MaxGroupNumber__c"],
        requireInitSync: userInfo["ebMobile__RequireInitSync__c"],
        photoExpiredDay:  50);

    Util.setStorage(Common.localStorageKey.sfdcDownloadGroupCount,appUserInfo.maxDownloadGroupNumber);
    return appUserInfo;
  }
  //根据对象名获取配置数据
  List<Map<String, dynamic>> _getLoginConfigData(
      List<Map<String, dynamic>> dataMap, String tableName) {
    List<Map<String, String>> datas = ([]);
    for (var item in dataMap) {
      if (item["Name"] == tableName) {
        datas = item["Data"];
        return datas.map((m) => Map.of(m)).toList();
      }
    }
    return datas;
  }


  getSfdcUrl(url) async {
    var instanceUrl = await Util.getStorage(Common.localStorageKey.sfdcInstanceUrl);
    return instanceUrl + url;
  }
}