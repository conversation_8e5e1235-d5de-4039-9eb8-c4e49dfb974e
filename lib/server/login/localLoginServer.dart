
import '../../modules/baseResultModel.dart';
import '../../modules/loginUserInfoModel.dart';
import '../../common/util.dart';
import 'ILoginServer.dart';

// 本地模拟登录
class LocalLoginServer implements ILoginServer {
  // 租户ID
  static const String tenantId = 'swire_tw_tb_q';

  /// 登录方法
  @override
  Future<BaseResultModel<LoginUserInfoModel>> login(
      String loginName, String password,
      {String platform = 'SFA'}) async {
    try {
      if(loginName == 'ebestsfa' && password == 'ebestsfa'){
        final response = await Future.delayed(const Duration(seconds: 3), () {
        return {
            "limit": 0,
            "offset": 0,
            "rows": 0,
            "code": 0,
            "message": "",
            "data": {
              "currentUserInfo": {
                "companyCode": "ebestsfa",
                "userName": "ebestsfa",
                "userCode": "ebestsfa",
                "userId": "ebestsfa",
                "loginName": "ebestsfa",
                "position": "SDR",
                "routeNo": "EB",
                "locationCode": null,
                "picAvatar": null,
                "mobile": "",
                "tenantId": null,
                "version": null,
                "localDateTime": null,
                "userRoleList": [
                  {"roleID": "1001", "roleCode": "9998", "roleName": "SDR"}
                ],
                "positionName": "SDR"
              },
              "token":  "eyJhbGciOiJSUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Cvk-H1nDRt41eJae2Ml1NeF4NgKG5RXfJ0xYNrImczddH_e8MDQU4dQ1IonQBLTADbAE_u0_wuEK9eiTgs0bmdTBIRyH6s7e7Sctoh-4IeZszNZswRlbfCEsMvM5uRJv0r4-ctPRNtAS9rF8DxWFSnHp0mwLK2i_JbqINGsdMoZqKOKNFUCDOsCUMw07yDFPm4fWY_lbHeDthNHyySsGij-G11cr9Ypn-HzQ2xJb-dJIRayyJzVo4bHLKxHEaBI9Z6Zf4eP4xoHT803TgQYbAsBanBaseyj4vzoXtK8BD0Vtjv2fh79o6PXSSyOs6US4g7w5G-ZuzynCZXIYNqk4VQ",
              "version": "1.5.23",
              "versionUrl":"https://appstore.ebestmobile.net/iphone/swireasia/index.htm",
              "timeZone": "+8",
              "appPage": [
                {"Code": "Pg014-B002", "Name": "Route Visit【Unplan Visit】"}
              ],
              "currentDateTime": "2025-07-16T05:17:31.127",
            }
        };
      });
      final result = BaseResultModel<LoginUserInfoModel>.fromJson(response, (json) => LoginUserInfoModel.fromJson(json));
      result.data?.version = result.data?.version ?? "123123123";
      result.data?.userId = result.data!.currentUserInfo?.userId ?? "";
      result.data?.userCode = result.data?.currentUserInfo?.userCode ?? "";
      result.data?.userName = result.data?.currentUserInfo?.userName ?? "";
      result.data?.position = result.data?.currentUserInfo?.position ?? "";
      result.data?.routeNo = result.data?.currentUserInfo?.routeNo ?? "";

      return result;
      }else{
        return BaseResultModel<LoginUserInfoModel>(code: 1, message: '登录失败 用户名或密码错误', data: null);
      }
    } catch (e) {
      print(e);
      throw Exception('登录失败: $e');
    }
  }
}
