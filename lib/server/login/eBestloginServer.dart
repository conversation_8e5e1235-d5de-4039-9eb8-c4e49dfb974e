import 'package:dio/dio.dart';
import 'package:crypto/crypto.dart';
import '../../modules/baseResultModel.dart';
import '../../modules/loginUserInfoModel.dart';
import 'dart:convert';
import '../../common/http_helper.dart';
import '../../common/util.dart';
import '../apis.dart';
import 'ILoginServer.dart';

class EBestLoginServer implements ILoginServer { 
  
  // 租户ID
  static const String tenantId = 'swire_tw_tb_q';
  /// 登录方法
  @override
   Future<BaseResultModel<LoginUserInfoModel>> login(
     String loginName,
     String password,
   {
     String platform = 'SFA'
   }
  ) async {
    try {
      // 对密码进行MD5加密
      final md5Password = Util.Md5(password);
      
      // 构建请求数据
      final requestData = {
        'loginName': loginName,
        'password': md5Password.toUpperCase(),
        'platform': platform,
      };
      // 构建请求头
      final options = Options(
        headers: {
          'x-tenant-id': tenantId,
          'Content-Type': 'application/json',
        },
      );
      // 发送登录请求
      final response = await HttpHelper.post(EBestApis.login, data: requestData, options: options);
      print('ebest login response: $response');
      final result = BaseResultModel<LoginUserInfoModel>.fromJson(response, (json) => LoginUserInfoModel.fromJson(json));
      result.data?.userId = result.data!.currentUserInfo?.userId??"";
      result.data?.userCode = result.data?.currentUserInfo?.userCode??"";
      result.data?.userName = result.data?.currentUserInfo?.userName??"";
      result.data?.position = result.data?.currentUserInfo?.position??"";
      result.data?.routeNo = result.data?.currentUserInfo?.routeNo??"";
      return result;
    } catch (e) {
      print(e);
      throw Exception('登录失败: $e');
    }
  } 
}