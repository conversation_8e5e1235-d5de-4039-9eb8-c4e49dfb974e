import 'package:get/get.dart';
import '../../pages/main/controller.dart';
import '../../pages/login/controller.dart';
import '../../pages/setting/controller.dart';
import '../../pages/sync/controller.dart';
import '../../pages/customers/controller.dart';
import '../../pages/profile/controller.dart';
import '../../pages/dashboard/controller.dart';
import '../../pages/customer_detail/controller.dart';

class LoginPageBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<LoginController>(() => LoginController());
  }
}

class SettingPageBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<SettingController>(() => SettingController());
  }
}

class MainPageBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<MainController>(() => MainController());
  }
}

class SyncPageBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<SyncController>(() => SyncController());
  }
}

class DashboardPageBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<DashboardController>(() => DashboardController());
  }
}

class CustomersPageBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<CustomersController>(() => CustomersController());
  }
}

class CustomerDetailPageBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<CustomerDetailController>(() => CustomerDetailController());
  }
}

/// 个人中心页面绑定
class ProfileBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<ProfileController>(() => ProfileController());
  }
} 

class ProfileSettingBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<ProfileController>(() => ProfileController());
  }
} 


