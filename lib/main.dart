import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:teemo_coca/them/red_theme.dart';
import '../../router/routers_names.dart';
import 'common/fin_clp_manager.dart';
// import 'componse/FlutterFinclipWidget.dart';
import '../../common/file_helper.dart';
import 'GlobalController.dart';
import 'common/platform_channel_util.dart';
import 'i10n/i10n.dart';
import 'sfaglobal.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'common/util.dart';
import 'common/common.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'router/routers_phone.dart';
import 'package:home_widget/home_widget.dart';
import 'pages/app_home_widget/home_widget1/sfa_widget_service.dart';

late StreamSubscription<bool> keyboardSubscription;
void main() async {
  // flutter规则 flutter中先执行WidgetsFlutterBinding.ensureInitialized()
  WidgetsFlutterBinding.ensureInitialized();

  //初始化widget
  await SfaWidgetService.initializeWidget();

  await SfaGlobal.initDataSource();
  var keyboardVisibilityController = KeyboardVisibilityController();

  keyboardSubscription =
      keyboardVisibilityController.onChange.listen((bool visible) {
    if (!visible) {
      print("主页面焦点去除");
      Get.focusScope!.unfocus();
      FocusScope.of(Get.context!).unfocus();
    }
  });
  if (!kIsWeb) {
    WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
    FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: [SystemUiOverlay.top]);
  }
  // 监听原生 intent
  HomeWidget.registerInteractivityCallback(_backgroundCallback);
  // 全局状态信息等
  GlobalController globalController = GlobalController();
  Get.put(globalController);
  runApp(
    ScreenUtilInit(
      designSize: const Size(375, 812), //建议用主流手机设计稿尺寸（如 375x812、360x690）。
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MyApp();
      },
    ),
  );
}

Future<void> _backgroundCallback(Uri? uri) async {
  print('_backgroundCallback uri $uri');
  if (uri != null && SfaGlobal.loginUserInfo != null) {
    final action = uri.queryParameters['action'];
    print('widget action: $action');
    if (action != null && action.isNotEmpty) {
      if (action == 'dashboard') {
        Get.offAllNamed(RoutesNames.dashboard);
      } else if (action == 'route_plan') {
        Get.offAllNamed(RoutesNames.routePlanAdd);
      } else if (action == 'orders') {
        Get.offAllNamed(RoutesNames.customers);
      }
    }
  }
}

class MyApp extends GetView<MyAppController> {
  MyApp({super.key});
  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(statusBarIconBrightness: Brightness.light));
    Get.put(MyAppController());
    return GetMaterialApp(
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      translations: i10nTranslations(),
      fallbackLocale: const Locale('zh', 'CN'),
      supportedLocales: const [
        Locale('zh', 'CN'),
        Locale('en', 'US'),
      ],
      smartManagement: SmartManagement.full,
      debugShowCheckedModeBanner: false,
      theme: RedTheme().themeData,
      initialRoute: RoutesNames.login,
      defaultTransition: Transition.rightToLeftWithFade,
      getPages: phonePage,
    );
  }
}

class MyAppController extends GetxController with WidgetsBindingObserver {
  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<ConnectivityResult> _connectivitySubscription;
  late final AppLifecycleListener _listener;
  late AppLifecycleState? _state;
  RxBool isShowOverlay = false.obs;

  void initSetting() async {
    String? language =
        await Util.getStorage(Common.localStorageKey.selectedLanguage);
    String? region =
        await Util.getStorage(Common.localStorageKey.selectedMarket);
    var locale = Locale(language ?? "zh", region ?? "CN");
    Get.updateLocale(locale);
    Intl.defaultLocale = locale.toString();
  }

  Future<void> initConnectivity() async {
    late ConnectivityResult result;
    try {
      result = await _connectivity.checkConnectivity();
    } on PlatformException catch (e) {
      developer.log('Couldn\'t check connectivity status', error: e);
      return;
    }
    return _updateConnectionStatus(result);
  }

  Future<void> _updateConnectionStatus(ConnectivityResult result) async {
    SfaGlobal.setConnectivity(result);
  }

  connectivityChangedListen() {
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }

  ///全局生命周期监听
  lifecycleListener() {
    _state = SchedulerBinding.instance.lifecycleState;
    _listener = AppLifecycleListener(
      onShow: () {
        print('onShow');
      },
      onResume: () {
        print('onResume');
      },
      onInactive: () {
        print('onInactive');
      },
      onPause: () {
        print('onPause');
      },
      onDetach: () {
        print('onDetach');
      },
      onRestart: () {
        print('onRestart');
      },
      onStateChange: _handleStateChange,
    );
  }

  ///AppLifecycle
  void _handleStateChange(AppLifecycleState state) {
    if (state != AppLifecycleState.resumed) {
      print("AppScreenPrivacyService().disableScreenPrivacy()");
      AppScreenPrivacyService().enableScreenPrivacy();
    } else {
      print("AppScreenPrivacyService().enableScreenPrivacy()");
      AppScreenPrivacyService().enableScreenPrivacy();
    }
  }

  void initialization() async {
    FlutterNativeSplash.remove();
    print('initialization---------1');
    initSetting();
    initConnectivity();
    lifecycleListener();
    //初始化本地需要的文件夹
    FileHelper.init();
    // FlutterFinclipWidget.initFinClip(appKey: FinClpManager.sdkKey, appSecret: FinClpManager.sdkSecret);
    //初始化小程序
    await FinClpManager.init();
    connectivityChangedListen();
  }

  @override
  void onInit() async {
    initialization();
    super.onInit();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.resumed) {
      // 应用程序从后台返回
    }
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    _connectivitySubscription.cancel();
    keyboardSubscription.cancel();
    _listener.dispose();
    _state = null;
    WidgetsBinding.instance.removeObserver(this);
  }
}
