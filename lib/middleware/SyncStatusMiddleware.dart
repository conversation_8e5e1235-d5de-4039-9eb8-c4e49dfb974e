import 'package:get/get.dart';
import 'package:flutter/cupertino.dart';
import '../common/sfa_exception.dart';
import '../sfaglobal.dart';

//同步状态验证中间件
//数据同步中不允许拜访门店
class SyncStatusMiddleware extends GetMiddleware{
    @override
  RouteSettings? redirect(String? route) {
    if(SfaGlobal.currentSyncStatus == SyncStatus.RUNNING){ 
      throw SfaException("Common.SyncProcessing".tr,SfaErrorType.syncProcessing);
    }else{
    return super.redirect(route);
    }
  }
}