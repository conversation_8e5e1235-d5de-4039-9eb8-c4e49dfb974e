import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:get/get.dart';
import 'package:teemo_coca/them/base_them.dart';
import 'package:teemo_coca/them/ebest_theme.dart'; 
import '../../router/routers_names.dart';
import 'modules/loginUserInfoModel.dart';
import 'modules/loginUserInfoModel.dart';
import 'common/common.dart';
import 'common/customIcons.dart';
import 'common/util.dart'; 


class SfaGlobal {
  
 
  ///根据 配置是否平板
  static bool isTable = false;

  static LoginUserInfoModel? _loginUserInfo;

  static set loginUserInfo(LoginUserInfoModel? value) {
    _loginUserInfo = value;
  }

  static LoginUserInfoModel? get loginUserInfo => _loginUserInfo;

  static String? _token;

  static set token(String? value) {
    _token = value;
  }

  static String? get token => _token;
  
 
  ///数据同步状态
  static SyncStatus _currentSyncStatus = SyncStatus.END;

  ///设置数据同步状态
  static set currentSyncStatus(SyncStatus status) {
    _currentSyncStatus = status;
  }

  ///获取数据同步状态
  static SyncStatus get currentSyncStatus => _currentSyncStatus;

  ///文件同步状态
  static FileSyncStatus _currentFileSyncStatus = FileSyncStatus.END;

  ///获取文件同步状态
  static set CurrentFileSyncStatus(FileSyncStatus status) {
    _currentFileSyncStatus = status;
  }

  ///获取文件同步状态
  static FileSyncStatus get CurrentFileSyncStatus => _currentFileSyncStatus;

  ///网络状态
  static ConnectivityResult _connectivity = ConnectivityResult.none;
  static setConnectivity(ConnectivityResult result) {
    _connectivity = result;
  }

  ///获取网络状态
  static ConnectivityResult get getConnectivity => _connectivity;

  //权限验证
  static List permissions = [];
  static List storepermissions = [];
  static bool funPromise(key) {
    if (permissions.isNotEmpty) {
      var have = permissions.firstWhere((element) => element["key"] == key);
      if (have.isNotEmpty) {
        return true;
      }
    }
    return false;
  }
 
  
  ///门店拜访所需要的数据
  static CallData? CurrentCallData;
  static set callData(CallData _callData) {
    CurrentCallData = _callData;
  }

  static CallData get callData {
    return CurrentCallData!;
  }

  ///退出拜访清理拜访缓存数据
  static void clearCallData() {
    CurrentCallData = null;
  }
 
  /// 全局数据源（SFDC/eBest）
  static String dataSource = "SFDC";

  /// 初始化数据源（从本地存储读取）
  static Future<void> initDataSource() async {
    String? saved = await Util.getStorage(Common.localStorageKey.selectedDataSource);
    if (saved != null && saved.isNotEmpty) {
      dataSource = saved;
    } else {
      dataSource = "SFDC";
    }
  }

  /// 设置数据源并持久化
  static Future<void> setDataSource(String value) async {
    dataSource = value;
    await Util.setStorage(Common.localStorageKey.selectedDataSource, value);
  }
} 

//拜访中数
class CallData { 
}


//数据同步状态
enum SyncStatus {
  //同步中
  RUNNING,
  //同步结束
  END
}

//文件同步状态
enum FileSyncStatus {
  //同步中
  RUNNING,
  //同步结束
  END
} 