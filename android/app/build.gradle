plugins {
    id "com.android.application"
    id "kotlin-android"
    id "kotlin-parcelize" 
//    id "kotlin-android-extensions"
    id "kotlin-kapt"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.ebestmobile.hubsfa1"
    // compileSdk = flutter.compileSdkVersion
    compileSdk = 35
    ndkVersion = "26.1.10909125"//flutter.ndkVersion
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    } 

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.ebestmobile.hubsfa1"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        // minSdk = flutter.minSdkVersion
        // targetSdk = flutter.targetSdkVersion
        // versionCode = flutter.versionCode
        // versionName = flutter.versionName
           minSdkVersion=21
           targetSdkVersion=35
           versionCode=1
           versionName="1.0.0"
        // minSdk = 21
        // targetSdk = 35
        ndk {
            abiFilters "armeabi-v7a", "arm64-v8a", "x86_64"
        }

        manifestPlaceholders = [
                JPUSH_PKGNAME : applicationId,
                JPUSH_APPKEY : "bfbf7371ee66eee8e5b04977", //JPush 上注册的包名对应的 Appkey.
                JPUSH_CHANNEL : "developer-default", //暂时填写默认值即可.

                //meizu_config_start
                MEIZU_APPKEY  : "MZ-魅族的APPKEY",
                MEIZU_APPID   : "MZ-魅族的APPID",
                //meizu_config_end
                //xiaomi_config_start
                XIAOMI_APPID  : "MI-小米的APPID",
                XIAOMI_APPKEY : "MI-小米的APPKEY",
                //xiaomi_config_end
                //oppo_config_start
                OPPO_APPKEY   : "OP-oppo的APPKEY",
                OPPO_APPID    : "OP-oppo的APPID",
                OPPO_APPSECRET: "OP-oppo的APPSECRET",
                //oppo_config_end
                //vivo_config_start
                VIVO_APPKEY   : "vivo的APPKEY",
                VIVO_APPID    : "vivo的APPID",
                //vivo_config_end
                //honor_config_start
                HONOR_APPID    : "honor的APPID",
                //honor_config_end
                //nio_config_start
                NIO_APPID    : "蔚来的APPID",
                //nio_config_end
        ]
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
        }
    }
     buildFeatures {
        viewBinding true
    }

     packagingOptions {
        // libsdkcore.so是被加固过的，不能被压缩，否则加载动态库时会报错
        doNotStrip "*/x86/libsdkcore.so"
        doNotStrip "*/x86_64/libsdkcore.so"
        doNotStrip "*/armeabi/libsdkcore.so"
        doNotStrip "*/armeabi-v7a/libsdkcore.so"
        doNotStrip "*/arm64-v8a/libsdkcore.so"
    }
}

dependencies {
    //  implementation fileTree(include: ['*.jar'], dir: 'libs')
     implementation fileTree(dir: 'libs', include: ['*.jar',"*.aar"])
     // Finclip SDK核心库
     implementation 'com.finogeeks.lib:finapplet:2.48.9'
     implementation 'androidx.appcompat:appcompat:1.6.1'
     implementation 'androidx.fragment:fragment:1.6.2'
     implementation 'androidx.window:window:1.1.0'
     implementation 'androidx.window:window-java:1.1.0'
     implementation project(':jiguang')
}


flutter {
    source = "../.."
}
