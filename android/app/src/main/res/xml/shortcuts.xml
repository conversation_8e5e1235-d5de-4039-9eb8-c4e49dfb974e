<?xml version="1.0" encoding="utf-8"?>
<shortcuts xmlns:android="http://schemas.android.com/apk/res/android">
    <shortcut
        android:shortcutId="dashboard"
        android:enabled="true"
        android:icon="@mipmap/icon_sfa"
        android:shortcutShortLabel="@string/dashboard_short_label"
        android:shortcutLongLabel="@string/dashboard_long_label">
        <intent
            android:action="android.intent.action.VIEW"
            android:targetPackage="com.example.teemo_coca"
            android:targetClass="com.example.teemo_coca.MainActivity" />
    </shortcut>
    
    <shortcut
        android:shortcutId="customers"
        android:enabled="true"
        android:icon="@mipmap/icon_sfa"
        android:shortcutShortLabel="@string/customers_short_label"
        android:shortcutLongLabel="@string/customers_long_label">
        <intent
            android:action="android.intent.action.VIEW"
            android:targetPackage="com.example.teemo_coca"
            android:targetClass="com.example.teemo_coca.MainActivity"
            android:data="customers" />
    </shortcut>
</shortcuts>