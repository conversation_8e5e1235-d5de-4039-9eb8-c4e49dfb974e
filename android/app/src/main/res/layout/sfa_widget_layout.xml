<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/widget_background">

    <!-- 头部信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_business"
            android:tint="@android:color/white" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="Hub SFA"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_update_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:00"
            android:textColor="#B3FFFFFF"
            android:textSize="12sp" />

        <!-- <ImageView
            android:id="@+id/btn_refresh"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_refresh"
            android:layout_marginStart="8dp"
            android:contentDescription="刷新"
            android:tint="#B3FFFFFF" /> -->

    </LinearLayout>

    <!-- 用户信息 -->
    <TextView
        android:id="@+id/tv_user_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="用户，今日工作概览"
        android:textColor="@android:color/white"
        android:textSize="14sp" />

    <!-- 核心数据区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal">

        <!-- 拜访完成情况 -->
        <LinearLayout
            android:id="@+id/visit_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:padding="12dp"
            android:background="@drawable/widget_card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_location"
                    android:tint="@android:color/white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="拜访完成"
                    android:textColor="#B3FFFFFF"
                    android:textSize="12sp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_visit_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="0/0"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_visit_rate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="0%"
                android:textColor="#B3FFFFFF"
                android:textSize="12sp" />

        </LinearLayout>
 
        <TextView
            android:layout_width="12dp"
            android:layout_height="0dp"
            android:background="@android:color/transparent" />

        <!-- 销售目标 -->
        <LinearLayout
            android:id="@+id/sales_card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:padding="12dp"
            android:background="@drawable/widget_card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_trending_up"
                    android:tint="@android:color/white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="销售目标"
                    android:textColor="#B3FFFFFF"
                    android:textSize="12sp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_sales_value"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="0万"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_sales_rate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="0%"
                android:textColor="#B3FFFFFF"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 底部快捷信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:orientation="horizontal"
        android:gravity="center">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_orders_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="今日订单"
                android:textColor="#B3FFFFFF"
                android:textSize="10sp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_customers_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="新增客户"
                android:textColor="#B3FFFFFF"
                android:textSize="10sp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_tasks_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="待办任务"
                android:textColor="#B3FFFFFF"
                android:textSize="10sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 快捷导航按钮区域 -->
    <LinearLayout
        android:id="@+id/quick_nav_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="16dp">

        <LinearLayout
            android:id="@+id/btn_dashboard"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">
            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_trending_up"
                android:tint="#B3FFFFFF"
                android:contentDescription="dashboard" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="数据看板"
                android:textColor="#B3FFFFFF"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/btn_route_plan"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">
            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_location"
                android:tint="#B3FFFFFF"
                android:contentDescription="路线计划" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="路线计划"
                android:textColor="#B3FFFFFF"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/btn_orders"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">
            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_business"
                android:tint="#B3FFFFFF"
                android:contentDescription="订单列表" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="订单列表"
                android:textColor="#B3FFFFFF"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>

