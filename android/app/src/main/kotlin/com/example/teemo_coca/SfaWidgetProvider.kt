package com.ebestmobile.hubsfa1

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.widget.RemoteViews
import es.antonborri.home_widget.HomeWidgetPlugin
import es.antonborri.home_widget.HomeWidgetLaunchIntent
import android.net.Uri
import android.content.ComponentName

class SfaWidgetProvider : AppWidgetProvider() {
    
    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId)
        }
    }

    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)
        if (intent.action == ACTION_REFRESH) {
            // 直接刷新 widget，不再启动 MainActivity
            val appWidgetManager = AppWidgetManager.getInstance(context)
            val thisWidget = ComponentName(context, SfaWidgetProvider::class.java)
            val appWidgetIds = appWidgetManager.getAppWidgetIds(thisWidget)
            for (appWidgetId in appWidgetIds) {
                updateAppWidget(context, appWidgetManager, appWidgetId)
            }
        }
    }

    companion object {
        const val ACTION_REFRESH = "com.ebestmobile.hubsfa1.ACTION_REFRESH_WIDGET"
    }

    private fun updateAppWidget(context: Context, appWidgetManager: AppWidgetManager, appWidgetId: Int) {
        val widgetData = HomeWidgetPlugin.getData(context)
        val views = RemoteViews(context.packageName, R.layout.sfa_widget_layout)

        // 设置用户信息
        val userName = widgetData.getString("userName", "用户")
        val updateTime = widgetData.getString("updateTime", "00:00")
        views.setTextViewText(R.id.tv_user_name, "$userName，今日工作概览")
        views.setTextViewText(R.id.tv_update_time, updateTime)

        // 设置拜访数据
        val visitPlanned = widgetData.getInt("todayVisitPlanned", 0)
        val visitCompleted = widgetData.getInt("todayVisitCompleted", 0)
        val visitRate = widgetData.getInt("visitCompletionRate", 0)
        views.setTextViewText(R.id.tv_visit_value, "$visitCompleted/$visitPlanned")
        views.setTextViewText(R.id.tv_visit_rate, "$visitRate%")

        // 设置销售目标数据
        val salesCompleted = widgetData.getString("salesCompleted", "0万")
        val salesRate = widgetData.getInt("salesCompletionRate", 0)
        views.setTextViewText(R.id.tv_sales_value, salesCompleted)
        views.setTextViewText(R.id.tv_sales_rate, "$salesRate%")

        // 设置其他业务数据
        val todayOrders = widgetData.getInt("todayOrders", 0)
        val newCustomers = widgetData.getInt("weeklyNewCustomers", 0)
        val pendingTasks = widgetData.getInt("pendingTasks", 0)
        
        views.setTextViewText(R.id.tv_orders_value, todayOrders.toString())
        views.setTextViewText(R.id.tv_customers_value, newCustomers.toString())
        views.setTextViewText(R.id.tv_tasks_value, pendingTasks.toString())

        // 设置点击事件
        setClickIntents(context, views, appWidgetId)

        appWidgetManager.updateAppWidget(appWidgetId, views)
    }

    private fun setClickIntents(context: Context, views: RemoteViews, appWidgetId: Int) {
        // 整体点击 - 打开应用
        val intent = Intent(context, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent, 
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.widget_container, pendingIntent)

        // 拜访卡片点击
        val visitIntent = Intent(context, MainActivity::class.java)
        visitIntent.putExtra("action", "visit_plan")
        val visitPendingIntent = PendingIntent.getActivity(
            context, 1, visitIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.visit_card, visitPendingIntent)

        // 销售目标点击
        val salesIntent = Intent(context, MainActivity::class.java)
        salesIntent.putExtra("action", "dashboard")
        val salesPendingIntent = PendingIntent.getActivity(
            context, 2, salesIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        views.setOnClickPendingIntent(R.id.sales_card, salesPendingIntent)

        // 刷新按钮点击（使用 HomeWidgetLaunchIntent）
        //val refreshIntent = HomeWidgetLaunchIntent.getActivity(
        //    context,
        //    MainActivity::class.java,
        //    Uri.parse("hubsfa://refresh_widget?action=refresh_widget")
        //)
        //views.setOnClickPendingIntent(R.id.btn_refresh, refreshIntent)

        // 快捷导航按钮统一设置
        setNavClickIntent(context, views, R.id.btn_dashboard, "dashboard", 100)
        setNavClickIntent(context, views, R.id.btn_route_plan, "route_plan", 101)
        setNavClickIntent(context, views, R.id.btn_orders, "orders", 102)
    }

    private fun setNavClickIntent(context: Context, views: RemoteViews, viewId: Int, action: String, requestCode: Int) {
        val uri = Uri.parse("hubsfa://widget_nav?action=$action")
        val pendingIntent = HomeWidgetLaunchIntent.getActivity(
            context,
            MainActivity::class.java,
            uri
        )
        views.setOnClickPendingIntent(viewId, pendingIntent)
    }
}