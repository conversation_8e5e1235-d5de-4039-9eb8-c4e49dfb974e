<manifest xmlns:android="http://schemas.android.com/apk/res/android"  
         xmlns:tools="http://schemas.android.com/tools">
    <!-- android:name="${applicationName}" -->
    <application
        android:label="Hub SFA"
        android:icon="@mipmap/icon_sfa">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
               <!-- 桌面启动图标 -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
            
             <!-- 支持自定义 URI scheme 唤起 -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="hubsfa" />
            </intent-filter>
        </activity>


        <!-- 添加服务卡片支持 -->
        <meta-data
            android:name="com.huawei.android.launcher.permission.READ_SETTINGS"
            android:value="true" />
            
        <!-- 华为服务卡片 -->
        <meta-data
            android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS"
            android:value="true" />
            
        <!-- 小米服务卡片 -->
        <meta-data
            android:name="com.miui.home.launcher.permission.READ_SETTINGS"
            android:value="true" />
            
        <!-- OPPO/VIVO 服务卡片 -->
        <meta-data
            android:name="com.oppo.launcher.permission.READ_SETTINGS"
            android:value="true" />

             <!-- 通用服务卡片支持 -->
        <meta-data
            android:name="android.app.shortcuts"
            android:resource="@xml/shortcuts" />
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
             <!-- Widget Provider -->
        <receiver android:name=".SfaWidgetProvider"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data android:name="android.appwidget.provider"
                android:resource="@xml/sfa_widget_info" />
        </receiver>
    </application>
    <!-- 生物识别权限 -->
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
    </queries>
</manifest>
