allprojects {
    repositories {
        google()
        mavenCentral() 
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}


buildscript {
    repositories {
        google()
        jcenter()
        // maven{ url'http://maven.aliyun.com/nexus/content/repositories/jcenter'}
        // maven { url 'http://maven.jpushoa.com/nexus/conte/groups/public' }
        // maven { url 'http://developer.huawei.com/repo/' }
    }
    dependencies {
        // classpath "com.android.tools.build:gradle:3.4.2"
        // classpath 'com.android.tools.build:gradle:8.6.0'
        classpath 'com.android.tools.build:gradle:8.0.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.6.20"
    }
}
 

allprojects {
    repositories {
        google()
        jcenter()
        maven {
            url "https://gradle.finogeeks.club/repository/applet/"
            credentials {
                username "applet"
                password "123321"
            }
        }
    }
}