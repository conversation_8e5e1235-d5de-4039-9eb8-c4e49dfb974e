<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    android:id="@+id/push_root_view"
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <FrameLayout
        android:id="@+id/v"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
    <FrameLayout
        android:id="@+id/layout_version_2"
        android:layout_width="0dp"
        android:layout_height="0dp" />
    <RelativeLayout
        android:id="@+id/push_notification_style_default"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone">
        <LinearLayout
            android:id="@+id/push_notification_layout_lefttop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentLeft="true"
            android:layout_toLeftOf="@+id/push_notification_big_icon"
            android:gravity="center_vertical"
            android:orientation="horizontal">
            <ImageView
                android:id="@+id/push_notification_small_icon"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:scaleType="centerInside" />
            <TextView
                style="@*android:style/TextAppearance.Material.Notification.Title"
                android:id="@+id/push_notification_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:maxLines="1"
                android:maxWidth="200dp"
                android:maxLength="24"
                android:textSize="12sp" />
            <TextView
                style="@*android:style/TextAppearance.Material.Notification.Info"
                android:id="@+id/push_notification_dot"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:textStyle="bold"
                android:text="·"
                android:textSize="16sp" />
            <TextView
                style="@*android:style/TextAppearance.Material.Notification.Time"
                android:maxLines="1"
                android:id="@+id/push_notification_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp" />
        </LinearLayout>
        <FrameLayout
            android:id="@+id/push_notification_for_bottom_margin"
            android:layout_width="match_parent"
            android:layout_alignParentBottom="true"
            android:layout_height="0dp"/>
        <ImageView
            android:id="@+id/push_notification_banner_icon"
            android:layout_width="match_parent"
            android:layout_height="96dp"
            android:layout_above="@+id/push_notification_for_bottom_margin"
            android:layout_marginBottom="8dp"
            android:visibility="gone"
            android:scaleType="centerCrop" />
        <LinearLayout
            android:id="@+id/push_notification_main_layout"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/push_notification_banner_icon"
            android:gravity="center_vertical"
            android:layout_marginRight="4dp"
            android:layout_marginBottom="5dp"
            android:layout_toLeftOf="@+id/push_notification_big_icon"
            android:layout_below="@id/push_notification_layout_lefttop">
            <TextView
                style="@*android:style/TextAppearance.Material.Notification.Title"
                android:id="@+id/push_notification_sub_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="1dp"
                android:maxLines="1"
                android:ellipsize="end"
                android:textSize="13sp"
                android:visibility="gone" />

            <TextView
                style="@*android:style/TextAppearance.Material.Notification.Info"
                android:id="@+id/push_notification_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:layout_marginTop="1dp"
                android:maxLines="2"
                android:textSize="13sp" />
            <TextView
                style="@*android:style/TextAppearance.Material.Notification.Info"
                android:id="@+id/push_notification_content_one_line"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:layout_marginTop="1dp"
                android:textSize="13sp"
                android:maxLines="1"
                android:visibility="gone" />
        </LinearLayout>
        <ImageView
            android:id="@+id/push_notification_big_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_alignParentRight="true"
            android:layout_alignTop="@+id/push_notification_main_layout"
            android:layout_alignBottom="@+id/push_notification_main_layout"
            android:scaleType="centerInside" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/push_notification_style_1"
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:visibility="gone">
        <ImageView
            android:id="@+id/push_notification_style_1_big_icon"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:scaleType="centerInside" />
        <LinearLayout
            android:id="@+id/push_notification_style_1_main_layout"
            android:layout_toRightOf="@+id/push_notification_style_1_big_icon"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/push_notification_layout_time"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <TextView
                    style="@*android:style/TextAppearance.Material.Notification.Title"
                    android:id="@+id/push_notification_style_1_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:layout_marginRight="8dp"
                    android:textSize="12sp"
                    android:textStyle="bold" />
                <TextView
                    style="@*android:style/TextAppearance.Material.Notification.Time"
                    android:id="@+id/push_notification_style_1_date"
                    android:layout_height="wrap_content"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_marginRight="4dp"
                    android:gravity="right"
                    android:textSize="12sp" />
                <TextView android:id="@+id/push_notification_null"
                    android:layout_width="24dp"
                    android:visibility="gone"
                    android:layout_height="20dp"/>
            </LinearLayout>

            <TextView
                style="@*android:style/TextAppearance.Material.Notification.Info"
                android:id="@+id/push_notification_style_1_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="4dp"
                android:ellipsize="end"
                android:layout_marginTop="1dp"
                android:maxLines="1"
                android:textSize="13sp" />
        </LinearLayout>
        <ImageView
            android:id="@+id/push_notification_style_1_banner_icon"
            android:layout_width="match_parent"
            android:layout_height="96dp"
            android:layout_alignParentBottom="true"
            android:visibility="gone"
            android:layout_below="@id/push_notification_style_1_main_layout"
            android:scaleType="centerInside" />
    </RelativeLayout>
    <FrameLayout
        android:id="@+id/push_notification_banner_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            android:id="@+id/push_notification_banner_img"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:visibility="gone" />
    </FrameLayout>

    <RelativeLayout
        android:id="@+id/push_notification_header_neg_fb"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_alignParentTop="true"
        android:visibility="gone">
        <ImageView android:id="@+id/push_notification_header_expand"
            android:layout_marginRight="2dp"
            android:layout_marginTop="2dp"
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_alignParentRight="true"
            android:layout_alignParentTop="true"
            android:scaleType="centerInside"
            android:background="#08000000"
            android:src="@drawable/jpush_ic_action_close"
            android:visibility="visible"/>
        <LinearLayout
            android:id="@+id/push_notification_fb_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="8dp"
            android:layout_below="@id/push_notification_header_expand"
            android:orientation="horizontal"
            android:visibility="gone"
            android:gravity="center">
            <TextView
                android:id="@+id/push_notification_fb_content_no_like1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#88000000"
                android:background="#0f000000"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:paddingLeft="6dp"
                android:paddingRight="6dp"
                android:textSize="14sp"
                android:gravity="center"
                android:maxLines="1"
                android:layout_margin="5dp"
                android:text="不感兴趣"/>
            <TextView
                android:id="@+id/push_notification_fb_content_no_like2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#88000000"
                android:background="#0f000000"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:paddingLeft="6dp"
                android:paddingRight="6dp"
                android:textSize="14sp"
                android:gravity="center"
                android:maxLines="1"
                android:layout_margin="5dp"
                android:text="重复收到"/>
            <TextView
                android:id="@+id/push_notification_fb_content_no_like3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#88000000"
                android:background="#0f000000"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:paddingLeft="6dp"
                android:paddingRight="6dp"
                android:textSize="14sp"
                android:gravity="center"
                android:maxLines="1"
                android:layout_margin="5dp"
                android:text="内容低质"/>
            <TextView
                android:id="@+id/push_notification_fb_content_no_like4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#88000000"
                android:background="#0f000000"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:paddingLeft="6dp"
                android:paddingRight="6dp"
                android:textSize="14sp"
                android:gravity="center"
                android:maxLines="1"
                android:layout_margin="5dp"
                android:text="内容不宜"/>
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>