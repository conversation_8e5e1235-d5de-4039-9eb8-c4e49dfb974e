<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/banner_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:weightSum="1.0">

        <RelativeLayout
            android:id="@+id/banner_content_root"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.0"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:padding="15dp"
            android:orientation="vertical"
            android:visibility="visible">

            <ImageView
                android:id="@+id/banner_image_only"
                android:adjustViewBounds="true"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:scaleType="fitXY"/>

            <ImageView
                android:id="@+id/banner_image"
                android:adjustViewBounds="true"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:layout_centerVertical="true"
                android:padding="5dp"
                android:layout_marginLeft="3dp"
                android:visibility="visible" />

            <LinearLayout
                android:id="@+id/banner_text_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="74dp"
                android:layout_toRightOf="@id/banner_image"
                android:layout_marginLeft="3dp"
                android:gravity="center_vertical"
                android:orientation="vertical"
                android:layout_centerInParent="true">
                <TextView
                    android:id="@+id/banner_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_toRightOf="@+id/banner_image"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:layout_marginRight="4dp"
                    android:text=""
                    android:textSize="14sp"
                    android:visibility="visible" />
                <TextView
                    android:id="@+id/banner_body"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/banner_title"
                    android:layout_toRightOf="@+id/banner_image"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:layout_marginRight="4dp"
                    android:text=""
                    android:textSize="14sp" />
            </LinearLayout>

        </RelativeLayout>

    </LinearLayout>

</FrameLayout>