<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="cn.jiguang.sdk">

    <!-- Required -->
    <permission
        android:name="${applicationId}.permission.JPUSH_MESSAGE"
        android:protectionLevel="signature" />
    <!-- Required  一些系统要求的权限，如访问网络等 -->
    <uses-permission android:name="${applicationId}.permission.JPUSH_MESSAGE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <!--huawei_permission_start-->
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
    <!--huawei_permission_end-->
    <!--vivo_permission_start-->
    <uses-permission android:name="com.vivo.notification.permission.BADGE_ICON" />
    <!--vivo_permission_end-->
    <!-- Optional for location -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <!--honor_permission_start-->
    <uses-permission android:name="com.hihonor.android.launcher.permission.CHANGE_BADGE" />
    <queries>
        <intent>
            <action android:name="com.hihonor.push.action.BIND_PUSH_SERVICE" />
        </intent>
    </queries>
    <!--honor_permission_end-->
    <!--xiaomi_permission_start-->
    <uses-permission android:name="android.permission.VIBRATE" />
    <permission
        android:name="${applicationId}.permission.MIPUSH_RECEIVE"
        android:protectionLevel="signature" />
    <uses-permission android:name="${applicationId}.permission.MIPUSH_RECEIVE" />
    <!--xiaomi_permission_end-->
    <!--oppo_permission_start-->
    <uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <!--oppo_permission_end-->
    <!--meizu_permission_start-->
    <uses-permission android:name="com.meizu.flyme.permission.PUSH" />
    <!--meizu_permission_end-->


    <!--jmrtc_permission_start-->
    <!-- Required for jmrtc -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <!--jmrtc_permission_end-->

    <!--jpush_permission_start-->
    <uses-sdk tools:overrideLibrary="
     cn.jpush.android.thirdpush.fcm
    ,cn.jpush.android.thirdpush.huawei
    ,cn.jpush.android.thirdpush.meizu
    ,cn.jpush.android.thirdpush.oppo
    ,cn.jpush.android.thirdpush.vivo
    ,cn.jpush.android.thirdpush.xiaomi
    ,com.google.firebase.firebase_core
    ,com.google.firebase.messaging
    ,com.google.firebase.analytics.connector.impl
    ,com.google.firebase.measurement
    ,com.google.android.gms.measurement.api
    ,com.google.firebase.measurement_impl
    ,com.google.firebase.iid
    ,com.google.firebase
    ,com.google.firebase.iid.internal
    ,com.google.android.gms.base
    ,com.google.android.gms.tasks
    ,com.google.firebase.analytics.connector
    ,com.google.android.gms.stats
    ,com.google.android.gms.ads_identifier
    ,com.google.android.gms.common
    ,com.google.android.gms.measurement_base
    ,com.huawei.android.hms.openid
    ,com.huawei.agconnect.core
    ,com.huawei.hmf.tasks
    ,com.huawei.hms.framework.network.grs
    ,com.huawei.hms.framework.common
    ,com.huawei.android.hms.base
    ,com.huawei.android.hms.push
    ,android.support.mediacompat
    ,android.support.fragment
    ,android.support.coreutils
    ,android.support.coreui
    ,android.support.compat
    ,android.arch.lifecycle" />
    <!--jpush_permission_end-->

    <application>

        <!--jpush_config_start-->
        <!-- Rich push 核心功能 since 2.0.6 -->
        <activity
            android:name="cn.jpush.android.ui.PopWinActivity"
            android:exported="true"
            tools:replace="android:theme"
            android:theme="@style/MyDialogStyle">
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT" />

                <action android:name="cn.jpush.android.ui.PopWinActivity" />

                <category android:name="${JPUSH_PKGNAME}" />
            </intent-filter>
        </activity>

        <!-- Required SDK核心功能 -->
        <activity
            android:name="cn.jpush.android.ui.PushActivity"
            android:configChanges="orientation|keyboardHidden"
            android:exported="true"
            android:theme="@android:style/Theme.NoTitleBar">
            <intent-filter>
                <action android:name="cn.jpush.android.ui.PushActivity" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="${JPUSH_PKGNAME}" />
            </intent-filter>
        </activity>

        <!-- since 3.5.0 Required SDK 核心功能 -->
        <!-- 3.5.0新增，用于定时展示功能 -->
        <receiver
            android:name="cn.jpush.android.service.SchedulerReceiver"
            android:exported="false" />

        <!-- 3.6.7 新增，用于负反馈组件 -->

        <!-- since 3.0.9 Required SDK 核心功能 -->
        <provider
            android:name="cn.jpush.android.service.DataProvider"
            android:authorities="${JPUSH_PKGNAME}.DataProvider"
            android:exported="false"
            android:process=":pushcore" />

        <!-- since 3.3.0 Required SDK核心功能 -->
        <activity
            android:name="cn.jpush.android.service.JNotifyActivity"
            android:exported="true"
            android:taskAffinity=""
            android:theme="@style/JPushTheme" >
            <intent-filter>
                <action android:name="cn.jpush.android.intent.JNotifyActivity" />
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="${applicationId}" />
            </intent-filter>
        </activity>
        <!-- since 4.6.0 Required SDK核心功能 -->
        <activity
            android:name="cn.android.service.JTransitActivity"
            android:exported="true"
            android:taskAffinity=""
            android:theme="@style/JPushTheme" >
            <intent-filter>
                <action android:name="cn.android.service.JTransitActivity" />
                <category android:name="android.intent.category.DEFAULT"/>

                <category android:name="${applicationId}" />
            </intent-filter>
        </activity>
        <!-- since 3.5.6 新增华硕通道 -->
        <receiver android:name="cn.jpush.android.asus.AsusPushMessageReceiver" />

        <!-- Required SDK 核心功能 -->
        <!-- 可配置android:process参数将PushService放在其他进程中 -->
        <service
                android:name="cn.jpush.android.service.PushService"
                android:enabled="true"
                android:exported="false"
                android:process=":pushcore">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.REGISTER" />
                <action android:name="cn.jpush.android.intent.REPORT" />
                <action android:name="cn.jpush.android.intent.PushService" />
                <action android:name="cn.jpush.android.intent.PUSH_TIME" />
            </intent-filter>
        </service>

        <!-- Required SDK核心功能 -->
        <receiver
                android:name="cn.jpush.android.service.PushReceiver"
                android:enabled="true"
                android:exported="false">
            <intent-filter android:priority="1000">
                <action android:name="cn.jpush.android.intent.NOTIFICATION_RECEIVED_PROXY" /> <!-- Required  显示通知栏 -->
                <category android:name="${JPUSH_PKGNAME}" />
            </intent-filter>
        </receiver>

        <!-- Required SDK核心功能 -->
        <receiver
                android:name="cn.jpush.android.service.AlarmReceiver"
                android:exported="false" />
        <!--jpush_config_end-->

        <!--xiaomi_config_start-->
        <service
            android:name="com.xiaomi.push.service.XMJobService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process=":pushcore" />
        <service
            android:name="com.xiaomi.push.service.XMPushService"
            android:enabled="true"
            android:process=":pushcore" />
        <service
            android:name="com.xiaomi.mipush.sdk.PushMessageHandler"
            android:enabled="true"
            android:exported="true" />
        <service
            android:name="com.xiaomi.mipush.sdk.MessageHandleService"
            android:enabled="true" />

        <receiver
            android:name="com.xiaomi.push.service.receivers.NetworkStatusReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.xiaomi.push.service.receivers.PingReceiver"
            android:exported="false"
            android:process=":pushcore">
            <intent-filter>
                <action android:name="com.xiaomi.push.PING_TIMER" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="cn.jpush.android.service.PluginXiaomiPlatformsReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.xiaomi.mipush.RECEIVE_MESSAGE" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.MESSAGE_ARRIVED" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mipush.ERROR" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="XIAOMI_APPKEY"
            android:value="${XIAOMI_APPKEY}" />
        <meta-data
            android:name="XIAOMI_APPID"
            android:value="${XIAOMI_APPID}" />
        <activity
            android:name="com.xiaomi.mipush.sdk.NotificationClickedActivity"
            android:enabled="true"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
            <meta-data
                android:name="supportStyle"
                android:value="scene|voip" />
        </activity>
        <!--xiaomi_config_end-->

        <!--huawei_config_start-->
        <service
            android:name="cn.jpush.android.service.PluginHuaweiPlatformsService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <!--huawei_config_end-->
        <!--nio_config_start-->
        <meta-data
            android:name="nio_push_app_id"
            android:value="${NIO_APPID}" />
        <!--nio_config_end-->

        <!--meizu_config_start-->
<!--        <service-->
<!--            android:name="com.meizu.cloud.pushsdk.NotificationService"-->
<!--            android:exported="true" />-->

<!--        <receiver-->
<!--            android:name="com.meizu.cloud.pushsdk.MzPushSystemReceiver"-->
<!--            android:permission="com.meizu.flyme.permission.PUSH"-->
<!--            android:exported="true">-->
<!--            <intent-filter>-->
<!--                <action android:name="com.meizu.flyme.push.intent.PUSH_SYSTEM" />-->
<!--            </intent-filter>-->
<!--        </receiver>-->

        <receiver android:name="cn.jpush.android.service.PluginMeizuPlatformsReceiver"
            android:exported="true">
            <intent-filter>
                <!-- 接收 push 消息 -->
                <action android:name="com.meizu.flyme.push.intent.MESSAGE" />
                <!-- 接收 register 消息 -->
                <action android:name="com.meizu.flyme.push.intent.REGISTER.FEEDBACK" />
                <!-- 接收 unregister 消息-->
                <action android:name="com.meizu.flyme.push.intent.UNREGISTER.FEEDBACK" />
                <!-- 兼容低版本 Flyme3 推送服务配置 -->
                <action android:name="com.meizu.c2dm.intent.REGISTRATION" />
                <action android:name="com.meizu.c2dm.intent.RECEIVE" />

                <category android:name="${applicationId}" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="MEIZU_APPKEY"
            android:value="${MEIZU_APPKEY}" />
        <meta-data
            android:name="MEIZU_APPID"
            android:value="${MEIZU_APPID}" />
        <!--meizu_config_end-->

        <!--fcm_config_start-->
        <service android:name="cn.jpush.android.service.PluginFCMMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <!--fcm_config_end-->

        <!--oppo_config_start-->
        <service
            android:name="cn.jpush.android.service.PluginOppoPushService"
            android:permission="com.coloros.mcs.permission.SEND_MCS_MESSAGE"
            android:exported="true">
            <intent-filter>
                <action android:name="com.coloros.mcs.action.RECEIVE_MCS_MESSAGE" />
            </intent-filter>
        </service>
        <!-- since JPushv3.6.8 ，oppov2.1.0 oppo 核心功能-->
        <service
            android:name="com.heytap.msp.push.service.DataMessageCallbackService"
            android:permission="com.heytap.mcs.permission.SEND_PUSH_MESSAGE"
            android:exported="true">

            <intent-filter>
                <action android:name="com.heytap.mcs.action.RECEIVE_MCS_MESSAGE" />

                <action android:name="com.heytap.msp.push.RECEIVE_MCS_MESSAGE" />
            </intent-filter>
        </service> <!--兼容Q版本-->
        <meta-data
            android:name="OPPO_APPKEY"
            android:value="${OPPO_APPKEY}" />
        <meta-data
            android:name="OPPO_APPID"
            android:value="${OPPO_APPID}" />
        <meta-data
            android:name="OPPO_APPSECRET"
            android:value="${OPPO_APPSECRET}" />
        <!--oppo_config_end-->

        <!--vivo_config_start-->
        <receiver android:name="cn.jpush.android.service.PluginVivoMessageReceiver"
            android:exported="true">
            <intent-filter>
                <!-- 接收 push 消息 -->
                <action android:name="com.vivo.pushclient.action.RECEIVE" />
            </intent-filter>
        </receiver>
        <service
            android:name="com.vivo.push.sdk.service.CommandClientService"
            android:permission="com.push.permission.UPSTAGESERVICE"
            android:exported="true" />
        <activity
            android:name="com.vivo.push.sdk.LinkProxyClientActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <meta-data
            android:name="com.vivo.push.api_key"
            android:value="${VIVO_APPKEY}" />
        <meta-data
            android:name="com.vivo.push.app_id"
            android:value="${VIVO_APPID}" />
        <!--vivo_config_end-->




        <!--Required_config-->
        <meta-data
            android:name="JPUSH_CHANNEL"
            android:value="${JPUSH_CHANNEL}" />
        <meta-data
            android:name="JPUSH_APPKEY"
            android:value="${JPUSH_APPKEY}" /> <!-- </>值来自开发者平台取得的AppKey -->
        <!--honor_config_start-->
        <service
            android:name="cn.jpush.android.service.JHonorService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.hihonor.push.action.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <meta-data
            android:name="com.hihonor.push.app_id"
            android:value="${HONOR_APPID}" />
        <!--honor_config_end-->
        <provider
            android:name="cn.jpush.android.service.InitProvider"
            android:authorities="${applicationId}.jiguang.InitProvider"
            android:exported="false"
            android:readPermission="${applicationId}.permission.JPUSH_MESSAGE"
            android:writePermission="${applicationId}.permission.JPUSH_MESSAGE" />
    </application>
</manifest>

