apply plugin: 'com.android.library'

android {
    namespace = "cn.jiguang.sdk"
    compileSdkVersion 30
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 30
        versionCode 1
        versionName "1.0"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    repositories {
        flatDir {
            dirs 'libs'
        }
    }
    lintOptions {
        abortOnError false
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }

}

dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
    compileOnly fileTree(dir: 'libs', include: ["*.aar"])

    //fcm_dependencies_start
    //谷歌请按照厂商文档配置主 gradle 谷歌镜像依赖和添加 google-services.json 后再打开此依赖
//    implementation 'com.google.firebase:firebase-messaging:23.1.2'
    //fcm_dependencies_end

    //huawei_dependencies_start
    //华为请按照厂商文档配置主 gradle 华为镜像依赖和添加 agconnect-services.json 后再打开此依赖
//    implementation 'com.huawei.hms:push:6.9.0.300'
    //huawei_dependencies_end


}

