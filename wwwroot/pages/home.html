<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFA App - 首页</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/custom.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 更新状态栏时间
        function updateTime() {
            const now = new Date();
            let hours = now.getHours();
            let minutes = now.getMinutes();
            hours = hours < 10 ? '0' + hours : hours;
            minutes = minutes < 10 ? '0' + minutes : minutes;
            document.getElementById('status-time').textContent = hours + ':' + minutes;
        }
        
        // 页面加载后初始化
        window.onload = function() {
            updateTime();
            setInterval(updateTime, 60000); // 每分钟更新一次
            
            // 初始化圆形进度条
            const ctx = document.getElementById('goalChart').getContext('2d');
            const progress = 0.68; // 68% 完成率
            
            // 绘制背景圆
            ctx.beginPath();
            ctx.arc(50, 50, 40, 0, 2 * Math.PI);
            ctx.lineWidth = 10;
            ctx.strokeStyle = '#F0F0F0';
            ctx.stroke();
            
            // 绘制进度
            ctx.beginPath();
            ctx.arc(50, 50, 40, -Math.PI/2, (2 * progress - 0.5) * Math.PI);
            ctx.lineWidth = 10;
            ctx.strokeStyle = '#FE2C55';
            ctx.stroke();
            
            // 绘制中心文字
            ctx.font = 'bold 22px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = '#333';
            ctx.fillText(Math.round(progress * 100) + '%', 50, 50);
            
            // 初始化拜访效率图表
            const visitEfficiencyCtx = document.getElementById('visitEfficiencyChart').getContext('2d');
            new Chart(visitEfficiencyCtx, {
                type: 'bar',
                data: {
                    labels: ['周一', '周二', '周三', '周四', '周五'],
                    datasets: [{
                        label: '计划拜访',
                        data: [4, 5, 3, 6, 4],
                        backgroundColor: 'rgba(254, 44, 85, 0.4)',
                        borderColor: 'rgba(254, 44, 85, 1)',
                        borderWidth: 1
                    }, {
                        label: '实际拜访',
                        data: [3, 5, 2, 4, 3],
                        backgroundColor: 'rgba(59, 130, 246, 0.4)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 8,
                            ticks: {
                                font: {
                                    size: 12
                                }
                            }
                        },
                        x: {
                            ticks: {
                                font: {
                                    size: 12
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                font: {
                                    size: 12
                                }
                            }
                        }
                    }
                }
            });
            
            // 初始化拜访成效图表
            const visitEffectivenessCtx = document.getElementById('visitEffectivenessChart').getContext('2d');
            new Chart(visitEffectivenessCtx, {
                type: 'doughnut',
                data: {
                    labels: ['成交', '跟进中', '无兴趣'],
                    datasets: [{
                        data: [35, 50, 15],
                        backgroundColor: [
                            'rgba(16, 185, 129, 0.7)',
                            'rgba(59, 130, 246, 0.7)',
                            'rgba(209, 213, 219, 0.7)'
                        ],
                        borderColor: [
                            'rgba(16, 185, 129, 1)',
                            'rgba(59, 130, 246, 1)',
                            'rgba(209, 213, 219, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                font: {
                                    size: 12
                                }
                            }
                        }
                    }
                }
            });
        }
    </script>
    <style>
        /* 增大全局字体大小 */
        body {
            font-size: 16px;
        }
        .text-xs {
            font-size: 0.85rem;
            line-height: 1.25rem;
        }
        .text-sm {
            font-size: 1rem;
            line-height: 1.5rem;
        }
        .text-lg {
            font-size: 1.25rem;
            line-height: 1.75rem;
        }
        .text-xl {
            font-size: 1.5rem;
            line-height: 2rem;
        }
        
        /* 调整导航字体大小 */
        .nav-bar {
            font-size: 1.25rem;
        }
        
        /* 调整卡片标题字体大小 */
        .card .font-semibold, .mt-2 .font-semibold {
            font-size: 1.15rem;
        }
        
        /* 调整统计卡片字体 */
        .stat-card .stat-value {
            font-size: 1.5rem;
        }
        
        /* 调整选项卡字体 */
        .tab-bar {
            font-size: 0.9rem;
        }
    </style>
</head>
<body class="bg-[#F6F6F6]">
    <!-- iOS状态栏 -->
    <div class="status-bar">
        <div class="status-bar-time" id="status-time">9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="text-pink-600 font-semibold text-xl">工作台</div>
        <div>
            <i class="fas fa-bell text-gray-500 relative text-xl">
                <span class="badge">3</span>
            </i>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content pb-20">
        <!-- 欢迎区域 -->
        <div class="p-4 pb-2">
            <div class="flex items-center">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像" class="w-14 h-14 rounded-full border-2 border-pink-500">
                <div class="ml-3">
                    <div class="text-lg font-bold">张经理，早上好！</div>
                    <div class="text-base text-gray-500">今天是个拿订单的好日子 👍</div>
                </div>
            </div>
        </div>
        
        <!-- 销售目标 -->
        <div class="card">
            <div class="flex justify-between items-center mb-2">
                <div class="font-semibold">本月销售目标</div>
                <div class="text-sm text-gray-500">剩余12天</div>
            </div>
            <div class="flex items-center">
                <canvas id="goalChart" width="100" height="100"></canvas>
                <div class="ml-4 flex-1">
                    <div class="flex justify-between items-center">
                        <div class="text-base text-gray-600">已完成金额</div>
                        <div class="font-semibold text-pink-600 text-lg">¥680,000</div>
                    </div>
                    <div class="progress-bar mt-1">
                        <div class="progress-fill" style="width: 68%"></div>
                    </div>
                    <div class="flex justify-between items-center mt-1">
                        <div class="text-base text-gray-600">目标金额</div>
                        <div class="font-semibold text-lg">¥1,000,000</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 今日任务 -->
        <div class="card">
            <div class="flex justify-between items-center mb-3">
                <div class="font-semibold">今日任务</div>
                <div class="text-sm text-pink-600">查看全部</div>
            </div>
            
            <div class="bg-gray-50 rounded-lg p-3 mb-3 flex items-center">
                <div class="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center text-pink-600 mr-3 text-lg">
                    <i class="fas fa-user-friends"></i>
                </div>
                <div class="flex-1">
                    <div class="font-medium text-base">客户拜访：上海万科集团</div>
                    <div class="text-sm text-gray-500 mt-1">10:30 - 12:00</div>
                </div>
                <div class="bg-pink-600 text-white text-sm rounded-full px-3 py-1">
                    即将开始
                </div>
            </div>
            
            <div class="bg-gray-50 rounded-lg p-3 mb-3 flex items-center">
                <div class="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center text-orange-600 mr-3 text-lg">
                    <i class="fas fa-phone-alt"></i>
                </div>
                <div class="flex-1">
                    <div class="font-medium text-base">电话跟进：北京朗科科技</div>
                    <div class="text-sm text-gray-500 mt-1">14:00 - 14:30</div>
                </div>
                <div class="bg-gray-200 text-gray-600 text-sm rounded-full px-3 py-1">
                    待开始
                </div>
            </div>
            
            <div class="bg-gray-50 rounded-lg p-3 flex items-center">
                <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-600 mr-3 text-lg">
                    <i class="fas fa-file-signature"></i>
                </div>
                <div class="flex-1">
                    <div class="font-medium text-base">合同签署：广州佳能打印</div>
                    <div class="text-sm text-gray-500 mt-1">16:00 - 17:30</div>
                </div>
                <div class="bg-gray-200 text-gray-600 text-sm rounded-full px-3 py-1">
                    待开始
                </div>
            </div>
        </div>
        
        <!-- 销售数据概览 -->
        <div class="mt-2">
            <div class="px-4 py-2 font-semibold">销售数据概览</div>
            <div class="grid grid-cols-2 gap-3 px-3">
                <div class="stat-card">
                    <div class="text-sm text-gray-500">本周新增客户</div>
                    <div class="stat-value">12</div>
                    <div class="text-sm text-green-500">
                        <i class="fas fa-arrow-up"></i> 20%
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="text-sm text-gray-500">本周新增商机</div>
                    <div class="stat-value">8</div>
                    <div class="text-sm text-green-500">
                        <i class="fas fa-arrow-up"></i> 15%
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="text-sm text-gray-500">本周跟进客户</div>
                    <div class="stat-value">23</div>
                    <div class="text-sm text-gray-500">
                        <i class="fas fa-minus"></i> 持平
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="text-sm text-gray-500">本周签约订单</div>
                    <div class="stat-value">5</div>
                    <div class="text-sm text-red-500">
                        <i class="fas fa-arrow-down"></i> 10%
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 拜访效率 Dashboard -->
        <div class="card mt-3">
            <div class="flex justify-between items-center mb-3">
                <div class="font-semibold">拜访效率分析</div>
                <div class="text-sm text-pink-600">查看详情</div>
            </div>
            
            <!-- 拜访数据统计 -->
            <div class="grid grid-cols-3 gap-2 mb-3">
                <div class="bg-gray-50 rounded-lg p-3 text-center">
                    <div class="text-sm text-gray-500 mb-1">本周拜访</div>
                    <div class="font-semibold text-xl">17</div>
                    <div class="text-sm text-green-500">
                        <i class="fas fa-arrow-up"></i> 12%
                    </div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-3 text-center">
                    <div class="text-sm text-gray-500 mb-1">拜访完成率</div>
                    <div class="font-semibold text-xl">85%</div>
                    <div class="text-sm text-green-500">
                        <i class="fas fa-arrow-up"></i> 5%
                    </div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-3 text-center">
                    <div class="text-sm text-gray-500 mb-1">平均时长</div>
                    <div class="font-semibold text-xl">72分</div>
                    <div class="text-sm text-red-500">
                        <i class="fas fa-arrow-down"></i> 3%
                    </div>
                </div>
            </div>
            
            <!-- 拜访图表 -->
            <div class="grid grid-cols-2 gap-3">
                <div class="bg-gray-50 rounded-lg p-3">
                    <div class="text-sm text-gray-700 font-medium mb-2">本周拜访计划vs实际</div>
                    <div style="height: 150px;">
                        <canvas id="visitEfficiencyChart"></canvas>
                    </div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-3">
                    <div class="text-sm text-gray-700 font-medium mb-2">拜访成效分布</div>
                    <div style="height: 150px;">
                        <canvas id="visitEffectivenessChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 拜访转化效率 -->
            <div class="mt-3">
                <div class="text-sm text-gray-700 font-medium mb-2">拜访转化效率</div>
                <div class="bg-gray-50 rounded-lg p-3">
                    <div class="flex justify-between items-center mb-1">
                        <div class="text-sm">访客转商机</div>
                        <div class="text-sm font-medium">45%</div>
                    </div>
                    <div class="progress-bar mt-1">
                        <div class="progress-fill bg-blue-500" style="width: 45%"></div>
                    </div>
                    
                    <div class="flex justify-between items-center mb-1 mt-3">
                        <div class="text-sm">商机转订单</div>
                        <div class="text-sm font-medium">28%</div>
                    </div>
                    <div class="progress-bar mt-1">
                        <div class="progress-fill bg-green-500" style="width: 28%"></div>
                    </div>
                    
                    <div class="flex justify-between items-center mb-1 mt-3">
                        <div class="text-sm">拜访平均成交金额</div>
                        <div class="text-sm font-medium">¥156,000</div>
                    </div>
                    <div class="progress-bar mt-1">
                        <div class="progress-fill" style="width: 65%"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 重要客户提醒 -->
        <div class="card mt-3">
            <div class="flex justify-between items-center mb-3">
                <div class="font-semibold">重要客户动态</div>
                <div class="text-sm text-pink-600">更多</div>
            </div>
            
            <div class="bg-gray-50 rounded-lg p-3 mb-3">
                <div class="flex items-center mb-2">
                    <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="客户头像" class="w-10 h-10 rounded-full mr-2">
                    <div class="font-medium text-base">广州科技有限公司</div>
                    <div class="ml-auto text-sm text-gray-500">今天 08:45</div>
                </div>
                <div class="text-base text-gray-700">合同到期提醒：维保服务合同将于7天后到期，请及时联系客户续约。</div>
                <div class="mt-2">
                    <button class="btn-outline text-sm py-1 px-3">查看详情</button>
                </div>
            </div>
            
            <div class="bg-gray-50 rounded-lg p-3">
                <div class="flex items-center mb-2">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="客户头像" class="w-10 h-10 rounded-full mr-2">
                    <div class="font-medium text-base">上海万科集团</div>
                    <div class="ml-auto text-sm text-gray-500">昨天 16:30</div>
                </div>
                <div class="text-base text-gray-700">产品咨询：客户对新款智能办公设备表示强烈兴趣，预计采购量50台。</div>
                <div class="mt-2">
                    <button class="btn-outline text-sm py-1 px-3">查看详情</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部选项卡 -->
    <div class="tab-bar">
        <div class="tab-item active">
            <i class="tab-icon fas fa-home text-lg"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-user-friends text-lg"></i>
            <span>客户</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-handshake text-lg"></i>
            <span>销售</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-calendar-check text-lg"></i>
            <span>任务</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-user text-lg"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html> 