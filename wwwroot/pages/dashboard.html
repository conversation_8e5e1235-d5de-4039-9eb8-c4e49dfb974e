<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFA App - 数据看板</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="../css/custom.css">
    <script>
        // 更新状态栏时间
        function updateTime() {
            const now = new Date();
            let hours = now.getHours();
            let minutes = now.getMinutes();
            hours = hours < 10 ? '0' + hours : hours;
            minutes = minutes < 10 ? '0' + minutes : minutes;
            document.getElementById('status-time').textContent = hours + ':' + minutes;
        }
        
        // 页面加载后初始化
        window.onload = function() {
            updateTime();
            setInterval(updateTime, 60000); // 每分钟更新一次
            
            // 销售业绩图表
            const performanceCtx = document.getElementById('performanceChart').getContext('2d');
            const performanceChart = new Chart(performanceCtx, {
                type: 'bar',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '销售业绩',
                        data: [65, 80, 75, 89, 95, 68],
                        backgroundColor: '#FE2C55',
                        borderRadius: 4,
                        barThickness: 10,
                    }, {
                        label: '目标',
                        data: [70, 70, 70, 70, 70, 100],
                        backgroundColor: 'rgba(254, 44, 85, 0.2)',
                        borderRadius: 4,
                        barThickness: 10,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                boxWidth: 10,
                                font: {
                                    size: 10
                                }
                            }
                        }
                    }
                }
            });
            
            // 销售漏斗图表
            const funnelCtx = document.getElementById('funnelChart').getContext('2d');
            const funnelChart = new Chart(funnelCtx, {
                type: 'bar',
                data: {
                    labels: ['初步接触', '需求确认', '方案报价', '商务谈判', '赢单'],
                    datasets: [{
                        label: '商机数量',
                        data: [35, 25, 20, 15, 5],
                        backgroundColor: [
                            '#FE2C55', '#FF4C79', '#FF6A93', '#FF89AC', '#FFA8C5'
                        ],
                        barThickness: 16,
                        borderRadius: 4
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            beginAtZero: true,
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        },
                        y: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        datalabels: {
                            color: '#fff',
                            font: {
                                weight: 'bold'
                            }
                        }
                    }
                }
            });
            
            // 客户增长图表
            const customerCtx = document.getElementById('customerChart').getContext('2d');
            const customerChart = new Chart(customerCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '新增客户',
                        data: [28, 35, 42, 38, 45, 52],
                        fill: true,
                        backgroundColor: 'rgba(254, 44, 85, 0.1)',
                        borderColor: '#FE2C55',
                        tension: 0.3,
                        pointBackgroundColor: '#FE2C55',
                        pointRadius: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                boxWidth: 10,
                                font: {
                                    size: 10
                                }
                            }
                        }
                    }
                }
            });
            
            // 产品销售占比
            const productCtx = document.getElementById('productChart').getContext('2d');
            const productChart = new Chart(productCtx, {
                type: 'doughnut',
                data: {
                    labels: ['智能打印设备', '办公自动化设备', '安保系统', '视频会议系统', '其他'],
                    datasets: [{
                        data: [35, 25, 20, 15, 5],
                        backgroundColor: [
                            '#FE2C55', '#FF4C79', '#FF6A93', '#FF89AC', '#FFA8C5'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '60%',
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 10,
                                font: {
                                    size: 10
                                }
                            }
                        }
                    }
                }
            });
        }
    </script>
</head>
<body class="bg-[#F6F6F6]">
    <!-- iOS状态栏 -->
    <div class="status-bar">
        <div class="status-bar-time" id="status-time">9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="text-pink-600 font-semibold text-xl">数据看板</div>
        <div class="flex items-center gap-4">
            <i class="fas fa-filter text-gray-500"></i>
            <i class="fas fa-ellipsis-h text-gray-500"></i>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content pb-20">
        <!-- 时间选择器 -->
        <div class="flex justify-between items-center bg-white p-3">
            <div class="text-sm text-gray-800 font-medium">数据范围</div>
            <div class="flex items-center text-sm">
                <select class="bg-gray-100 border-0 rounded-md py-1 px-2 text-xs">
                    <option>本月</option>
                    <option>上月</option>
                    <option>本季度</option>
                    <option>本年</option>
                    <option>自定义</option>
                </select>
            </div>
        </div>
        
        <!-- 关键指标卡片 -->
        <div class="grid grid-cols-2 gap-3 p-3">
            <div class="stat-card">
                <div class="text-xs text-gray-500">总销售额</div>
                <div class="stat-value">¥368万</div>
                <div class="text-xs text-green-500">
                    <i class="fas fa-arrow-up"></i> 23%
                </div>
            </div>
            
            <div class="stat-card">
                <div class="text-xs text-gray-500">新签订单</div>
                <div class="stat-value">56单</div>
                <div class="text-xs text-green-500">
                    <i class="fas fa-arrow-up"></i> 12%
                </div>
            </div>
            
            <div class="stat-card">
                <div class="text-xs text-gray-500">销售线索转化率</div>
                <div class="stat-value">32%</div>
                <div class="text-xs text-gray-500">
                    <i class="fas fa-minus"></i> 持平
                </div>
            </div>
            
            <div class="stat-card">
                <div class="text-xs text-gray-500">新增客户</div>
                <div class="stat-value">52家</div>
                <div class="text-xs text-green-500">
                    <i class="fas fa-arrow-up"></i> 18%
                </div>
            </div>
        </div>
        
        <!-- 销售业绩图表 -->
        <div class="card">
            <div class="flex justify-between items-center mb-3">
                <div class="font-semibold">销售业绩趋势</div>
                <div class="text-xs text-gray-500">目标完成率: 68%</div>
            </div>
            <div class="h-48">
                <canvas id="performanceChart"></canvas>
            </div>
        </div>
        
        <!-- 销售漏斗图表 -->
        <div class="card">
            <div class="flex justify-between items-center mb-3">
                <div class="font-semibold">销售漏斗分析</div>
                <div class="text-xs text-gray-500">总商机: 100个</div>
            </div>
            <div class="h-40">
                <canvas id="funnelChart"></canvas>
            </div>
        </div>
        
        <!-- 客户增长图表 -->
        <div class="card">
            <div class="flex justify-between items-center mb-3">
                <div class="font-semibold">客户增长趋势</div>
                <div class="text-xs text-pink-600">查看详情</div>
            </div>
            <div class="h-40">
                <canvas id="customerChart"></canvas>
            </div>
        </div>
        
        <!-- 产品销售占比 -->
        <div class="card">
            <div class="flex justify-between items-center mb-3">
                <div class="font-semibold">产品销售占比</div>
                <div class="text-xs text-pink-600">查看详情</div>
            </div>
            <div class="h-48">
                <canvas id="productChart"></canvas>
            </div>
        </div>
        
        <!-- 业绩排行榜 -->
        <div class="card">
            <div class="flex justify-between items-center mb-3">
                <div class="font-semibold">销售人员业绩排行</div>
                <div class="text-xs text-pink-600">查看全部</div>
            </div>
            
            <!-- 排行榜项目 -->
            <div class="space-y-3">
                <!-- 排行1 -->
                <div class="flex items-center">
                    <div class="w-6 h-6 bg-pink-600 rounded-full flex items-center justify-center text-white text-xs font-bold mr-3">1</div>
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="销售头像" class="w-8 h-8 rounded-full mr-3">
                    <div class="flex-1">
                        <div class="flex justify-between items-center">
                            <div class="font-medium">张经理</div>
                            <div class="text-sm font-semibold text-pink-600">¥92.5万</div>
                        </div>
                        <div class="progress-bar mt-1">
                            <div class="progress-fill" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 排行2 -->
                <div class="flex items-center">
                    <div class="w-6 h-6 bg-pink-400 rounded-full flex items-center justify-center text-white text-xs font-bold mr-3">2</div>
                    <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="销售头像" class="w-8 h-8 rounded-full mr-3">
                    <div class="flex-1">
                        <div class="flex justify-between items-center">
                            <div class="font-medium">李销售</div>
                            <div class="text-sm font-semibold text-pink-600">¥85.2万</div>
                        </div>
                        <div class="progress-bar mt-1">
                            <div class="progress-fill" style="width: 92%"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 排行3 -->
                <div class="flex items-center">
                    <div class="w-6 h-6 bg-pink-300 rounded-full flex items-center justify-center text-white text-xs font-bold mr-3">3</div>
                    <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="销售头像" class="w-8 h-8 rounded-full mr-3">
                    <div class="flex-1">
                        <div class="flex justify-between items-center">
                            <div class="font-medium">王销售</div>
                            <div class="text-sm font-semibold text-pink-600">¥78.6万</div>
                        </div>
                        <div class="progress-bar mt-1">
                            <div class="progress-fill" style="width: 85%"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 排行4 -->
                <div class="flex items-center">
                    <div class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-white text-xs font-bold mr-3">4</div>
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="销售头像" class="w-8 h-8 rounded-full mr-3">
                    <div class="flex-1">
                        <div class="flex justify-between items-center">
                            <div class="font-medium">赵销售</div>
                            <div class="text-sm font-semibold text-pink-600">¥64.8万</div>
                        </div>
                        <div class="progress-bar mt-1">
                            <div class="progress-fill" style="width: 70%"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 排行5 -->
                <div class="flex items-center">
                    <div class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-white text-xs font-bold mr-3">5</div>
                    <img src="https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="销售头像" class="w-8 h-8 rounded-full mr-3">
                    <div class="flex-1">
                        <div class="flex justify-between items-center">
                            <div class="font-medium">陈销售</div>
                            <div class="text-sm font-semibold text-pink-600">¥46.9万</div>
                        </div>
                        <div class="progress-bar mt-1">
                            <div class="progress-fill" style="width: 50%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部选项卡 -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="tab-icon fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-user-friends"></i>
            <span>客户</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-handshake"></i>
            <span>销售</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-calendar-check"></i>
            <span>任务</span>
        </div>
        <div class="tab-item active">
            <i class="tab-icon fas fa-chart-bar"></i>
            <span>报表</span>
        </div>
    </div>
</body>
</html> 