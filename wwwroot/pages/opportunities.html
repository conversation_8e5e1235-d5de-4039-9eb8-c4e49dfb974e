<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFA App - 销售机会</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/custom.css">
    <script>
        // 更新状态栏时间
        function updateTime() {
            const now = new Date();
            let hours = now.getHours();
            let minutes = now.getMinutes();
            hours = hours < 10 ? '0' + hours : hours;
            minutes = minutes < 10 ? '0' + minutes : minutes;
            document.getElementById('status-time').textContent = hours + ':' + minutes;
        }
        
        // 页面加载后初始化
        window.onload = function() {
            updateTime();
            setInterval(updateTime, 60000); // 每分钟更新一次
        }
    </script>
</head>
<body class="bg-[#F6F6F6]">
    <!-- iOS状态栏 -->
    <div class="status-bar">
        <div class="status-bar-time" id="status-time">9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="text-pink-600 font-semibold text-xl">销售机会</div>
        <div class="flex items-center gap-4">
            <i class="fas fa-filter text-gray-500"></i>
            <i class="fas fa-plus text-gray-500"></i>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content pb-20">
        <!-- 销售漏斗概览 -->
        <div class="card">
            <div class="font-semibold mb-3">销售漏斗</div>
            <div class="relative h-12 bg-gray-100 rounded-lg overflow-hidden flex">
                <div class="bg-pink-600 h-full flex items-center justify-center text-white text-xs" style="width: 35%;">
                    <span>初步接触</span>
                </div>
                <div class="bg-pink-500 h-full flex items-center justify-center text-white text-xs" style="width: 25%;">
                    <span>需求确认</span>
                </div>
                <div class="bg-pink-400 h-full flex items-center justify-center text-white text-xs" style="width: 20%;">
                    <span>方案报价</span>
                </div>
                <div class="bg-pink-300 h-full flex items-center justify-center text-white text-xs" style="width: 15%;">
                    <span>商务谈判</span>
                </div>
                <div class="bg-pink-200 h-full flex items-center justify-center text-xs" style="width: 5%;">
                    <span>赢单</span>
                </div>
            </div>
            <div class="flex justify-between mt-3 text-xs text-gray-500">
                <div>总计: <span class="font-semibold text-gray-700">20个</span></div>
                <div>预计金额: <span class="font-semibold text-pink-600">¥3,500,000</span></div>
            </div>
        </div>
        
        <!-- 销售阶段选项卡 -->
        <div class="flex overflow-x-auto border-b border-gray-200 bg-white px-2 no-scrollbar">
            <button class="py-3 px-4 border-b-2 border-pink-600 text-pink-600 font-medium whitespace-nowrap">全部</button>
            <button class="py-3 px-4 text-gray-500 whitespace-nowrap">初步接触</button>
            <button class="py-3 px-4 text-gray-500 whitespace-nowrap">需求确认</button>
            <button class="py-3 px-4 text-gray-500 whitespace-nowrap">方案报价</button>
            <button class="py-3 px-4 text-gray-500 whitespace-nowrap">商务谈判</button>
            <button class="py-3 px-4 text-gray-500 whitespace-nowrap">赢单</button>
        </div>
        
        <!-- 搜索栏 -->
        <div class="search-bar">
            <i class="fas fa-search search-icon"></i>
            <input type="text" placeholder="搜索商机名称、客户名称" class="search-input">
        </div>
        
        <!-- 商机列表 -->
        <div class="bg-white px-4">
            <!-- 商机1 -->
            <div class="border-b border-gray-100 py-4">
                <div class="flex justify-between items-start mb-2">
                    <div class="font-semibold">上海万科办公设备采购项目</div>
                    <div class="bg-pink-100 text-pink-600 text-xs px-2 py-1 rounded-full">商务谈判</div>
                </div>
                <div class="flex items-center mb-2">
                    <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="客户头像" class="w-5 h-5 rounded-full mr-2">
                    <div class="text-sm text-gray-700">上海万科集团</div>
                </div>
                <div class="grid grid-cols-3 gap-2 text-xs text-gray-500">
                    <div>
                        <div>金额</div>
                        <div class="font-semibold text-gray-700">¥1,200,000</div>
                    </div>
                    <div>
                        <div>负责人</div>
                        <div class="font-semibold text-gray-700">张经理</div>
                    </div>
                    <div>
                        <div>预计成交</div>
                        <div class="font-semibold text-gray-700">2023-07-15</div>
                    </div>
                </div>
                <div class="mt-2">
                    <div class="text-xs text-gray-500">赢率</div>
                    <div class="progress-bar mt-1">
                        <div class="progress-fill" style="width: 75%"></div>
                    </div>
                    <div class="flex justify-between text-xs mt-1">
                        <div class="text-gray-500">更新时间: 2023-06-12</div>
                        <div class="font-semibold text-pink-600">75%</div>
                    </div>
                </div>
            </div>
            
            <!-- 商机2 -->
            <div class="border-b border-gray-100 py-4">
                <div class="flex justify-between items-start mb-2">
                    <div class="font-semibold">北京朗科科技IT基础设施更新</div>
                    <div class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">方案报价</div>
                </div>
                <div class="flex items-center mb-2">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="客户头像" class="w-5 h-5 rounded-full mr-2">
                    <div class="text-sm text-gray-700">北京朗科科技</div>
                </div>
                <div class="grid grid-cols-3 gap-2 text-xs text-gray-500">
                    <div>
                        <div>金额</div>
                        <div class="font-semibold text-gray-700">¥800,000</div>
                    </div>
                    <div>
                        <div>负责人</div>
                        <div class="font-semibold text-gray-700">李销售</div>
                    </div>
                    <div>
                        <div>预计成交</div>
                        <div class="font-semibold text-gray-700">2023-08-20</div>
                    </div>
                </div>
                <div class="mt-2">
                    <div class="text-xs text-gray-500">赢率</div>
                    <div class="progress-bar mt-1">
                        <div class="progress-fill" style="width: 60%"></div>
                    </div>
                    <div class="flex justify-between text-xs mt-1">
                        <div class="text-gray-500">更新时间: 2023-06-10</div>
                        <div class="font-semibold text-pink-600">60%</div>
                    </div>
                </div>
            </div>
            
            <!-- 商机3 -->
            <div class="border-b border-gray-100 py-4">
                <div class="flex justify-between items-start mb-2">
                    <div class="font-semibold">广州佳能打印智能办公方案</div>
                    <div class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">赢单</div>
                </div>
                <div class="flex items-center mb-2">
                    <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="客户头像" class="w-5 h-5 rounded-full mr-2">
                    <div class="text-sm text-gray-700">广州佳能打印</div>
                </div>
                <div class="grid grid-cols-3 gap-2 text-xs text-gray-500">
                    <div>
                        <div>金额</div>
                        <div class="font-semibold text-gray-700">¥500,000</div>
                    </div>
                    <div>
                        <div>负责人</div>
                        <div class="font-semibold text-gray-700">王销售</div>
                    </div>
                    <div>
                        <div>预计成交</div>
                        <div class="font-semibold text-gray-700">2023-06-30</div>
                    </div>
                </div>
                <div class="mt-2">
                    <div class="text-xs text-gray-500">赢率</div>
                    <div class="progress-bar mt-1">
                        <div class="progress-fill bg-green-500" style="width: 100%"></div>
                    </div>
                    <div class="flex justify-between text-xs mt-1">
                        <div class="text-gray-500">更新时间: 2023-06-15</div>
                        <div class="font-semibold text-green-600">100%</div>
                    </div>
                </div>
            </div>
            
            <!-- 商机4 -->
            <div class="border-b border-gray-100 py-4">
                <div class="flex justify-between items-start mb-2">
                    <div class="font-semibold">深圳鼎盛实业自动化设备升级</div>
                    <div class="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full">需求确认</div>
                </div>
                <div class="flex items-center mb-2">
                    <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="客户头像" class="w-5 h-5 rounded-full mr-2">
                    <div class="text-sm text-gray-700">深圳鼎盛实业</div>
                </div>
                <div class="grid grid-cols-3 gap-2 text-xs text-gray-500">
                    <div>
                        <div>金额</div>
                        <div class="font-semibold text-gray-700">¥650,000</div>
                    </div>
                    <div>
                        <div>负责人</div>
                        <div class="font-semibold text-gray-700">赵销售</div>
                    </div>
                    <div>
                        <div>预计成交</div>
                        <div class="font-semibold text-gray-700">2023-09-10</div>
                    </div>
                </div>
                <div class="mt-2">
                    <div class="text-xs text-gray-500">赢率</div>
                    <div class="progress-bar mt-1">
                        <div class="progress-fill" style="width: 40%"></div>
                    </div>
                    <div class="flex justify-between text-xs mt-1">
                        <div class="text-gray-500">更新时间: 2023-06-05</div>
                        <div class="font-semibold text-pink-600">40%</div>
                    </div>
                </div>
            </div>
            
            <!-- 商机5 -->
            <div class="py-4">
                <div class="flex justify-between items-start mb-2">
                    <div class="font-semibold">杭州网络科技网络升级项目</div>
                    <div class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">初步接触</div>
                </div>
                <div class="flex items-center mb-2">
                    <img src="https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="客户头像" class="w-5 h-5 rounded-full mr-2">
                    <div class="text-sm text-gray-700">杭州网络科技</div>
                </div>
                <div class="grid grid-cols-3 gap-2 text-xs text-gray-500">
                    <div>
                        <div>金额</div>
                        <div class="font-semibold text-gray-700">¥350,000</div>
                    </div>
                    <div>
                        <div>负责人</div>
                        <div class="font-semibold text-gray-700">陈销售</div>
                    </div>
                    <div>
                        <div>预计成交</div>
                        <div class="font-semibold text-gray-700">2023-10-15</div>
                    </div>
                </div>
                <div class="mt-2">
                    <div class="text-xs text-gray-500">赢率</div>
                    <div class="progress-bar mt-1">
                        <div class="progress-fill" style="width: 20%"></div>
                    </div>
                    <div class="flex justify-between text-xs mt-1">
                        <div class="text-gray-500">更新时间: 2023-06-01</div>
                        <div class="font-semibold text-pink-600">20%</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 浮动添加按钮 -->
        <div class="fixed bottom-24 right-5">
            <button class="w-14 h-14 rounded-full bg-pink-600 text-white flex items-center justify-center shadow-lg">
                <i class="fas fa-plus text-lg"></i>
            </button>
        </div>
    </div>
    
    <!-- 底部选项卡 -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="tab-icon fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-user-friends"></i>
            <span>客户</span>
        </div>
        <div class="tab-item active">
            <i class="tab-icon fas fa-handshake"></i>
            <span>销售</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-calendar-check"></i>
            <span>任务</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html> 