<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFA App - 用户管理</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/custom.css">
    <script>
        // 更新状态栏时间
        function updateTime() {
            const now = new Date();
            let hours = now.getHours();
            let minutes = now.getMinutes();
            hours = hours < 10 ? '0' + hours : hours;
            minutes = minutes < 10 ? '0' + minutes : minutes;
            document.getElementById('status-time').textContent = hours + ':' + minutes;
        }
        
        // 页面加载后初始化
        window.onload = function() {
            updateTime();
            setInterval(updateTime, 60000); // 每分钟更新一次
        }
    </script>
    <style>
        /* 增大全局字体大小 */
        body {
            font-size: 16px;
        }
        .text-xs {
            font-size: 0.85rem;
            line-height: 1.25rem;
        }
        .text-sm {
            font-size: 1rem;
            line-height: 1.5rem;
        }
        .text-lg {
            font-size: 1.25rem;
            line-height: 1.75rem;
        }
        .text-xl {
            font-size: 1.5rem;
            line-height: 2rem;
        }
        
        /* 调整导航字体大小 */
        .nav-bar {
            font-size: 1.25rem;
        }
        
        /* 调整卡片标题字体大小 */
        .card .font-semibold, .mt-2 .font-semibold {
            font-size: 1.15rem;
        }
        
        /* 调整选项卡字体 */
        .tab-bar {
            font-size: 0.9rem;
        }
        
        /* 用户列表项样式 */
        .user-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 12px;
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-name {
            font-weight: 500;
            font-size: 1.1rem;
            margin-bottom: 2px;
        }
        
        .user-position {
            color: #666;
            font-size: 0.95rem;
            margin-bottom: 4px;
        }
        
        .user-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }
        
        .tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            display: inline-block;
        }
        
        .tag-pink {
            background-color: rgba(254, 44, 85, 0.1);
            color: #FE2C55;
        }
        
        .tag-blue {
            background-color: rgba(59, 130, 246, 0.1);
            color: #3B82F6;
        }
        
        .tag-green {
            background-color: rgba(16, 185, 129, 0.1);
            color: #10B981;
        }
        
        .tag-orange {
            background-color: rgba(249, 115, 22, 0.1);
            color: #F97316;
        }
    </style>
</head>
<body class="bg-[#F6F6F6]">
    <!-- iOS状态栏 -->
    <div class="status-bar">
        <div class="status-bar-time" id="status-time">9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="text-pink-600 font-semibold text-xl">用户管理</div>
        <div class="flex items-center gap-4">
            <i class="fas fa-plus text-gray-500 text-lg"></i>
            <i class="fas fa-filter text-gray-500 text-lg"></i>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content pb-20">
        <!-- 搜索栏 -->
        <div class="search-bar">
            <i class="fas fa-search search-icon"></i>
            <input type="text" placeholder="搜索用户姓名、部门或职位" class="search-input text-base">
        </div>
        
        <!-- 用户分类选项卡 -->
        <div class="flex justify-between border-b border-gray-200 bg-white px-2">
            <button class="py-3 px-4 border-b-2 border-pink-600 text-pink-600 font-medium text-base">全部</button>
            <button class="py-3 px-4 text-gray-500 text-base">销售</button>
            <button class="py-3 px-4 text-gray-500 text-base">管理</button>
            <button class="py-3 px-4 text-gray-500 text-base">技术</button>
        </div>
        
        <!-- 用户列表 -->
        <div class="bg-white">
            <!-- 用户1 -->
            <div class="user-item">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像" class="user-avatar">
                <div class="user-info">
                    <div class="user-name">张俊杰</div>
                    <div class="user-position">销售总监 | 华东大区</div>
                    <div class="user-tags">
                        <span class="tag tag-pink">管理员</span>
                        <span class="tag tag-blue">销售团队</span>
                    </div>
                </div>
                <div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- 用户2 -->
            <div class="user-item">
                <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像" class="user-avatar">
                <div class="user-info">
                    <div class="user-name">李梦琪</div>
                    <div class="user-position">高级销售经理 | 南方大区</div>
                    <div class="user-tags">
                        <span class="tag tag-blue">销售团队</span>
                        <span class="tag tag-green">绩效优秀</span>
                    </div>
                </div>
                <div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- 用户3 -->
            <div class="user-item">
                <img src="https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像" class="user-avatar">
                <div class="user-info">
                    <div class="user-name">王伟东</div>
                    <div class="user-position">销售经理 | 华北大区</div>
                    <div class="user-tags">
                        <span class="tag tag-blue">销售团队</span>
                        <span class="tag tag-orange">新进人员</span>
                    </div>
                </div>
                <div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- 用户4 -->
            <div class="user-item">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像" class="user-avatar">
                <div class="user-info">
                    <div class="user-name">刘建国</div>
                    <div class="user-position">人力资源总监 | 总部</div>
                    <div class="user-tags">
                        <span class="tag tag-pink">管理员</span>
                        <span class="tag tag-blue">管理团队</span>
                    </div>
                </div>
                <div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- 用户5 -->
            <div class="user-item">
                <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像" class="user-avatar">
                <div class="user-info">
                    <div class="user-name">郑明辉</div>
                    <div class="user-position">技术总监 | 产品部</div>
                    <div class="user-tags">
                        <span class="tag tag-blue">技术团队</span>
                        <span class="tag tag-green">产品开发</span>
                    </div>
                </div>
                <div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- 用户6 -->
            <div class="user-item">
                <img src="https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像" class="user-avatar">
                <div class="user-info">
                    <div class="user-name">陈小红</div>
                    <div class="user-position">销售顾问 | 华东大区</div>
                    <div class="user-tags">
                        <span class="tag tag-blue">销售团队</span>
                        <span class="tag tag-orange">培训中</span>
                    </div>
                </div>
                <div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- 用户7 -->
            <div class="user-item">
                <img src="https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像" class="user-avatar">
                <div class="user-info">
                    <div class="user-name">林佳怡</div>
                    <div class="user-position">市场经理 | 市场部</div>
                    <div class="user-tags">
                        <span class="tag tag-blue">管理团队</span>
                        <span class="tag tag-green">策划</span>
                    </div>
                </div>
                <div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>
        
        <!-- 浮动添加按钮 -->
        <div class="fixed bottom-24 right-5">
            <button class="w-14 h-14 rounded-full bg-pink-600 text-white flex items-center justify-center shadow-lg">
                <i class="fas fa-plus text-lg"></i>
            </button>
        </div>
    </div>
    
    <!-- 底部选项卡 -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="tab-icon fas fa-home text-lg"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-user-friends text-lg"></i>
            <span>客户</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-handshake text-lg"></i>
            <span>销售</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-calendar-check text-lg"></i>
            <span>任务</span>
        </div>
        <div class="tab-item active">
            <i class="tab-icon fas fa-user text-lg"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html> 