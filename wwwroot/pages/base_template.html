<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFA App - 页面模板</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/custom.css">
    <script>
        // 更新状态栏时间
        function updateTime() {
            const now = new Date();
            let hours = now.getHours();
            let minutes = now.getMinutes();
            hours = hours < 10 ? '0' + hours : hours;
            minutes = minutes < 10 ? '0' + minutes : minutes;
            document.getElementById('status-time').textContent = hours + ':' + minutes;
        }
        
        // 页面加载后初始化
        window.onload = function() {
            updateTime();
            setInterval(updateTime, 60000); // 每分钟更新一次
        }
    </script>
</head>
<body class="bg-[#F6F6F6]">
    <!-- iOS状态栏 -->
    <div class="status-bar">
        <div class="status-bar-time" id="status-time">9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <!-- 导航栏 - 根据页面需要修改 -->
    <div class="nav-bar">
        <div>标题</div>
        <div>
            <i class="fas fa-ellipsis-h"></i>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
        <!-- 页面内容将在这里 -->
    </div>
    
    <!-- 底部选项卡 -->
    <div class="tab-bar">
        <div class="tab-item active">
            <i class="tab-icon fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-user-friends"></i>
            <span>客户</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-handshake"></i>
            <span>销售</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-calendar-check"></i>
            <span>任务</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html> 