<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFA App - 客户管理</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/custom.css">
    <script>
        // 更新状态栏时间
        function updateTime() {
            const now = new Date();
            let hours = now.getHours();
            let minutes = now.getMinutes();
            hours = hours < 10 ? '0' + hours : hours;
            minutes = minutes < 10 ? '0' + minutes : minutes;
            document.getElementById('status-time').textContent = hours + ':' + minutes;
        }
        
        // 页面加载后初始化
        window.onload = function() {
            updateTime();
            setInterval(updateTime, 60000); // 每分钟更新一次
        }
    </script>
</head>
<body class="bg-[#F6F6F6]">
    <!-- iOS状态栏 -->
    <div class="status-bar">
        <div class="status-bar-time" id="status-time">9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="text-pink-600 font-semibold text-xl">客户管理</div>
        <div class="flex items-center gap-4">
            <i class="fas fa-plus text-gray-500"></i>
            <i class="fas fa-filter text-gray-500"></i>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content pb-20">
        <!-- 搜索栏 -->
        <div class="search-bar">
            <i class="fas fa-search search-icon"></i>
            <input type="text" placeholder="搜索客户名称、联系人或电话" class="search-input">
        </div>
        
        <!-- 客户分类选项卡 -->
        <div class="flex justify-between border-b border-gray-200 bg-white px-2">
            <button class="py-3 px-4 border-b-2 border-pink-600 text-pink-600 font-medium">全部</button>
            <button class="py-3 px-4 text-gray-500">重点客户</button>
            <button class="py-3 px-4 text-gray-500">普通客户</button>
            <button class="py-3 px-4 text-gray-500">潜在客户</button>
        </div>
        
        <!-- 客户列表 -->
        <div class="bg-white">
            <!-- 客户1 -->
            <div class="customer-item">
                <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="客户头像" class="customer-avatar">
                <div class="customer-info">
                    <div class="customer-name">上海万科集团</div>
                    <div class="customer-company">张总监 | 18612345678</div>
                    <div class="customer-tags">
                        <span class="tag tag-pink">A级客户</span>
                        <span class="tag tag-blue">地产行业</span>
                    </div>
                </div>
                <div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- 客户2 -->
            <div class="customer-item">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="客户头像" class="customer-avatar">
                <div class="customer-info">
                    <div class="customer-name">北京朗科科技</div>
                    <div class="customer-company">李经理 | 13987654321</div>
                    <div class="customer-tags">
                        <span class="tag tag-pink">A级客户</span>
                        <span class="tag tag-blue">IT行业</span>
                    </div>
                </div>
                <div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- 客户3 -->
            <div class="customer-item">
                <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="客户头像" class="customer-avatar">
                <div class="customer-info">
                    <div class="customer-name">广州佳能打印</div>
                    <div class="customer-company">王总 | 15888888888</div>
                    <div class="customer-tags">
                        <span class="tag tag-blue">B级客户</span>
                        <span class="tag tag-orange">制造业</span>
                    </div>
                </div>
                <div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- 客户4 -->
            <div class="customer-item">
                <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="客户头像" class="customer-avatar">
                <div class="customer-info">
                    <div class="customer-name">深圳鼎盛实业</div>
                    <div class="customer-company">赵总 | 13777777777</div>
                    <div class="customer-tags">
                        <span class="tag tag-blue">B级客户</span>
                        <span class="tag tag-blue">电子行业</span>
                    </div>
                </div>
                <div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- 客户5 -->
            <div class="customer-item">
                <img src="https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="客户头像" class="customer-avatar">
                <div class="customer-info">
                    <div class="customer-name">杭州网络科技</div>
                    <div class="customer-company">陈经理 | 13666666666</div>
                    <div class="customer-tags">
                        <span class="tag tag-orange">C级客户</span>
                        <span class="tag tag-blue">互联网</span>
                    </div>
                </div>
                <div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- 客户6 -->
            <div class="customer-item">
                <img src="https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="客户头像" class="customer-avatar">
                <div class="customer-info">
                    <div class="customer-name">成都软件有限公司</div>
                    <div class="customer-company">刘总 | 13555555555</div>
                    <div class="customer-tags">
                        <span class="tag tag-orange">C级客户</span>
                        <span class="tag tag-blue">软件行业</span>
                    </div>
                </div>
                <div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- 客户7 -->
            <div class="customer-item">
                <img src="https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="客户头像" class="customer-avatar">
                <div class="customer-info">
                    <div class="customer-name">武汉医疗设备公司</div>
                    <div class="customer-company">孙总 | 13444444444</div>
                    <div class="customer-tags">
                        <span class="tag tag-pink">A级客户</span>
                        <span class="tag tag-blue">医疗行业</span>
                    </div>
                </div>
                <div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
        </div>
        
        <!-- 浮动添加按钮 -->
        <div class="fixed bottom-24 right-5">
            <button class="w-14 h-14 rounded-full bg-pink-600 text-white flex items-center justify-center shadow-lg">
                <i class="fas fa-plus text-lg"></i>
            </button>
        </div>
    </div>
    
    <!-- 底部选项卡 -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="tab-icon fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item active">
            <i class="tab-icon fas fa-user-friends"></i>
            <span>客户</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-handshake"></i>
            <span>销售</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-calendar-check"></i>
            <span>任务</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html> 