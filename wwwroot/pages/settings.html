<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFA App - 设置</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/custom.css">
    <script>
        // 更新状态栏时间
        function updateTime() {
            const now = new Date();
            let hours = now.getHours();
            let minutes = now.getMinutes();
            hours = hours < 10 ? '0' + hours : hours;
            minutes = minutes < 10 ? '0' + minutes : minutes;
            document.getElementById('status-time').textContent = hours + ':' + minutes;
        }
        
        // 页面加载后初始化
        window.onload = function() {
            updateTime();
            setInterval(updateTime, 60000); // 每分钟更新一次
        }
    </script>
</head>
<body class="bg-[#F6F6F6]">
    <!-- iOS状态栏 -->
    <div class="status-bar">
        <div class="status-bar-time" id="status-time">9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="flex items-center">
            <i class="fas fa-chevron-left mr-2 text-gray-500"></i>
            <span>设置</span>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content pb-20">
        <!-- 个人信息区域 -->
        <div class="bg-white mb-4">
            <div class="p-4 flex items-center">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像" class="w-16 h-16 rounded-full mr-4">
                <div class="flex-1">
                    <div class="text-lg font-semibold">张经理</div>
                    <div class="text-sm text-gray-500">高级销售经理 | 华东大区</div>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>
        
        <!-- 账号安全 -->
        <div class="bg-white mb-4">
            <div class="px-4 py-3 text-sm text-gray-500 border-b border-gray-100">
                账号安全
            </div>
            
            <div class="px-4 py-3 flex justify-between items-center border-b border-gray-100">
                <div class="flex items-center">
                    <i class="fas fa-lock text-gray-500 w-6"></i>
                    <span class="ml-2">修改密码</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
            
            <div class="px-4 py-3 flex justify-between items-center border-b border-gray-100">
                <div class="flex items-center">
                    <i class="fas fa-mobile-alt text-gray-500 w-6"></i>
                    <span class="ml-2">绑定手机</span>
                </div>
                <div class="flex items-center text-gray-500">
                    <span class="text-sm mr-2">186****5678</span>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <div class="px-4 py-3 flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-fingerprint text-gray-500 w-6"></i>
                    <span class="ml-2">生物识别</span>
                </div>
                <div class="flex items-center">
                    <div class="relative inline-block w-10 mr-2 align-middle select-none">
                        <input type="checkbox" checked name="toggle" id="toggle-biometric" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"/>
                        <label for="toggle-biometric" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 通知设置 -->
        <div class="bg-white mb-4">
            <div class="px-4 py-3 text-sm text-gray-500 border-b border-gray-100">
                通知设置
            </div>
            
            <div class="px-4 py-3 flex justify-between items-center border-b border-gray-100">
                <div class="flex items-center">
                    <i class="fas fa-bell text-gray-500 w-6"></i>
                    <span class="ml-2">应用通知</span>
                </div>
                <div class="flex items-center">
                    <div class="relative inline-block w-10 mr-2 align-middle select-none">
                        <input type="checkbox" checked name="toggle" id="toggle-app-notify" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"/>
                        <label for="toggle-app-notify" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
                    </div>
                </div>
            </div>
            
            <div class="px-4 py-3 flex justify-between items-center border-b border-gray-100">
                <div class="flex items-center">
                    <i class="fas fa-envelope text-gray-500 w-6"></i>
                    <span class="ml-2">邮件通知</span>
                </div>
                <div class="flex items-center">
                    <div class="relative inline-block w-10 mr-2 align-middle select-none">
                        <input type="checkbox" name="toggle" id="toggle-email-notify" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"/>
                        <label for="toggle-email-notify" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
                    </div>
                </div>
            </div>
            
            <div class="px-4 py-3 flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-comment-alt text-gray-500 w-6"></i>
                    <span class="ml-2">短信通知</span>
                </div>
                <div class="flex items-center">
                    <div class="relative inline-block w-10 mr-2 align-middle select-none">
                        <input type="checkbox" checked name="toggle" id="toggle-sms-notify" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"/>
                        <label for="toggle-sms-notify" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 通用设置 -->
        <div class="bg-white mb-4">
            <div class="px-4 py-3 text-sm text-gray-500 border-b border-gray-100">
                通用设置
            </div>
            
            <div class="px-4 py-3 flex justify-between items-center border-b border-gray-100">
                <div class="flex items-center">
                    <i class="fas fa-globe text-gray-500 w-6"></i>
                    <span class="ml-2">语言</span>
                </div>
                <div class="flex items-center text-gray-500">
                    <span class="text-sm mr-2">简体中文</span>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <div class="px-4 py-3 flex justify-between items-center border-b border-gray-100">
                <div class="flex items-center">
                    <i class="fas fa-moon text-gray-500 w-6"></i>
                    <span class="ml-2">深色模式</span>
                </div>
                <div class="flex items-center">
                    <div class="relative inline-block w-10 mr-2 align-middle select-none">
                        <input type="checkbox" name="toggle" id="toggle-dark-mode" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"/>
                        <label for="toggle-dark-mode" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
                    </div>
                </div>
            </div>
            
            <div class="px-4 py-3 flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-database text-gray-500 w-6"></i>
                    <span class="ml-2">清除缓存</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>
        
        <!-- 其他设置 -->
        <div class="bg-white mb-4">
            <div class="px-4 py-3 flex justify-between items-center border-b border-gray-100">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-gray-500 w-6"></i>
                    <span class="ml-2">关于我们</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
            
            <div class="px-4 py-3 flex justify-between items-center border-b border-gray-100">
                <div class="flex items-center">
                    <i class="fas fa-headset text-gray-500 w-6"></i>
                    <span class="ml-2">联系客服</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
            
            <div class="px-4 py-3 flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-user-shield text-gray-500 w-6"></i>
                    <span class="ml-2">隐私政策</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>
        
        <!-- 退出登录 -->
        <div class="p-4">
            <button class="w-full py-3 bg-white text-pink-600 font-medium rounded-md">
                退出登录
            </button>
        </div>
        
        <div class="text-center text-gray-400 text-xs p-4">
            版本号: v1.0.0
        </div>
    </div>
    
    <style>
        /* 自定义开关按钮样式 */
        .toggle-checkbox:checked {
            right: 0;
            border-color: #FE2C55;
        }
        .toggle-checkbox:checked + .toggle-label {
            background-color: #FE2C55;
        }
        .toggle-checkbox {
            right: 0;
            transition: all 0.3s;
        }
        .toggle-label {
            transition: all 0.3s;
        }
    </style>
</body>
</html> 