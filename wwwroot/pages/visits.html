<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFA App - 拜访计划</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/custom.css">
    <script>
        // 更新状态栏时间
        function updateTime() {
            const now = new Date();
            let hours = now.getHours();
            let minutes = now.getMinutes();
            hours = hours < 10 ? '0' + hours : hours;
            minutes = minutes < 10 ? '0' + minutes : minutes;
            document.getElementById('status-time').textContent = hours + ':' + minutes;
        }
        
        // 当前日期和月份
        function getCurrentMonthDays() {
            const now = new Date();
            const year = now.getFullYear();
            const month = now.getMonth();
            const daysInMonth = new Date(year, month + 1, 0).getDate();
            const firstDayOfMonth = new Date(year, month, 1).getDay(); // 0 for Sunday
            
            let days = [];
            // 添加上个月的最后几天
            const prevMonthDays = new Date(year, month, 0).getDate();
            for (let i = 0; i < firstDayOfMonth; i++) {
                days.push({
                    date: prevMonthDays - firstDayOfMonth + i + 1,
                    currentMonth: false,
                    isToday: false
                });
            }
            
            // 添加当前月的天数
            const today = now.getDate();
            for (let i = 1; i <= daysInMonth; i++) {
                days.push({
                    date: i,
                    currentMonth: true,
                    isToday: i === today
                });
            }
            
            // 添加下个月的开始几天，使总数为42（6周）
            const remainingDays = 42 - days.length;
            for (let i = 1; i <= remainingDays; i++) {
                days.push({
                    date: i,
                    currentMonth: false,
                    isToday: false
                });
            }
            
            return days;
        }
        
        // 渲染日历
        function renderCalendar() {
            const days = getCurrentMonthDays();
            const calendarEl = document.getElementById('calendar-days');
            const months = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
            const now = new Date();
            
            // 设置月份和年份
            document.getElementById('current-month').textContent = months[now.getMonth()];
            document.getElementById('current-year').textContent = now.getFullYear();
            
            let calendarHTML = '';
            
            // 添加星期头部
            const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
            for (let i = 0; i < 7; i++) {
                calendarHTML += `<div class="text-center text-xs font-medium text-gray-500 py-2">${weekdays[i]}</div>`;
            }
            
            // 添加日期
            days.forEach(day => {
                let dayClass = 'flex flex-col items-center justify-center h-10';
                if (!day.currentMonth) {
                    dayClass += ' text-gray-400'; // 非当前月的日期
                }
                
                if (day.isToday) {
                    dayClass += ' bg-pink-600 rounded-full text-white'; // 今天
                }
                
                // 添加拜访计划标记
                let hasPlan = false;
                // 示例：在本月的10、15、20日添加拜访计划标记
                if (day.currentMonth && (day.date === 10 || day.date === 15 || day.date === 20)) {
                    hasPlan = true;
                }
                
                calendarHTML += `
                <div class="calendar-day">
                    <div class="${dayClass}">
                        <span class="text-sm">${day.date}</span>
                        ${hasPlan ? '<div class="w-1 h-1 bg-pink-600 rounded-full mt-1"></div>' : ''}
                    </div>
                </div>`;
            });
            
            calendarEl.innerHTML = calendarHTML;
        }
        
        // 页面加载后初始化
        window.onload = function() {
            updateTime();
            setInterval(updateTime, 60000); // 每分钟更新一次
            renderCalendar();
        }
    </script>
</head>
<body class="bg-[#F6F6F6]">
    <!-- iOS状态栏 -->
    <div class="status-bar">
        <div class="status-bar-time" id="status-time">9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="text-pink-600 font-semibold text-xl">拜访计划</div>
        <div class="flex items-center gap-4">
            <i class="fas fa-calendar-alt text-gray-500"></i>
            <i class="fas fa-plus text-gray-500"></i>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content pb-20">
        <!-- 日历视图 -->
        <div class="bg-white p-4 shadow-sm">
            <div class="flex justify-between items-center mb-4">
                <button class="text-gray-500">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div class="flex items-center">
                    <span id="current-month" class="font-semibold mr-1">六月</span>
                    <span id="current-year">2023</span>
                </div>
                <button class="text-gray-500">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            
            <div id="calendar-days" class="grid grid-cols-7 gap-1">
                <!-- 日历内容将通过JavaScript生成 -->
            </div>
        </div>
        
        <!-- 今日拜访计划 -->
        <div class="mt-4">
            <div class="px-4 py-2 flex justify-between items-center">
                <div class="font-semibold">今日拜访计划</div>
                <div class="text-sm text-pink-600">2023年6月15日</div>
            </div>
            
            <div class="bg-white">
                <!-- 拜访计划1 -->
                <div class="border-b border-gray-100 p-4">
                    <div class="flex items-start">
                        <div class="w-10 h-10 rounded-full bg-pink-100 flex items-center justify-center text-pink-600 mr-3 mt-1">
                            <i class="fas fa-user-friends"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-semibold mb-1">上海万科集团拜访</div>
                            <div class="flex items-center text-sm text-gray-600 mb-1">
                                <i class="fas fa-clock mr-1"></i>
                                <span>10:30 - 12:00</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-600 mb-1">
                                <i class="fas fa-map-marker-alt mr-1"></i>
                                <span>上海市浦东新区世纪大道1号</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-600 mb-1">
                                <i class="fas fa-user mr-1"></i>
                                <span>联系人: 张总监 (18612345678)</span>
                            </div>
                            <div class="text-sm text-gray-700 mt-2">
                                沟通智能办公设备解决方案，准备产品演示和方案文档。
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end mt-3 gap-2">
                        <button class="bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full">
                            <i class="fas fa-map-marked-alt mr-1"></i> 导航
                        </button>
                        <button class="bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full">
                            <i class="fas fa-phone-alt mr-1"></i> 联系
                        </button>
                        <button class="bg-pink-100 text-pink-600 text-xs px-3 py-1 rounded-full">
                            <i class="fas fa-check-circle mr-1"></i> 完成拜访
                        </button>
                    </div>
                </div>
                
                <!-- 拜访计划2 -->
                <div class="p-4">
                    <div class="flex items-start">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3 mt-1">
                            <i class="fas fa-user-friends"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-semibold mb-1">北京朗科科技拜访</div>
                            <div class="flex items-center text-sm text-gray-600 mb-1">
                                <i class="fas fa-clock mr-1"></i>
                                <span>14:30 - 16:00</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-600 mb-1">
                                <i class="fas fa-map-marker-alt mr-1"></i>
                                <span>北京市海淀区中关村南大街5号</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-600 mb-1">
                                <i class="fas fa-user mr-1"></i>
                                <span>联系人: 李经理 (13987654321)</span>
                            </div>
                            <div class="text-sm text-gray-700 mt-2">
                                跟进IT基础设施更新项目，带上技术支持同事一起参与。
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end mt-3 gap-2">
                        <button class="bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full">
                            <i class="fas fa-map-marked-alt mr-1"></i> 导航
                        </button>
                        <button class="bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full">
                            <i class="fas fa-phone-alt mr-1"></i> 联系
                        </button>
                        <button class="bg-pink-100 text-pink-600 text-xs px-3 py-1 rounded-full">
                            <i class="fas fa-check-circle mr-1"></i> 完成拜访
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 未来拜访计划 -->
        <div class="mt-4">
            <div class="px-4 py-2 flex justify-between items-center">
                <div class="font-semibold">未来拜访计划</div>
                <div class="text-xs text-pink-600">查看全部</div>
            </div>
            
            <div class="bg-white">
                <!-- 未来计划1 -->
                <div class="border-b border-gray-100 p-4">
                    <div class="flex items-center justify-between mb-2">
                        <div class="font-medium">广州佳能打印拜访</div>
                        <div class="text-xs text-gray-500">06-20</div>
                    </div>
                    <div class="text-sm text-gray-600">
                        <i class="fas fa-clock mr-1"></i> 10:00 - 11:30
                    </div>
                </div>
                
                <!-- 未来计划2 -->
                <div class="border-b border-gray-100 p-4">
                    <div class="flex items-center justify-between mb-2">
                        <div class="font-medium">深圳鼎盛实业拜访</div>
                        <div class="text-xs text-gray-500">06-23</div>
                    </div>
                    <div class="text-sm text-gray-600">
                        <i class="fas fa-clock mr-1"></i> 14:00 - 15:30
                    </div>
                </div>
                
                <!-- 未来计划3 -->
                <div class="p-4">
                    <div class="flex items-center justify-between mb-2">
                        <div class="font-medium">杭州网络科技拜访</div>
                        <div class="text-xs text-gray-500">06-28</div>
                    </div>
                    <div class="text-sm text-gray-600">
                        <i class="fas fa-clock mr-1"></i> 15:00 - 16:30
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 浮动添加按钮 -->
        <div class="fixed bottom-24 right-5">
            <button class="w-14 h-14 rounded-full bg-pink-600 text-white flex items-center justify-center shadow-lg">
                <i class="fas fa-plus text-lg"></i>
            </button>
        </div>
    </div>
    
    <!-- 底部选项卡 -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="tab-icon fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-user-friends"></i>
            <span>客户</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-handshake"></i>
            <span>销售</span>
        </div>
        <div class="tab-item active">
            <i class="tab-icon fas fa-calendar-check"></i>
            <span>任务</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html> 