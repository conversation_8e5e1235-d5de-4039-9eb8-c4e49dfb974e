<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFA App - 个人中心</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/custom.css">
    <script>
        // 更新状态栏时间
        function updateTime() {
            const now = new Date();
            let hours = now.getHours();
            let minutes = now.getMinutes();
            hours = hours < 10 ? '0' + hours : hours;
            minutes = minutes < 10 ? '0' + minutes : minutes;
            document.getElementById('status-time').textContent = hours + ':' + minutes;
        }
        
        // 页面加载后初始化
        window.onload = function() {
            updateTime();
            setInterval(updateTime, 60000); // 每分钟更新一次
            
            // 初始化圆形进度条
            const ctx = document.getElementById('achievementChart').getContext('2d');
            const progress = 0.82; // 82% 完成率
            
            // 绘制背景圆
            ctx.beginPath();
            ctx.arc(60, 60, 50, 0, 2 * Math.PI);
            ctx.lineWidth = 10;
            ctx.strokeStyle = '#F0F0F0';
            ctx.stroke();
            
            // 绘制进度
            ctx.beginPath();
            ctx.arc(60, 60, 50, -Math.PI/2, (2 * progress - 0.5) * Math.PI);
            ctx.lineWidth = 10;
            ctx.strokeStyle = '#FE2C55';
            ctx.stroke();
            
            // 绘制中心文字
            ctx.font = 'bold 20px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = '#333';
            ctx.fillText(Math.round(progress * 100) + '%', 60, 60);
        }
    </script>
</head>
<body class="bg-[#F6F6F6]">
    <!-- iOS状态栏 -->
    <div class="status-bar">
        <div class="status-bar-time" id="status-time">9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <!-- 个人中心顶部信息 -->
    <div class="relative">
        <div class="bg-gradient-to-r from-pink-600 to-pink-500 h-48">
            <div class="nav-bar bg-transparent border-b-0">
                <div class="text-white font-semibold text-xl">个人中心</div>
                <div class="flex items-center gap-4">
                    <i class="fas fa-cog text-white"></i>
                </div>
            </div>
        </div>
        
        <div class="absolute top-16 left-0 right-0 px-4">
            <div class="bg-white rounded-xl p-4 shadow-sm">
                <div class="flex items-center mb-4">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像" class="w-16 h-16 rounded-full border-2 border-white shadow-md">
                    <div class="ml-4">
                        <div class="text-xl font-bold">张经理</div>
                        <div class="text-sm text-gray-600">高级销售经理 | 华东大区</div>
                        <div class="text-sm text-pink-600 mt-1">ID: 10086</div>
                    </div>
                </div>
                
                <div class="grid grid-cols-3 gap-3 border-t border-gray-100 pt-3">
                    <div class="text-center">
                        <div class="text-gray-500 text-xs mb-1">客户总数</div>
                        <div class="font-semibold">128</div>
                    </div>
                    <div class="text-center">
                        <div class="text-gray-500 text-xs mb-1">商机总数</div>
                        <div class="font-semibold text-pink-600">42</div>
                    </div>
                    <div class="text-center">
                        <div class="text-gray-500 text-xs mb-1">年度排名</div>
                        <div class="font-semibold">
                            <i class="fas fa-trophy text-yellow-400"></i> 1
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content pt-32 pb-20">
        <!-- 业绩完成度 -->
        <div class="card">
            <div class="flex justify-between items-center mb-3">
                <div class="font-semibold">本月目标完成度</div>
                <div class="text-xs text-gray-500">剩余12天</div>
            </div>
            <div class="flex items-center">
                <canvas id="achievementChart" width="120" height="120"></canvas>
                <div class="ml-4 flex-1">
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-600">已完成金额</div>
                        <div class="font-semibold text-pink-600">¥82万</div>
                    </div>
                    <div class="progress-bar mt-1">
                        <div class="progress-fill" style="width: 82%"></div>
                    </div>
                    <div class="flex justify-between items-center mt-1">
                        <div class="text-sm text-gray-600">目标金额</div>
                        <div class="font-semibold">¥100万</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 功能模块 -->
        <div class="grid grid-cols-4 gap-2 p-4">
            <div class="flex flex-col items-center justify-center">
                <div class="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center text-pink-600 mb-1">
                    <i class="fas fa-chart-line"></i>
                </div>
                <span class="text-xs text-gray-700">我的业绩</span>
            </div>
            
            <div class="flex flex-col items-center justify-center">
                <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mb-1">
                    <i class="fas fa-medal"></i>
                </div>
                <span class="text-xs text-gray-700">销售排行</span>
            </div>
            
            <div class="flex flex-col items-center justify-center">
                <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-600 mb-1">
                    <i class="fas fa-tasks"></i>
                </div>
                <span class="text-xs text-gray-700">待办事项</span>
            </div>
            
            <div class="flex flex-col items-center justify-center">
                <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mb-1">
                    <i class="fas fa-file-invoice-dollar"></i>
                </div>
                <span class="text-xs text-gray-700">佣金明细</span>
            </div>
        </div>
        
        <!-- 最近跟进记录 -->
        <div class="card">
            <div class="flex justify-between items-center mb-3">
                <div class="font-semibold">最近跟进记录</div>
                <div class="text-xs text-pink-600">查看全部</div>
            </div>
            
            <div class="space-y-3">
                <!-- 记录1 -->
                <div class="bg-gray-50 rounded-lg p-3">
                    <div class="flex items-center justify-between mb-1">
                        <div class="font-medium">上海万科集团</div>
                        <div class="text-xs text-gray-500">今天 10:30</div>
                    </div>
                    <div class="text-sm text-gray-700 mb-1">
                        客户拜访：与张总监沟通智能办公设备解决方案。
                    </div>
                    <div class="flex items-center text-xs">
                        <span class="text-gray-500">跟进方式:</span>
                        <span class="ml-1 text-gray-700">客户拜访</span>
                    </div>
                </div>
                
                <!-- 记录2 -->
                <div class="bg-gray-50 rounded-lg p-3">
                    <div class="flex items-center justify-between mb-1">
                        <div class="font-medium">北京朗科科技</div>
                        <div class="text-xs text-gray-500">昨天 14:30</div>
                    </div>
                    <div class="text-sm text-gray-700 mb-1">
                        电话沟通：确认IT基础设施更新项目进展，安排下周现场演示。
                    </div>
                    <div class="flex items-center text-xs">
                        <span class="text-gray-500">跟进方式:</span>
                        <span class="ml-1 text-gray-700">电话沟通</span>
                    </div>
                </div>
                
                <!-- 记录3 -->
                <div class="bg-gray-50 rounded-lg p-3">
                    <div class="flex items-center justify-between mb-1">
                        <div class="font-medium">广州佳能打印</div>
                        <div class="text-xs text-gray-500">3天前</div>
                    </div>
                    <div class="text-sm text-gray-700 mb-1">
                        邮件沟通：发送最终合同版本，客户已确认，等待签署。
                    </div>
                    <div class="flex items-center text-xs">
                        <span class="text-gray-500">跟进方式:</span>
                        <span class="ml-1 text-gray-700">邮件沟通</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 我的团队 -->
        <div class="card">
            <div class="flex justify-between items-center mb-3">
                <div class="font-semibold">我的团队</div>
                <div class="text-xs text-pink-600">查看全部</div>
            </div>
            
            <div class="space-y-3">
                <!-- 团队成员1 -->
                <div class="flex items-center">
                    <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="团队成员头像" class="w-10 h-10 rounded-full mr-3">
                    <div class="flex-1">
                        <div class="flex justify-between items-center">
                            <div class="font-medium">李销售</div>
                            <div class="text-xs text-gray-500">完成率: 86%</div>
                        </div>
                        <div class="flex justify-between items-center mt-1 text-xs text-gray-500">
                            <div>销售顾问</div>
                            <div>¥85.2万 / ¥100万</div>
                        </div>
                        <div class="progress-bar mt-1">
                            <div class="progress-fill" style="width: 86%"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 团队成员2 -->
                <div class="flex items-center">
                    <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="团队成员头像" class="w-10 h-10 rounded-full mr-3">
                    <div class="flex-1">
                        <div class="flex justify-between items-center">
                            <div class="font-medium">王销售</div>
                            <div class="text-xs text-gray-500">完成率: 78%</div>
                        </div>
                        <div class="flex justify-between items-center mt-1 text-xs text-gray-500">
                            <div>销售顾问</div>
                            <div>¥78.6万 / ¥100万</div>
                        </div>
                        <div class="progress-bar mt-1">
                            <div class="progress-fill" style="width: 78%"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 团队成员3 -->
                <div class="flex items-center">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="团队成员头像" class="w-10 h-10 rounded-full mr-3">
                    <div class="flex-1">
                        <div class="flex justify-between items-center">
                            <div class="font-medium">赵销售</div>
                            <div class="text-xs text-gray-500">完成率: 65%</div>
                        </div>
                        <div class="flex justify-between items-center mt-1 text-xs text-gray-500">
                            <div>销售顾问</div>
                            <div>¥64.8万 / ¥100万</div>
                        </div>
                        <div class="progress-bar mt-1">
                            <div class="progress-fill" style="width: 65%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部选项卡 -->
    <div class="tab-bar">
        <div class="tab-item">
            <i class="tab-icon fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-user-friends"></i>
            <span>客户</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-handshake"></i>
            <span>销售</span>
        </div>
        <div class="tab-item">
            <i class="tab-icon fas fa-calendar-check"></i>
            <span>任务</span>
        </div>
        <div class="tab-item active">
            <i class="tab-icon fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>
</body>
</html> 