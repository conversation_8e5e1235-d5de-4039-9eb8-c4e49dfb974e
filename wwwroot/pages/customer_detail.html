<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFA App - 客户详情</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/custom.css">
    <script>
        // 更新状态栏时间
        function updateTime() {
            const now = new Date();
            let hours = now.getHours();
            let minutes = now.getMinutes();
            hours = hours < 10 ? '0' + hours : hours;
            minutes = minutes < 10 ? '0' + minutes : minutes;
            document.getElementById('status-time').textContent = hours + ':' + minutes;
        }
        
        // 页面加载后初始化
        window.onload = function() {
            updateTime();
            setInterval(updateTime, 60000); // 每分钟更新一次
        }
    </script>
</head>
<body class="bg-[#F6F6F6]">
    <!-- iOS状态栏 -->
    <div class="status-bar">
        <div class="status-bar-time" id="status-time">9:41</div>
        <div class="status-bar-icons">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="flex items-center">
            <i class="fas fa-chevron-left mr-2 text-gray-500"></i>
            <span>客户详情</span>
        </div>
        <div class="flex items-center gap-4">
            <i class="fas fa-edit text-gray-500"></i>
            <i class="fas fa-ellipsis-h text-gray-500"></i>
        </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content pb-20">
        <!-- 客户基本信息 -->
        <div class="relative">
            <div class="bg-gradient-to-b from-pink-600 to-pink-500 h-32 w-full absolute top-0 left-0"></div>
            <div class="relative pt-4 px-4">
                <div class="bg-white rounded-xl p-4 shadow-sm">
                    <div class="flex items-start mb-4">
                        <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="客户头像" class="w-16 h-16 rounded-full mr-4 border-2 border-white shadow-md">
                        <div class="flex-1">
                            <div class="text-xl font-bold mb-1">上海万科集团</div>
                            <div class="text-sm text-gray-600 mb-1">房地产开发 | A级客户</div>
                            <div class="flex items-center gap-2">
                                <span class="tag tag-pink">重点客户</span>
                                <span class="tag tag-blue">地产行业</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-3 gap-3 border-t border-gray-100 pt-3">
                        <div class="text-center">
                            <div class="text-gray-500 text-xs mb-1">合作时长</div>
                            <div class="font-semibold">2年3个月</div>
                        </div>
                        <div class="text-center">
                            <div class="text-gray-500 text-xs mb-1">成交订单</div>
                            <div class="font-semibold text-pink-600">12单</div>
                        </div>
                        <div class="text-center">
                            <div class="text-gray-500 text-xs mb-1">累计金额</div>
                            <div class="font-semibold">¥1,230,000</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 联系人信息 -->
        <div class="card mt-4">
            <div class="font-semibold mb-3">联系人信息</div>
            <div class="flex items-center justify-between py-2 border-b border-gray-100">
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <div class="font-medium">张总监（主要联系人）</div>
                        <div class="text-xs text-gray-500">市场部 | 总监</div>
                    </div>
                </div>
                <div class="flex">
                    <a href="tel:18612345678" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 mr-2">
                        <i class="fas fa-phone-alt"></i>
                    </a>
                    <a href="sms:18612345678" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600">
                        <i class="fas fa-comment"></i>
                    </a>
                </div>
            </div>
            
            <div class="flex items-center justify-between py-2">
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <div class="font-medium">李先生</div>
                        <div class="text-xs text-gray-500">采购部 | 经理</div>
                    </div>
                </div>
                <div class="flex">
                    <a href="tel:18600000000" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 mr-2">
                        <i class="fas fa-phone-alt"></i>
                    </a>
                    <a href="sms:18600000000" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600">
                        <i class="fas fa-comment"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 快捷操作 -->
        <div class="grid grid-cols-4 gap-2 p-4">
            <button class="flex flex-col items-center justify-center bg-white rounded-lg p-3 shadow-sm">
                <div class="w-10 h-10 rounded-full bg-pink-100 flex items-center justify-center text-pink-600 mb-1">
                    <i class="fas fa-phone-alt"></i>
                </div>
                <span class="text-xs">拨打电话</span>
            </button>
            
            <button class="flex flex-col items-center justify-center bg-white rounded-lg p-3 shadow-sm">
                <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mb-1">
                    <i class="fas fa-file-signature"></i>
                </div>
                <span class="text-xs">跟进记录</span>
            </button>
            
            <button class="flex flex-col items-center justify-center bg-white rounded-lg p-3 shadow-sm">
                <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-600 mb-1">
                    <i class="fas fa-handshake"></i>
                </div>
                <span class="text-xs">添加商机</span>
            </button>
            
            <button class="flex flex-col items-center justify-center bg-white rounded-lg p-3 shadow-sm">
                <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 mb-1">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <span class="text-xs">安排拜访</span>
            </button>
        </div>
        
        <!-- 选项卡区域 -->
        <div class="bg-white mb-3">
            <div class="flex justify-between border-b border-gray-200">
                <button class="py-3 px-6 border-b-2 border-pink-600 text-pink-600 font-medium">跟进记录</button>
                <button class="py-3 px-6 text-gray-500">销售机会</button>
                <button class="py-3 px-6 text-gray-500">合同订单</button>
            </div>
        </div>
        
        <!-- 跟进记录时间线 -->
        <div class="timeline px-4">
            <!-- 记录1 -->
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <div class="timeline-content">
                    <div class="timeline-date">2023-06-15 09:30</div>
                    <div class="timeline-title">电话沟通</div>
                    <div class="text-sm text-gray-700">与张总监沟通了智能办公解决方案，客户对新一代智能打印设备表现出兴趣，计划近期拜访演示产品。</div>
                    <div class="flex items-center mt-2">
                        <span class="text-xs text-gray-500 mr-2">跟进人: 张经理</span>
                        <span class="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">有效沟通</span>
                    </div>
                </div>
            </div>
            
            <!-- 记录2 -->
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <div class="timeline-content">
                    <div class="timeline-date">2023-06-10 14:00</div>
                    <div class="timeline-title">客户拜访</div>
                    <div class="text-sm text-gray-700">上门拜访客户，了解到客户计划在Q3季度更新全公司的办公设备，初步预算约200万元，已提供相关解决方案资料。</div>
                    <div class="flex items-center mt-2">
                        <span class="text-xs text-gray-500 mr-2">跟进人: 张经理</span>
                        <span class="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">有效沟通</span>
                    </div>
                    <div class="flex flex-wrap gap-2 mt-2">
                        <div class="w-16 h-16 bg-gray-100 rounded-md overflow-hidden">
                            <img src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80" alt="拜访照片" class="w-full h-full object-cover">
                        </div>
                        <div class="w-16 h-16 bg-gray-100 rounded-md overflow-hidden">
                            <img src="https://images.unsplash.com/photo-1573497620053-ea5300f94f21?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80" alt="拜访照片" class="w-full h-full object-cover">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 记录3 -->
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <div class="timeline-content">
                    <div class="timeline-date">2023-05-25 10:15</div>
                    <div class="timeline-title">邮件沟通</div>
                    <div class="text-sm text-gray-700">发送了最新产品目录和价格表，客户回复会在内部讨论后给予反馈。</div>
                    <div class="flex items-center mt-2">
                        <span class="text-xs text-gray-500 mr-2">跟进人: 张经理</span>
                        <span class="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">信息传递</span>
                    </div>
                </div>
            </div>
            
            <button class="w-full text-center text-sm text-pink-600 py-2 mt-2">
                查看更多记录 <i class="fas fa-chevron-down ml-1"></i>
            </button>
        </div>
    </div>
    
    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-3 flex justify-around items-center">
        <button class="btn-primary flex-1 py-2">
            <i class="fas fa-plus mr-1"></i> 添加跟进
        </button>
    </div>
</body>
</html> 