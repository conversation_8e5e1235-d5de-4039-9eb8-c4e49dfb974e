/**
 * SFA App Settings Manager
 * 
 * 这个JS文件负责管理SFA应用的所有设置，包括：
 * - 语言（Language）
 * - 地区（Region）
 * - 主题（Theme）
 * - 设备类型（Device Type）
 * - 环境（Environment）
 */

class SFASettings {
    constructor() {
        this.defaultSettings = {
            language: '简体中文',
            region: '中国',
            theme: 'light',
            device: 'phone',
            environment: 'prd'
        };
        
        // 从本地存储加载设置
        this.settings = JSON.parse(localStorage.getItem('sfa_settings') || JSON.stringify(this.defaultSettings));
        
        // 应用初始主题
        this.applyTheme();
    }
    
    /**
     * 获取所有设置
     */
    getSettings() {
        return this.settings;
    }
    
    /**
     * 获取单个设置项
     */
    getSetting(key) {
        return this.settings[key];
    }
    
    /**
     * 更新设置
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        localStorage.setItem('sfa_settings', JSON.stringify(this.settings));
        
        // 应用主题设置
        this.applyTheme();
        
        return this.settings;
    }
    
    /**
     * 重置为默认设置
     */
    resetSettings() {
        this.settings = { ...this.defaultSettings };
        localStorage.setItem('sfa_settings', JSON.stringify(this.settings));
        
        // 应用主题设置
        this.applyTheme();
        
        return this.settings;
    }
    
    /**
     * 应用主题设置到HTML元素
     */
    applyTheme() {
        if (this.settings.theme === 'dark') {
            document.documentElement.classList.add('dark-mode');
        } else {
            document.documentElement.classList.remove('dark-mode');
        }
    }
    
    /**
     * 获取可用的语言选项
     */
    getLanguageOptions() {
        return [
            { value: '简体中文', label: '简体中文' },
            { value: '繁體中文', label: '繁體中文' },
            { value: 'English', label: 'English' },
            { value: '日本語', label: '日本語' },
            { value: '한국어', label: '한국어' }
        ];
    }
    
    /**
     * 获取可用的地区选项
     */
    getRegionOptions() {
        return [
            { value: '中国', label: '中国' },
            { value: '香港', label: '香港' },
            { value: '台湾', label: '台湾' },
            { value: '美国', label: '美国' },
            { value: '日本', label: '日本' },
            { value: '韩国', label: '韩国' }
        ];
    }
    
    /**
     * 获取可用的主题选项
     */
    getThemeOptions() {
        return [
            { value: 'light', label: '浅色' },
            { value: 'dark', label: '深色' }
        ];
    }
    
    /**
     * 获取可用的设备类型选项
     */
    getDeviceOptions() {
        return [
            { value: 'phone', label: '手机' },
            { value: 'tablet', label: '平板' }
        ];
    }
    
    /**
     * 获取可用的环境选项
     */
    getEnvironmentOptions() {
        return [
            { value: 'prd', label: '生产环境 (PRD)' },
            { value: 'uat', label: '测试环境 (UAT)' },
            { value: 'dev', label: '开发环境 (DEV)' }
        ];
    }
}

// 创建全局设置实例
const sfaSettings = new SFASettings();

// 导出设置实例
window.sfaSettings = sfaSettings; 