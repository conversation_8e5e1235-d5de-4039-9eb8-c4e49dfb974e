/* 设备框架样式 - 模拟iPhone 15 Pro */
.device-frame {
    width: 393px;
    height: 852px;
    border-radius: 55px;
    padding: 0;
    background: #ffffff;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    position: relative;
    overflow: hidden;
}

/* iOS状态栏样式 */
.status-bar {
    height: 44px;
    background: #000000;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    font-size: 14px;
    font-weight: 600;
    position: relative;
    z-index: 10;
}

.status-bar-time {
    font-weight: 600;
}

.status-bar-icons {
    display: flex;
    gap: 5px;
}

/* iOS导航栏样式 */
.nav-bar {
    height: 50px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    font-size: 17px;
    font-weight: 600;
    position: sticky;
    top: 44px;
    z-index: 9;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* 底部选项卡样式 */
.tab-bar {
    height: 83px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-around;
    align-items: center;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 20px; /* 为home indicator留出空间 */
}

.tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 10px;
    color: #8e8e93;
    width: 20%;
}

.tab-item.active {
    color: #FE2C55; /* 小红书粉色 */
}

.tab-icon {
    font-size: 22px;
    margin-bottom: 3px;
}

/* 内容区域样式 */
.content {
    height: calc(852px - 44px - 83px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding-bottom: 20px;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 12px;
    margin: 12px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 小红书风格按钮 */
.btn-primary {
    background-color: #FE2C55;
    color: white;
    border-radius: 20px;
    font-weight: 600;
    padding: 8px 24px;
    font-size: 15px;
    display: inline-block;
    text-align: center;
}

.btn-outline {
    border: 1px solid #FE2C55;
    color: #FE2C55;
    background-color: transparent;
    border-radius: 20px;
    font-weight: 600;
    padding: 8px 24px;
    font-size: 15px;
    display: inline-block;
    text-align: center;
}

/* 小红书风格颜色系统 */
:root {
    --primary-color: #FE2C55;
    --secondary-color: #FF4C79;
    --background-color: #F6F6F6;
    --card-background: #FFFFFF;
    --text-color-primary: #333333;
    --text-color-secondary: #666666;
    --text-color-tertiary: #999999;
    --border-color: #E0E0E0;
    --success-color: #07C160;
    --warning-color: #FF9500;
    --error-color: #FF3B30;
}

/* 标签样式 */
.tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 5px;
}

.tag-pink {
    background-color: rgba(254, 44, 85, 0.1);
    color: var(--primary-color);
}

.tag-blue {
    background-color: rgba(24, 118, 242, 0.1);
    color: #1876F2;
}

.tag-orange {
    background-color: rgba(255, 149, 0, 0.1);
    color: var(--warning-color);
}

/* 徽章计数 */
.badge {
    background-color: var(--primary-color);
    color: white;
    font-size: 10px;
    font-weight: 600;
    width: 18px;
    height: 18px;
    border-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: -5px;
    right: -5px;
}

/* 表单样式 */
.form-input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 15px;
    background-color: #F5F5F5;
    margin-bottom: 15px;
}

.form-label {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 6px;
    display: block;
    color: var(--text-color-secondary);
}

/* 客户列表项样式 */
.customer-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
}

.customer-avatar {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    margin-right: 15px;
    object-fit: cover;
}

.customer-info {
    flex: 1;
}

.customer-name {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 3px;
    color: var(--text-color-primary);
}

.customer-company {
    font-size: 14px;
    color: var(--text-color-secondary);
    margin-bottom: 3px;
}

.customer-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

/* 进度条样式 */
.progress-bar {
    height: 6px;
    background-color: #F0F0F0;
    border-radius: 3px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    border-radius: 3px;
    background-color: var(--primary-color);
}

/* 统计卡片 */
.stat-card {
    text-align: center;
    padding: 15px;
    border-radius: 12px;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    margin: 5px 0;
}

.stat-label {
    font-size: 12px;
    color: var(--text-color-tertiary);
}

/* 搜索栏 */
.search-bar {
    display: flex;
    align-items: center;
    background-color: #F0F0F0;
    border-radius: 8px;
    padding: 10px 15px;
    margin: 12px;
}

.search-icon {
    color: var(--text-color-tertiary);
    margin-right: 10px;
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: 15px;
    color: var(--text-color-primary);
}

.search-input:focus {
    outline: none;
}

/* 时间线样式 */
.timeline {
    position: relative;
    margin: 20px 0;
}

.timeline-item {
    position: relative;
    padding-left: 30px;
    margin-bottom: 20px;
}

.timeline-item:before {
    content: '';
    position: absolute;
    left: 10px;
    top: 0;
    height: 100%;
    width: 2px;
    background-color: #E0E0E0;
}

.timeline-dot {
    position: absolute;
    left: 5px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--primary-color);
    z-index: 1;
}

.timeline-content {
    background-color: white;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.timeline-date {
    font-size: 12px;
    color: var(--text-color-tertiary);
    margin-bottom: 5px;
}

.timeline-title {
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-color-primary);
} 