PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - FinApplet (2.48.9)
  - FinAppletExt (2.48.9):
    - FinApplet (= 2.48.9)
  - Flutter (1.0.0)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_native_splash (0.0.1):
    - Flutter
  - Google-Maps-iOS-Utils (5.0.0):
    - GoogleMaps (~> 8.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - home_widget (0.0.1):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - JCore (5.1.0)
  - JPush (5.7.0):
    - J<PERSON>ore (>= 4.8.0)
  - jpush_flutter (0.0.2):
    - Flutter
    - JCore (>= 5.1.0)
    - JPush (= 5.7.0)
  - local_auth_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - mop (0.1.1):
    - FinApplet (= 2.48.9)
    - FinAppletExt (= 2.48.9)
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - ReachabilitySwift (5.2.4)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - FinApplet (= 2.48.9)
  - FinAppletExt (= 2.48.9)
  - Flutter (from `Flutter`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - home_widget (from `.symlinks/plugins/home_widget/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - JPush (= 5.7.0)
  - jpush_flutter (from `.symlinks/plugins/jpush_flutter/ios`)
  - local_auth_darwin (from `.symlinks/plugins/local_auth_darwin/darwin`)
  - mop (from `.symlinks/plugins/mop/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git:
    - FinApplet
    - FinAppletExt
    - Google-Maps-iOS-Utils
    - GoogleMaps
    - JCore
    - JPush
    - ReachabilitySwift

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  home_widget:
    :path: ".symlinks/plugins/home_widget/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  jpush_flutter:
    :path: ".symlinks/plugins/jpush_flutter/ios"
  local_auth_darwin:
    :path: ".symlinks/plugins/local_auth_darwin/darwin"
  mop:
    :path: ".symlinks/plugins/mop/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  connectivity_plus: bf0076dd84a130856aa636df1c71ccaff908fa1d
  device_info_plus: 97af1d7e84681a90d0693e63169a5d50e0839a0d
  FinApplet: 439ad03d8d51cea0dd33821a61d8c8dd9bc5d003
  FinAppletExt: 05edc3ee7a69b529a257f429f72eec675a740147
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  flutter_native_splash: edf599c81f74d093a4daf8e17bd7a018854bc778
  Google-Maps-iOS-Utils: 66d6de12be1ce6d3742a54661e7a79cb317a9321
  google_maps_flutter_ios: e31555a04d1986ab130f2b9f24b6cdc861acc6d3
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  home_widget: 0434835a4c9a75704264feff6be17ea40e0f0d57
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  JCore: fcad36c11a5c70518322770db33408c23ff1ba7a
  JPush: 3d76286b63609a8790cdb0004ba19a3c0eea8828
  jpush_flutter: 4ed99b1d6e88011fbd99f1d595833d0f2f45d8d3
  local_auth_darwin: 66e40372f1c29f383a314c738c7446e2f7fdadc3
  mop: 75c71633a6687135cf5f6b4f956bc4b3485cf4c4
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: 42f8378448df76a335e3760b57d8038561096b32

COCOAPODS: 1.16.2
