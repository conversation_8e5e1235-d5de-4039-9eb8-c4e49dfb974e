import WidgetKit
import SwiftUI

// This struct provides the timeline for the SFA widget.
struct SfaWidgetProvider: TimelineProvider {
    
    // This method provides a default, placeholder view for the widget.
    // It is required by the TimelineProvider protocol.
    func placeholder(in context: Context) -> SimpleEntry {
        SimpleEntry(date: Date(), data: WidgetData.placeholder())
    }

    // This method provides a single, current snapshot for the widget gallery.
    // It is required by the TimelineProvider protocol.
    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        let entry = SimpleEntry(date: Date(), data: WidgetData.placeholder())
        completion(entry)
    }

    // This method provides the complete timeline for the widget's updates.
    // It is required by the TimelineProvider protocol.
    func getTimeline(in context: Context, completion: @escaping (Timeline<SimpleEntry>) -> ()) {
        // Attempt to read data from the shared App Group UserDefaults.
//        let userDefaults = UserDefaults(suiteName: "group.com.ebestmobile.hubsfa")
        let userDefaults = UserDefaults(suiteName: "group.com.ebestmobile.hubsfa1")
        let data = WidgetData.from(userDefaults: userDefaults)
        
        // Create a single timeline entry with the current data.
        let entry = SimpleEntry(date: Date(), data: data)
        
        // Schedule the next update for 15 minutes in the future.
        let nextUpdate = Calendar.current.date(byAdding: .minute, value: 15, to: Date())!
        
        // Create the timeline with the single entry and the refresh policy.
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        
        // Pass the timeline to the completion handler.
        completion(timeline)
    }
}

// This struct defines the data for a single entry in the widget's timeline.
// It must conform to the TimelineEntry protocol.
struct SimpleEntry: TimelineEntry {
    let date: Date
    let data: WidgetData
} 
