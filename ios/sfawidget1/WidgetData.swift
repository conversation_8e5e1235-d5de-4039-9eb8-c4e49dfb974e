 
import Foundation

struct WidgetData {
    let userName: String
    let updateTime: String
    
    let todayVisitPlanned: Int
    let todayVisitCompleted: Int
    let visitCompletionRate: Int
    
    let salesCompleted: String
    let salesCompletionRate: Int
    
    let todayOrders: Int
    let weeklyNewCustomers: Int
    let pendingTasks: Int

    static func from(userDefaults: UserDefaults?) -> WidgetData {
        // Safely access UserDefaults provided from the App Group
        guard let defaults = userDefaults else {
            return placeholder()
        }
        
        return WidgetData(
            userName: defaults.string(forKey: "userName") ?? "用户",
            updateTime: defaults.string(forKey: "updateTime") ?? "00:00",
            todayVisitPlanned: defaults.integer(forKey: "todayVisitPlanned"),
            todayVisitCompleted: defaults.integer(forKey: "todayVisitCompleted"),
            visitCompletionRate: defaults.integer(forKey: "visitCompletionRate"),
            salesCompleted: defaults.string(forKey: "salesCompleted") ?? "0万",
            salesCompletionRate: defaults.integer(forKey: "salesCompletionRate"),
            todayOrders: defaults.integer(forKey: "todayOrders"),
            weeklyNewCustomers: defaults.integer(forKey: "weeklyNewCustomers"),
            pendingTasks: defaults.integer(forKey: "pendingTasks")
        )
    }

    static func placeholder() -> WidgetData {
        // Provide sample data for previews and placeholders
        return WidgetData(
            userName: "用户",
            updateTime: "00:00",
            todayVisitPlanned: 10,
            todayVisitCompleted: 5,
            visitCompletionRate: 50,
            salesCompleted: "5万",
            salesCompletionRate: 50,
            todayOrders: 8,
            weeklyNewCustomers: 2,
            pendingTasks: 3
        )
    }
}
