import SwiftUI
import WidgetKit

struct SfaWidgetEntryView : View {
    var entry: SfaWidgetProvider.Entry

    var body: some View {
        ZStack {
            // 渐变背景，和 Android 一致
            // android:startColor="#1876F2"
            // android:endColor="#42A5F5"
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.259, green: 0.647, blue: 0.961),  // #1876F2
                    Color(red: 0.094, green: 0.463, blue: 0.949) // #42A5F5
                ]), 
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .edgesIgnoringSafeArea(.all)

            VStack(spacing: 16) {
                // 顶部 Hub SFA 标题 + 公文包icon
                HStack {
                    Image(systemName: "briefcase.fill")
                        .foregroundColor(.white)
                        .font(.system(size: 20))
                    Text("Hub SFA")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.white)
                    Spacer()
                     Text(entry.data.updateTime)
                        .font(.system(size: 14))
                        .foregroundColor(Color.white.opacity(0.8))
                }
                .padding(.horizontal)
                .padding(.top, 12)

                // 顶部栏
                HStack {
                    Text("\(entry.data.userName)，今日工作概览")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                    Spacer()
                   
                }
                .padding(.horizontal)

                // 卡片区
                HStack(spacing: 12) {
                    InfoCardView(
                        title: "拜访计划",
                        value: "\(entry.data.todayVisitCompleted)/\(entry.data.todayVisitPlanned)",
                        rate: "\(entry.data.visitCompletionRate)%",
                        progress: Double(entry.data.visitCompletionRate) / 100.0
                    )
                    InfoCardView(
                        title: "销售目标",
                        value: entry.data.salesCompleted,
                        rate: "\(entry.data.salesCompletionRate)%",
                        progress: Double(entry.data.salesCompletionRate) / 100.0
                    )
                }
                .padding(.horizontal)

                // 业务数据区
                HStack {
                    SmallInfoBoxView(title: "今日订单", value: "\(entry.data.todayOrders)")
                    Divider().background(Color.white.opacity(0.3))
                    SmallInfoBoxView(title: "新增客户", value: "\(entry.data.weeklyNewCustomers)")
                    Divider().background(Color.white.opacity(0.3))
                    SmallInfoBoxView(title: "待办任务", value: "\(entry.data.pendingTasks)")
                }
                .frame(height: 50)
                .padding(.horizontal)

                Spacer()

                // 底部导航
                BottomNavBar()
            }
            .padding(.bottom, 12)
        }
    }
}

struct HeaderView: View {
    let userName: String, updateTime: String
    var body: some View {
        HStack {
            Text("\(userName)，今日工作概览").font(.system(size: 16, weight: .bold)).foregroundColor(.white)
            Spacer()
            Text(updateTime).font(.system(size: 14)).foregroundColor(Color.white.opacity(0.8))
        }.padding(.horizontal).padding(.top)
    }
}

struct InfoCardView: View {
    let title: String, value: String, rate: String, progress: Double
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title).font(.system(size: 14)).foregroundColor(.white.opacity(0.8))
            HStack {
                Text(value).font(.system(size: 20, weight: .bold)).foregroundColor(.white)
                Spacer()
                Text(rate).font(.system(size: 16, weight: .semibold)).foregroundColor(.white)
            }
            ProgressView(value: progress).progressViewStyle(LinearProgressViewStyle(tint: .white))
        }.padding().background(Color.white.opacity(0.15)).cornerRadius(12)
    }
}

struct SmallInfoBoxView: View {
    let title: String, value: String
    var body: some View {
        VStack(spacing: 4) {
            Text(title).font(.system(size: 13)).foregroundColor(.white.opacity(0.8))
            Text(value).font(.system(size: 18, weight: .bold)).foregroundColor(.white)
        }.frame(maxWidth: .infinity)
    }
}

struct BottomNavBar: View {
    var body: some View {
        HStack {
            Spacer()
            NavItem(icon: "rectangle.grid.2x2", title: "数据看板", action: "dashboard")
            Spacer()
            NavItem(icon: "map", title: "路线计划", action: "route_plan")
            Spacer()
            NavItem(icon: "doc.text", title: "订单列表", action: "orders")
            Spacer()
        }.padding(.vertical, 8)
    }
}

struct NavItem: View {
    let icon: String, title: String, action: String
    var body: some View {
        Link(destination: URL(string: "hubsfa://widget_nav?action=\(action)")!) {
            VStack(spacing: 4) {
                Image(systemName: icon).font(.system(size: 20))
                Text(title).font(.system(size: 12))
            }.foregroundColor(.white)
        }
    }
}

struct SfaWidget_Previews: PreviewProvider {
    static var previews: some View {
        SfaWidgetEntryView(entry: SimpleEntry(date: Date(), data: WidgetData.placeholder()))
            .previewContext(WidgetPreviewContext(family: .systemLarge))
    }
} 
