import WidgetKit
import Swift<PERSON>

@main
struct SfaWidget: Widget {
    let kind: String = "SfaWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: SfaWidgetProvider()) { entry in
            SfaWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("SFA 业务组件")
        .description("在主屏幕上快速查看今日业务数据。")
        .supportedFamilies([.systemLarge])
    }
} 
